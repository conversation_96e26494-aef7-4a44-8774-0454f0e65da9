{"project_info": {"project_number": "782549266569", "project_id": "toggleinnovations", "storage_bucket": "toggleinnovations.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:782549266569:android:1a1014333e9314f973d658", "android_client_info": {"package_name": "com.example.academy_app"}}, "oauth_client": [{"client_id": "782549266569-79s4a3q1dofuvlgvkqq1b1qb3aj0sm8a.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDcOqpwp9oT25hosDEmYJAQKGFmyx3XrwA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "782549266569-79s4a3q1dofuvlgvkqq1b1qb3aj0sm8a.apps.googleusercontent.com", "client_type": 3}, {"client_id": "782549266569-80p7i9eppfclo203e835i6ck0br61c9p.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.temp.academy.app"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:782549266569:android:333194c13f7627c073d658", "android_client_info": {"package_name": "com.toggle.innovations"}}, "oauth_client": [{"client_id": "782549266569-79s4a3q1dofuvlgvkqq1b1qb3aj0sm8a.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDcOqpwp9oT25hosDEmYJAQKGFmyx3XrwA"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "782549266569-79s4a3q1dofuvlgvkqq1b1qb3aj0sm8a.apps.googleusercontent.com", "client_type": 3}, {"client_id": "782549266569-80p7i9eppfclo203e835i6ck0br61c9p.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.temp.academy.app"}}]}}}], "configuration_version": "1"}