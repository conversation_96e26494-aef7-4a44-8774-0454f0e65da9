{"originHash": "08eecfd2a7e184a5c0df38bc36a790e26deee4eff0a18fdd7796988052d5bae4", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "194a6706acbd25e4ef639bcaddea16e8758a3e27", "version": "1.2024011602.0"}}, {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire", "state": {"revision": "3dc6a42c7727c49bf26508e29b0a0b35f9c7e1ad", "version": "5.8.1"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "21fe1af9be463a359aaf8d96789ef73fc3760d09", "version": "11.0.1"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "c89ed571ae140f8eb1142735e6e23d7bb8c34cb2", "version": "1.7.5"}}, {"identity": "availablehapticfeedback", "kind": "remoteSourceControl", "location": "https://github.com/yonat/AvailableHapticFeedback", "state": {"revision": "9b2e0d85b532333bcb026b850052575852659d18", "version": "1.0.4"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "9118aca998dbe2ceac45d64b21a91c6376928df7", "version": "11.1.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "07a2f57d147d2bf368a0d2dcb5579ff082d9e44f", "version": "11.1.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "state": {"revision": "65fb3f1aa6ffbfdc79c4e22178a55cd91561f5e9", "version": "8.0.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "53156c7ec267db846e6b64c9f4c4e31ba4cf75eb", "version": "8.0.2"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "f56d8fc3162de9a498377c7b6cea43431f4f5083", "version": "1.65.1"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "2d12673670417654f08f5f90fdd62926dc3a2648", "version": "100.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "lottie-ios", "kind": "remoteSourceControl", "location": "https://github.com/airbnb/lottie-ios.git", "state": {"revision": "fe4c6fe3a0aa66cdeb51d549623c82ca9704b9a5", "version": "4.5.0"}}, {"identity": "multislider", "kind": "remoteSourceControl", "location": "https://github.com/yonat/MultiSlider.git", "state": {"revision": "ce55c0ec06e5fcde1dd8ad9791bd74ccfe4c4032", "version": "2.1.2"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "richtext", "kind": "remoteSourceControl", "location": "https://github.com/NuPlay/RichText.git", "state": {"revision": "8abbc59142523ac18f36fcf07414c2f85a2e75ae", "version": "2.4.1"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage.git", "state": {"revision": "f6afa0132961d593f07970d84e2d8b588c29ea04", "version": "5.19.1"}}, {"identity": "sdwebimageswiftui", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSwiftUI.git", "state": {"revision": "53573d6dd017e354c0e7d8f1c86b77ef1383c996", "version": "2.2.7"}}, {"identity": "securedefaults", "kind": "remoteSourceControl", "location": "https://github.com/vpeschenkov/SecureDefaults.git", "state": {"revision": "3f9d5d19f7401250791840bff857ed5f6b8c8ddf", "version": "1.2.2"}}, {"identity": "sweeterswift", "kind": "remoteSourceControl", "location": "https://github.com/yonat/SweeterSwift", "state": {"revision": "aff918fc4e8cba40c51b1df99766594e3acc4eb1", "version": "1.2.3"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "edb6ed4919f7756157fe02f2552b7e3850a538e5", "version": "1.28.1"}}, {"identity": "swiftui-image-viewer", "kind": "remoteSourceControl", "location": "https://github.com/fuzzzlove/swiftui-image-viewer.git", "state": {"revision": "ca77123064ae046eac2be01654ee756085c2b182", "version": "1.0.0"}}, {"identity": "swiftui-shimmer", "kind": "remoteSourceControl", "location": "https://github.com/markiv/SwiftUI-Shimmer.git", "state": {"revision": "0226e21f9bf355d40e07e5f5e1c33679d50e167f", "version": "1.5.1"}}, {"identity": "<PERSON><PERSON><PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/SwiftyJSON/SwiftyJSON.git", "state": {"revision": "af76cf3ef710b6ca5f8c05f3a31307d44a3c5828", "version": "5.0.2"}}, {"identity": "tocropviewcontroller", "kind": "remoteSourceControl", "location": "https://github.com/TimOliver/TOCropViewController.git", "state": {"revision": "a634cb7cdfd580006e79a6e74e64417fe9e9783b", "version": "2.7.4"}}], "version": 3}