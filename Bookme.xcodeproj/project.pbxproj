// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		00C10FA1E67623F5088681F9 /* CancelModifyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 66B8C2A31A7480EC8D957251 /* CancelModifyView.swift */; };
		01F6BD2791E89A48E4B41288 /* TransactionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22537CF02841E18BE0784A18 /* TransactionView.swift */; };
		0207F0FEE04772A23C9F1DFD /* FaqitemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C225A3C75CAB5D48C4DAC3D /* FaqitemCell.swift */; };
		032E34BFA02E32CC4B138E00 /* Artistitem3Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 957FBC9615CD14885AFD7BDE /* Artistitem3Cell.swift */; };
		0415DA7236FBE0A59DA9AC3C /* EReceptViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 35BD570585104FCE6D563F2B /* EReceptViewModel.swift */; };
		07D829807E13C6C293612640 /* TimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23CA2090F2F413108567ADD2 /* TimeView.swift */; };
		090E1B178583CD80B995DD38 /* AddreviewThreeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 529A95E8FE6F29260C311EAE /* AddreviewThreeViewModel.swift */; };
		0AD9EFE7B6017F20C8D80A32 /* AddreviewFour1View.swift in Sources */ = {isa = PBXBuildFile; fileRef = 13EEE21115A6A962A27B5C58 /* AddreviewFour1View.swift */; };
		0C4D2F22C87D67EBAA5FF9FE /* HomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C839DB3248481D896457DF7F /* HomeViewModel.swift */; };
		0E110BEE52D72B975F71A79A /* TimeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C6C43755E9B7F8B79D77D16C /* TimeViewModel.swift */; };
		0F458805D385474EE6665122 /* Appoinmentitem2Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4FAF4E03AD06A81A2E8A23B0 /* Appoinmentitem2Cell.swift */; };
		1047D3638CBD7560E5C3B584 /* RatepopupOneView.swift in Sources */ = {isa = PBXBuildFile; fileRef = EC307C65E9A49A79891B5130 /* RatepopupOneView.swift */; };
		1110C005B096E7091425B900 /* OnboardingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D7403D06676355688772804C /* OnboardingView.swift */; };
		116BD8B070E76CF500A762A2 /* ShopdetailsabouutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BD62E1311AD397DEA979D58 /* ShopdetailsabouutView.swift */; };
		124A56BED185E6CA3A3C38CE /* Shopdetails1ViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32413C28985DD80026544535 /* Shopdetails1ViewModel.swift */; };
		126AD1DB0B3AE8B9EC93E9D3 /* AppointmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 588BD6C454432C1260EAC106 /* AppointmentView.swift */; };
		132A95182FEBB004EB399E19 /* Rowrectangleten3Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D37AE73ADEC24E180B41C67 /* Rowrectangleten3Cell.swift */; };
		14A6D8B2B9B51F4B66EB7649 /* DateandtimeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DAE942D25AFB6435A52E112 /* DateandtimeViewModel.swift */; };
		159035D8E33D4DEC1AED3AE0 /* Shopdetails1View.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C3F388FA9E3BD0C82EB0DC4 /* Shopdetails1View.swift */; };
		176E2A9811A78DD27601E5EE /* ContactUsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = FBFB7EF0B9853E64FFF259A8 /* ContactUsViewModel.swift */; };
		1815DE542BC166D600FEEF15 /* FAQSampleModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1815DE532BC166D600FEEF15 /* FAQSampleModel.swift */; };
		1816CDC52BDBC2830058E6B9 /* TabViewDynamicHeight.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1816CDC42BDBC2830058E6B9 /* TabViewDynamicHeight.swift */; };
		1816CDCB2BDCFBBE0058E6B9 /* UserDefaults.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1816CDCA2BDCFBBE0058E6B9 /* UserDefaults.swift */; };
		1816CDCE2BDCFBF90058E6B9 /* SecureDefaults in Frameworks */ = {isa = PBXBuildFile; productRef = 1816CDCD2BDCFBF90058E6B9 /* SecureDefaults */; };
		1816CDD02BDCFC6D0058E6B9 /* UserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1816CDCF2BDCFC6D0058E6B9 /* UserModel.swift */; };
		181B7F672CB159D5006C17C6 /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 181B7F662CB159D5006C17C6 /* FirebaseMessaging */; };
		181B7F692CB286B2006C17C6 /* FirstResponderTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 181B7F682CB286AC006C17C6 /* FirstResponderTextField.swift */; };
		181E38492CA19BB000780921 /* CustomPlaceholder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 181E38482CA19BB000780921 /* CustomPlaceholder.swift */; };
		181E384D2CA1E99100780921 /* CustomDatePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 181E384C2CA1E98C00780921 /* CustomDatePicker.swift */; };
		182CE7022B86896900F2FEF8 /* WrappingHStack in Frameworks */ = {isa = PBXBuildFile; productRef = 182CE7012B86896900F2FEF8 /* WrappingHStack */; };
		182CE7052B87D6A400F2FEF8 /* MultiSlider in Frameworks */ = {isa = PBXBuildFile; productRef = 182CE7042B87D6A400F2FEF8 /* MultiSlider */; };
		182CE7072B87D6D900F2FEF8 /* SliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 182CE7062B87D6D900F2FEF8 /* SliderView.swift */; };
		182FCE5E2BDFA5F7000C2717 /* SignUpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 182FCE5D2BDFA5F7000C2717 /* SignUpView.swift */; };
		1847303B2C76411C006385D2 /* ForgotPasswordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1847303A2C76411C006385D2 /* ForgotPasswordViewModel.swift */; };
		1847303D2C764151006385D2 /* ForgotPasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1847303C2C764151006385D2 /* ForgotPasswordView.swift */; };
		1847BCF82CD3F45800020F9F /* finsh-loader.mov.lottie.json in Resources */ = {isa = PBXBuildFile; fileRef = 1847BCF72CD3F45800020F9F /* finsh-loader.mov.lottie.json */; };
		1847BCFB2CD3F49300020F9F /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = 1847BCFA2CD3F49300020F9F /* Lottie */; };
		1849D2282DAE90990053CB32 /* assetlinks.json in Resources */ = {isa = PBXBuildFile; fileRef = 1849D2272DAE908C0053CB32 /* assetlinks.json */; };
		18566E9B2BBAB0600050A98B /* TabExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18566E9A2BBAB0600050A98B /* TabExtension.swift */; };
		185886CA2BBC0B2100650239 /* AppointmentCompletedCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185886C92BBC0B2100650239 /* AppointmentCompletedCell.swift */; };
		185886CC2BBC0D3100650239 /* AppointmentCancelledCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185886CB2BBC0D3100650239 /* AppointmentCancelledCell.swift */; };
		185886CF2BBC117600650239 /* WriteReviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185886CE2BBC117600650239 /* WriteReviewView.swift */; };
		185886D12BBC11C700650239 /* WriteReviewViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185886D02BBC11C700650239 /* WriteReviewViewModel.swift */; };
		185886D32BBC11E700650239 /* WriteReviewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185886D22BBC11E700650239 /* WriteReviewModel.swift */; };
		185A35A12BE8BE720006CB98 /* AddAddressModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35A02BE8BE720006CB98 /* AddAddressModel.swift */; };
		185A35A62BE933900006CB98 /* SaloonMapModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35A52BE933900006CB98 /* SaloonMapModel.swift */; };
		185A35A82BEA1C3F0006CB98 /* CustomRichTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35A72BEA1C3F0006CB98 /* CustomRichTextView.swift */; };
		185A35AB2BEA1C8A0006CB98 /* RichText in Frameworks */ = {isa = PBXBuildFile; productRef = 185A35AA2BEA1C8A0006CB98 /* RichText */; };
		185A35AD2BEA21C90006CB98 /* RichTextPageModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35AC2BEA21C90006CB98 /* RichTextPageModel.swift */; };
		185A35AF2BEA29BC0006CB98 /* MyAccountModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35AE2BEA29BC0006CB98 /* MyAccountModel.swift */; };
		185A35B12BEA46170006CB98 /* ContactUsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35B02BEA46170006CB98 /* ContactUsModel.swift */; };
		185A35B32BEA514E0006CB98 /* TransactionHistoryModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35B22BEA514E0006CB98 /* TransactionHistoryModel.swift */; };
		185A35B52BEAA6BC0006CB98 /* ShopDetailsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35B42BEAA6BC0006CB98 /* ShopDetailsModel.swift */; };
		185A35B72BEB829B0006CB98 /* BookAppoinmentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185A35B62BEB829B0006CB98 /* BookAppoinmentModel.swift */; };
		185B68092CBD8EDF00D3DC0D /* MapLocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185B68082CBD8ED800D3DC0D /* MapLocationManager.swift */; };
		185B680B2CBD8FAF00D3DC0D /* LocationDeniedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185B680A2CBD8FAB00D3DC0D /* LocationDeniedView.swift */; };
		185B680D2CC1A82D00D3DC0D /* WrappingHStack.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185B680C2CC1A82800D3DC0D /* WrappingHStack.swift */; };
		185B680F2CC3935800D3DC0D /* TopStaffCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185B680E2CC3935800D3DC0D /* TopStaffCell.swift */; };
		185B68112CC3A68600D3DC0D /* CustomBottomNavigationButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185B68102CC3A68600D3DC0D /* CustomBottomNavigationButton.swift */; };
		185B68132CC3A6C700D3DC0D /* DateExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185B68122CC3A6C200D3DC0D /* DateExtensions.swift */; };
		185EA9562C220433008554FB /* BookingAppointmentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185EA9552C220433008554FB /* BookingAppointmentModel.swift */; };
		185EA9582C249255008554FB /* ExploreModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 185EA9572C249255008554FB /* ExploreModel.swift */; };
		186094102B6BDB9800B3C95F /* TabModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1860940F2B6BDB9800B3C95F /* TabModel.swift */; };
		186094122B6BE01900B3C95F /* DashboardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186094112B6BE01900B3C95F /* DashboardViewModel.swift */; };
		186094142B6BE15100B3C95F /* Route.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186094132B6BE15100B3C95F /* Route.swift */; };
		1865DB6A2C76478200C7C9D7 /* ForgotPasswordOtpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1865DB692C76478200C7C9D7 /* ForgotPasswordOtpView.swift */; };
		1865DB6C2C7648FA00C7C9D7 /* OtpFormFieldView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1865DB6B2C7648FA00C7C9D7 /* OtpFormFieldView.swift */; };
		1865DB6E2C76511A00C7C9D7 /* ChangePasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1865DB6D2C76511A00C7C9D7 /* ChangePasswordView.swift */; };
		1865DB702C76544400C7C9D7 /* ChangePasswordSuccessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1865DB6F2C76544400C7C9D7 /* ChangePasswordSuccessView.swift */; };
		1865DB732C79B8C700C7C9D7 /* Shimmer in Frameworks */ = {isa = PBXBuildFile; productRef = 1865DB722C79B8C700C7C9D7 /* Shimmer */; };
		1865DB752C79C7FF00C7C9D7 /* FilterShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1865DB742C79C7FF00C7C9D7 /* FilterShimmerView.swift */; };
		186B7AAD2CABF9310002AE40 /* ShopDetailsStaffView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186B7AAC2CABF9230002AE40 /* ShopDetailsStaffView.swift */; };
		186B7AAF2CABFA320002AE40 /* ShopDetailsStaffViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186B7AAE2CABFA300002AE40 /* ShopDetailsStaffViewModel.swift */; };
		186BC7842C99DEBE00DA5131 /* BookingSuccessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186BC7832C99DEBE00DA5131 /* BookingSuccessView.swift */; };
		186CFB4C2CC3D1BE00117D6C /* TestView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186CFB4B2CC3D1B900117D6C /* TestView.swift */; };
		186CFB4E2CC77F7B00117D6C /* VendorServicesCategoryModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186CFB4D2CC77F6D00117D6C /* VendorServicesCategoryModel.swift */; };
		186CFB502CC7C6D700117D6C /* CartItemCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186CFB4F2CC7C6D700117D6C /* CartItemCellView.swift */; };
		186CFB532CC8365C00117D6C /* DebounceHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186CFB522CC8365500117D6C /* DebounceHelper.swift */; };
		186DC6062BE7C18B00B24DC2 /* NotificationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6052BE7C18B00B24DC2 /* NotificationView.swift */; };
		186DC6082BE7C19A00B24DC2 /* NotificationModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6072BE7C19A00B24DC2 /* NotificationModel.swift */; };
		186DC60A2BE7C1A600B24DC2 /* NotificationViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6092BE7C1A600B24DC2 /* NotificationViewModel.swift */; };
		186DC60C2BE7F31900B24DC2 /* NotificationCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC60B2BE7F31900B24DC2 /* NotificationCellView.swift */; };
		186DC60E2BE7F3EA00B24DC2 /* ColorExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC60D2BE7F3EA00B24DC2 /* ColorExtension.swift */; };
		186DC6112BE8062000B24DC2 /* AddressListingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6102BE8062000B24DC2 /* AddressListingView.swift */; };
		186DC6132BE8076700B24DC2 /* AddressCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6122BE8076700B24DC2 /* AddressCellView.swift */; };
		186DC6152BE80ECC00B24DC2 /* AddressViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6142BE80ECC00B24DC2 /* AddressViewModel.swift */; };
		186DC6172BE8132600B24DC2 /* AddressModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6162BE8132600B24DC2 /* AddressModel.swift */; };
		186DC6192BE817A400B24DC2 /* AddAddressViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 186DC6182BE817A400B24DC2 /* AddAddressViewModel.swift */; };
		187028122DD8BDBE009C93E6 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 187028112DD8BDBE009C93E6 /* FirebaseAnalytics */; };
		187028142DD8BEC8009C93E6 /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = 187028132DD8BEC8009C93E6 /* FirebaseCrashlytics */; };
		187028162DD8C93B009C93E6 /* FirebaseAnalyticsOnDeviceConversion in Frameworks */ = {isa = PBXBuildFile; productRef = 187028152DD8C93B009C93E6 /* FirebaseAnalyticsOnDeviceConversion */; };
		187028182DD8C93B009C93E6 /* FirebaseAnalyticsWithoutAdIdSupport in Frameworks */ = {isa = PBXBuildFile; productRef = 187028172DD8C93B009C93E6 /* FirebaseAnalyticsWithoutAdIdSupport */; };
		187901F22C04532C00355246 /* MonthPopUpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187901F12C04532C00355246 /* MonthPopUpView.swift */; };
		187ACFBB610D147221EB1F53 /* ShopdetailsabouutViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A0FE3A43DAFE9B3390E7B93 /* ShopdetailsabouutViewModel.swift */; };
		187FEEC92CD55C5600EAC51C /* NotificationSettingsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEEC82CD55C4000EAC51C /* NotificationSettingsModel.swift */; };
		187FEECB2CD57B7900EAC51C /* RegistrationOtpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEECA2CD57B6E00EAC51C /* RegistrationOtpView.swift */; };
		187FEECD2CD60CF400EAC51C /* logo.animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 187FEECC2CD60CF400EAC51C /* logo.animation.json */; };
		187FEEDA2CE3C24700EAC51C /* CheckoutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEED92CE3C24000EAC51C /* CheckoutView.swift */; };
		187FEEDC2CE654AF00EAC51C /* WebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEEDB2CE654AB00EAC51C /* WebView.swift */; };
		187FEEDE2CE6611C00EAC51C /* PaymentGatewayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEEDD2CE6611300EAC51C /* PaymentGatewayView.swift */; };
		187FEEE02CE6625200EAC51C /* PaymentGatewayViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEEDF2CE6624800EAC51C /* PaymentGatewayViewModel.swift */; };
		187FEEE22CE66AD900EAC51C /* StringExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEEE12CE66AD400EAC51C /* StringExtension.swift */; };
		187FEEE42CF1E9AA00EAC51C /* PaymentCardModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEEE32CF1E9A300EAC51C /* PaymentCardModel.swift */; };
		187FEEE62CF1ED9500EAC51C /* PaymentCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 187FEEE52CF1ED8B00EAC51C /* PaymentCardViewModel.swift */; };
		188002E42BDE661B007375B0 /* ProfileModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188002E32BDE661B007375B0 /* ProfileModel.swift */; };
		188002E72BDEA27C007375B0 /* ImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188002E62BDEA27C007375B0 /* ImagePicker.swift */; };
		188221F12CF50C30005FB2C9 /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 188221F02CF50C30005FB2C9 /* Localizable.xcstrings */; };
		188222052CF70BB8005FB2C9 /* UPaymentCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188222042CF70BB5005FB2C9 /* UPaymentCardView.swift */; };
		188222072CF70BDB005FB2C9 /* UPaymentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188222062CF70BDA005FB2C9 /* UPaymentView.swift */; };
		188222092CF70C1A005FB2C9 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 188222082CF70C19005FB2C9 /* LoadingView.swift */; };
		1885E6192D0D7AB600030465 /* FirebaseDynamicLinks in Frameworks */ = {isa = PBXBuildFile; productRef = 1885E6182D0D7AB600030465 /* FirebaseDynamicLinks */; };
		189258D62D17214D00EB1D1D /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 189258D52D17214D00EB1D1D /* GoogleService-Info.plist */; };
		18931CFF2CA4253000E258DA /* DocumentPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18931CFE2CA4252E00E258DA /* DocumentPicker.swift */; };
		189A966C2DB66D5100BF6663 /* LottieView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1847BCFC2CD3F58500020F9F /* LottieView.swift */; };
		18AF03FB2BBC1BBA002D374F /* RatingBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AF03FA2BBC1BBA002D374F /* RatingBarView.swift */; };
		18AFC0C52C7CE35500DF0F7A /* SideMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AFC0C42C7CE35500DF0F7A /* SideMenu.swift */; };
		18AFC0C72C7F103F00DF0F7A /* ExploreShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AFC0C62C7F103F00DF0F7A /* ExploreShimmerView.swift */; };
		18AFC0C92C804FC300DF0F7A /* SaloonMapShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AFC0C82C804FC300DF0F7A /* SaloonMapShimmerView.swift */; };
		18AFC0CB2C823AA600DF0F7A /* ForgotPasswordModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AFC0CA2C823AA600DF0F7A /* ForgotPasswordModel.swift */; };
		18AFC0CD2C82FE0700DF0F7A /* NotificationShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AFC0CC2C82FE0700DF0F7A /* NotificationShimmerView.swift */; };
		18AFC0CF2C8300E700DF0F7A /* AddressListingShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AFC0CE2C8300E700DF0F7A /* AddressListingShimmerView.swift */; };
		18AFC0D12C831E6500DF0F7A /* AppointmentShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18AFC0D02C831E6500DF0F7A /* AppointmentShimmerView.swift */; };
		18B0871D2D099F0F001F6DE8 /* InfoPlist.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 18B0871C2D099F0F001F6DE8 /* InfoPlist.xcstrings */; };
		18B0871F2D09B775001F6DE8 /* LanguageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18B0871E2D09B770001F6DE8 /* LanguageView.swift */; };
		18BD0A2C2D7B18E800AB65A0 /* PasskeyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18BD0A2B2D7B18DE00AB65A0 /* PasskeyManager.swift */; };
		18C2A5BD2D67C2ED001AE292 /* Message.apns in Resources */ = {isa = PBXBuildFile; fileRef = 18C2A5BC2D67C2ED001AE292 /* Message.apns */; };
		18CB629F2B680F93009D60CE /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 18CB629E2B680F93009D60CE /* Alamofire */; };
		18CB62A22B681142009D60CE /* FSPagerView in Frameworks */ = {isa = PBXBuildFile; productRef = 18CB62A12B681142009D60CE /* FSPagerView */; };
		18CB62A92B690499009D60CE /* AppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CB62A82B690499009D60CE /* AppState.swift */; };
		18CB62AC2B690800009D60CE /* RouterManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CB62AB2B690800009D60CE /* RouterManager.swift */; };
		18CB62AF2B6908D3009D60CE /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CB62AE2B6908D3009D60CE /* MainView.swift */; };
		18CB62B12B698582009D60CE /* OnboardingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CB62B02B698582009D60CE /* OnboardingModel.swift */; };
		18CB62B32B699E00009D60CE /* CalendarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CB62B22B699E00009D60CE /* CalendarView.swift */; };
		18CB62BA2B6BD9B4009D60CE /* DashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CB62B92B6BD9B4009D60CE /* DashboardView.swift */; };
		18CD42742D88401C0095378F /* ShopDetailsShimmerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CD42732D88401A0095378F /* ShopDetailsShimmerView.swift */; };
		18CD42762D8848D30095378F /* Swiftui Copilot Guidelines .md in Resources */ = {isa = PBXBuildFile; fileRef = 18CD42752D8848D00095378F /* Swiftui Copilot Guidelines .md */; };
		18CD427C2D9125850095378F /* NetworkMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CD427B2D9125840095378F /* NetworkMonitor.swift */; };
		18CDD1C62CA04B1100D09B24 /* SavedModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18CDD1C52CA04B0D00D09B24 /* SavedModel.swift */; };
		18D44EA52D16F8B000A2C54A /* DeepLinkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18D44EA42D16F8A900A2C54A /* DeepLinkManager.swift */; };
		18D44EA72D16FCD100A2C54A /* apple-app-site-association.json in Resources */ = {isa = PBXBuildFile; fileRef = 18D44EA62D16FC9D00A2C54A /* apple-app-site-association.json */; };
		18DA00872B9F36DF00AAFC10 /* TestCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18DA00862B9F36DF00AAFC10 /* TestCell.swift */; };
		18E7791A2D358B8B000E18CB /* NotificationReminderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E779192D358B85000E18CB /* NotificationReminderView.swift */; };
		18E7791C2D358CE4000E18CB /* NotificationReminderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E7791B2D358CE3000E18CB /* NotificationReminderViewModel.swift */; };
		18E7791E2D35995B000E18CB /* ReminderNotificationModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E7791D2D359959000E18CB /* ReminderNotificationModel.swift */; };
		18E779202D35A7E6000E18CB /* ServiceForView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18E7791F2D35A7E0000E18CB /* ServiceForView.swift */; };
		18EED27E2C90687E00FFBCCA /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = 18EED27D2C90687E00FFBCCA /* FirebaseAuth */; };
		18EED2842C90899500FFBCCA /* AuthService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18EED2832C90899500FFBCCA /* AuthService.swift */; };
		18EED2872C9089FD00FFBCCA /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 18EED2862C9089FD00FFBCCA /* GoogleSignIn */; };
		18EED2892C9089FD00FFBCCA /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 18EED2882C9089FD00FFBCCA /* GoogleSignInSwift */; };
		18EED2982C959E3B00FFBCCA /* AddCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18EED2972C959E3B00FFBCCA /* AddCardView.swift */; };
		18EED29B2C959E9800FFBCCA /* AddCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18EED29A2C959E9800FFBCCA /* AddCardViewModel.swift */; };
		18EED29D2C95AE7300FFBCCA /* UserPaymentCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18EED29C2C95AE7300FFBCCA /* UserPaymentCardView.swift */; };
		18EED29F2C95AED200FFBCCA /* RowellipseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18EED29E2C95AED200FFBCCA /* RowellipseCell.swift */; };
		18EED2A12C95B87000FFBCCA /* PaymentCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18EED2A02C95B87000FFBCCA /* PaymentCardView.swift */; };
		18F022612B97209200A35420 /* MainScrollBody.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F022602B97209200A35420 /* MainScrollBody.swift */; };
		18F34D052BD6432500A9DDC2 /* TargetType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D042BD6432500A9DDC2 /* TargetType.swift */; };
		18F34D072BD644B900A9DDC2 /* BaseAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D062BD644B900A9DDC2 /* BaseAPI.swift */; };
		18F34D0A2BD644FF00A9DDC2 /* SwiftyJSON in Frameworks */ = {isa = PBXBuildFile; productRef = 18F34D092BD644FF00A9DDC2 /* SwiftyJSON */; };
		18F34D0C2BD6456A00A9DDC2 /* APIEndPoints.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D0B2BD6456A00A9DDC2 /* APIEndPoints.swift */; };
		18F34D0E2BD645F100A9DDC2 /* RepositoriesNetworking.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D0D2BD645F100A9DDC2 /* RepositoriesNetworking.swift */; };
		18F34D102BD6464500A9DDC2 /* RepositoriesAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D0F2BD6464500A9DDC2 /* RepositoriesAPI.swift */; };
		18F34D152BD646FF00A9DDC2 /* SuperModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D142BD646FF00A9DDC2 /* SuperModel.swift */; };
		18F34D172BD64D3E00A9DDC2 /* SuperViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D162BD64D3E00A9DDC2 /* SuperViewModel.swift */; };
		18F34D192BD6D8A200A9DDC2 /* HomeModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D182BD6D8A200A9DDC2 /* HomeModel.swift */; };
		18F34D1C2BD6E16400A9DDC2 /* NetworkImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D1B2BD6E16400A9DDC2 /* NetworkImageView.swift */; };
		18F34D272BD6E31C00A9DDC2 /* SwiftUIImageViewer in Frameworks */ = {isa = PBXBuildFile; productRef = 18F34D262BD6E31C00A9DDC2 /* SwiftUIImageViewer */; };
		18F34D2D2BD6E45900A9DDC2 /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = 18F34D2C2BD6E45900A9DDC2 /* SDWebImageSwiftUI */; };
		18F34D2F2BD6E62A00A9DDC2 /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D2E2BD6E62A00A9DDC2 /* LocationManager.swift */; };
		18F34D312BD7AF1A00A9DDC2 /* FiltersModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D302BD7AF1A00A9DDC2 /* FiltersModel.swift */; };
		18F34D332BD7C41F00A9DDC2 /* VendorDetailsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D322BD7C41F00A9DDC2 /* VendorDetailsModel.swift */; };
		18F34D3C2BDAA28400A9DDC2 /* SuperView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D3B2BDAA28400A9DDC2 /* SuperView.swift */; };
		18F34D3F2BDAA45300A9DDC2 /* ActivityLoaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D3E2BDAA45300A9DDC2 /* ActivityLoaderView.swift */; };
		18F34D422BDAA4C400A9DDC2 /* AlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F34D412BDAA4C400A9DDC2 /* AlertView.swift */; };
		18F735D22CCBD2D600F68F62 /* MainCategoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F735D12CCBD2D200F68F62 /* MainCategoryView.swift */; };
		18F736192CCF74BC00F68F62 /* UIApplicationExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F736182CCF74B500F68F62 /* UIApplicationExtension.swift */; };
		18F7361C2CCF819400F68F62 /* CropViewController in Frameworks */ = {isa = PBXBuildFile; productRef = 18F7361B2CCF819400F68F62 /* CropViewController */; };
		18F7361E2CCF819400F68F62 /* TOCropViewController in Frameworks */ = {isa = PBXBuildFile; productRef = 18F7361D2CCF819400F68F62 /* TOCropViewController */; };
		18F736202CCF959300F68F62 /* UserNotificationSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F7361F2CCF959300F68F62 /* UserNotificationSettingsView.swift */; };
		18F736222CD2425B00F68F62 /* AppStorageKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18F736212CD2425500F68F62 /* AppStorageKey.swift */; };
		1983ACFC4629B1241A636620 /* FilterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E139C3DA4506C3688F40F0EC /* FilterView.swift */; };
		19A9C77DDDE3E67513E3D5B9 /* FilterViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79F799C23A3762C0CB22EECA /* FilterViewModel.swift */; };
		1E64A774ECF735645D8E7B66 /* AddreviewViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E491B4BF03311FC15008DB0A /* AddreviewViewModel.swift */; };
		1FB4284313F4B2856F39B01D /* RatepopupTwoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8191F5E3981071AD9CC36383 /* RatepopupTwoViewModel.swift */; };
		210D8779D1C4DF5572C9C58C /* MontserratSemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C5060714CFF68337B5BF5E77 /* MontserratSemiBold.ttf */; };
		21DD9798F6B1ECA6843618BE /* TabsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC8B4A3848D007E74E32757C /* TabsView.swift */; };
		27B56B35101E9B8E8D1537E9 /* Ratepopup1ViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3273704861873543B7CC6E95 /* Ratepopup1ViewModel.swift */; };
		2B70C300B1694B9D70AAEBA8 /* Ratepopup1View.swift in Sources */ = {isa = PBXBuildFile; fileRef = 426FCD196113C9BBCB75FA78 /* Ratepopup1View.swift */; };
		2C0EE0C358BEC50F25C3FCAE /* PasswordManagerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4A716227FAD6DAFB836E3ED2 /* PasswordManagerViewModel.swift */; };
		2CED9F0E3879CB4D10917B3D /* Validation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 87A58454AF817ADDCEC787F3 /* Validation.swift */; };
		2D1962DD7D125090A07B0D0E /* RowkdcounterCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64C93F3D6A16F6DF47423661 /* RowkdcounterCell.swift */; };
		2D8D750A363486202CE37482 /* Rowhairextension1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6E3718B44F3912B8D6684EF0 /* Rowhairextension1Cell.swift */; };
		2EA45C1F6BC4AD1357AC8AA8 /* DateandtimeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0042B3BC9F651A6A77EDC34 /* DateandtimeView.swift */; };
		2FBC1041B6E3C46E5171978C /* SaloonMapViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2454E66944C46A9C50CE784D /* SaloonMapViewModel.swift */; };
		2FC40198A19702AD8FB56302 /* SaloonCardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 75434BAEDCA5814DF1AFCD60 /* SaloonCardCell.swift */; };
		306EC6088B14C238881EADE3 /* SavedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F31E1B4B13DCB0B4B14BAFA8 /* SavedView.swift */; };
		31B33BB528A4F575CF94C495 /* RatepopupTwoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4242827EFFAD75839AC966D /* RatepopupTwoView.swift */; };
		36254C7F306BA2E481E47416 /* Transactionare1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = D930045FAACE59AFF24C8A6A /* Transactionare1Cell.swift */; };
		3629C2EC4D952E82C35F18FA /* MapviewpopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5DD0ED80654454421B558145 /* MapviewpopupView.swift */; };
		36C5CA1D7CC14CB621CF4FB5 /* MapviewpopupViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D4EF4A8DAD78C9F31471940 /* MapviewpopupViewModel.swift */; };
		36F5DD0723D3C85A550E89CD /* PasswordManagerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4EE1D17EF96B8E758E8DA23 /* PasswordManagerView.swift */; };
		3A066F7A85F7E353A90A78EF /* AppoinmentcancelledViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 298EFA764F7A2363385A6291 /* AppoinmentcancelledViewModel.swift */; };
		3B1C9B6716D81781AC3ED0FB /* FSPagerViewSUIOrigin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C13A3833F24813541EBA28B /* FSPagerViewSUIOrigin.swift */; };
		41528372BDB9C4E72DAA411D /* RowbookingdateCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 71D21200D0C42BDF5C591D71 /* RowbookingdateCell.swift */; };
		43182E504FDC1CFB41B45ED6 /* AppoinmentcompleteView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6C092156BAFBB7773D6523A /* AppoinmentcompleteView.swift */; };
		43E617696CBB9DCC47E490F6 /* PaymentMethodView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74D199077DBA5E9CDE9AD755 /* PaymentMethodView.swift */; };
		44D12CFA90F7EFFE9393B9C7 /* PageIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = C8115FEAE118E245A91E09B1 /* PageIndicator.swift */; };
		49BFF16875A4A65573F8AFB1 /* TransactionareCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 352ABB27C143678799D9E7BE /* TransactionareCell.swift */; };
		49F533124CD42879758E4EB9 /* CircularProgress.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3BC67228A0A7B91B4AC455CE /* CircularProgress.swift */; };
		4A8276BAEBBDEFAD3F1C13EB /* AddreviewThreeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83C3AC99758D07D7198071BB /* AddreviewThreeView.swift */; };
		4C48B701B41E0DDEF1F43D40 /* Rowtime1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = AD2534C5A37A1FAFC7DB65F7 /* Rowtime1Cell.swift */; };
		4D83A38F0D3CDB679460A511 /* NunitoSemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 861ABF7F5C33AA8EE90A5C32 /* NunitoSemiBold.ttf */; };
		4ED125557E92791F9B12ABED /* RatepopupThreeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0DDE21E1972EEFEB807371B7 /* RatepopupThreeViewModel.swift */; };
		4F7F1C28A86E08BB8AD17D60 /* ProfileOneViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1A5B0968B143938EAC3AA12 /* ProfileOneViewModel.swift */; };
		50ABDBA88D796907E8E6CF53 /* WelcomeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFBBD4402E23412D642341DC /* WelcomeViewModel.swift */; };
		5298DF0A2850C4B0ECCD2613 /* Appoinmentitem1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F32EB3B384713AAFEE491E34 /* Appoinmentitem1Cell.swift */; };
		53A68D0B2B59F4277070669A /* BottomSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D0FCF3657EDE31E1726DEEA /* BottomSheetView.swift */; };
		54308AC9E019614EE86FB929 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1FA512405431EFEE8DC23D05 /* ProfileView.swift */; };
		558C7FAB6E469F3921D173A4 /* FSPageControlSUI.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD7FF113C67CFE1174201B01 /* FSPageControlSUI.swift */; };
		5616DFBEA02EA7205826E97D /* Rowkdcounter2Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 66FDDFAEA96593E2D197A0E5 /* Rowkdcounter2Cell.swift */; };
		586179A486CFC7CB7F7132A9 /* SavedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D5DCD7809B7E4F0692568A1 /* SavedViewModel.swift */; };
		595BFB266B1BFC108022CBA9 /* RatepopupOneContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 57560E13D7D6EFBE27F3B90A /* RatepopupOneContainerView.swift */; };
		5B1BA54538DACD9A566B3154 /* Radiogroupgreatexperienc1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 947A35BA09A8025FDC8C5F7F /* Radiogroupgreatexperienc1Cell.swift */; };
		5B469FF9DAC75BD2B482DC42 /* RadiogroupgreatexperiencCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 289BD4426287B5E8E9846FE3 /* RadiogroupgreatexperiencCell.swift */; };
		5C1E33A320A528192141730C /* CustomRichTextPageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9288160833C831BD3F6D29E1 /* CustomRichTextPageView.swift */; };
		5E2FCB0750D9D03933041531 /* ColorConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9159EB55DD88DDF0BDAE5965 /* ColorConstants.swift */; };
		63325934A4D0961AF6BCC90F /* TitleCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = D146F6AFB9B383710422FCF2 /* TitleCell.swift */; };
		64FC034D0CD430DB61CD8C42 /* RateiteCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 09F5200C1018D83C1EF669F8 /* RateiteCell.swift */; };
		68679CE88ACED714D2CE6615 /* ShopDetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4F9C244AB993E1460414A861 /* ShopDetailsViewModel.swift */; };
		68C4C027A49F504336839ACD /* CancelmodifyViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 647ECD8F27ADC8020AC6FDFA /* CancelmodifyViewModel.swift */; };
		699C73A5AC26A403D37F9890 /* HelpCenterViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F94DC0D97B7931A279BCFC2F /* HelpCenterViewModel.swift */; };
		6EAD04CDBFE4511BA7D375DB /* SignInView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 496E7AD02FFAF5A360FF5EC4 /* SignInView.swift */; };
		73F326246FEA8A53D638DD8C /* MapPinCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A865747F9E855DA5560AFF9B /* MapPinCell.swift */; };
		7502F468DC080EF3B16F990B /* RowhairextensionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A884E9014DE2C84B43773177 /* RowhairextensionCell.swift */; };
		75ECD3F08DF4A284FC85A777 /* FSPagerViewSUI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07DB9F877046866C0AE68342 /* FSPagerViewSUI.swift */; };
		796D769D4EC556D2FF247E48 /* SplashViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2940D7B41F325169FAFFB9E3 /* SplashViewModel.swift */; };
		79DDAA66F77B110B8DD16A20 /* AddreviewFourOneViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DBE4773819FD9699756A31AB /* AddreviewFourOneViewModel.swift */; };
		7F44DBA127422A5D00171570 /* ActivityLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F44DBA027422A5D00171570 /* ActivityLoader.swift */; };
		7F4D9C5690C2869AA238DECE /* CustomRichTextPageViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3EFEE4514E87D38A7E3C66C1 /* CustomRichTextPageViewModel.swift */; };
		8054DCD3CF06C9D47E75F5F7 /* RatepopupsucessmessegeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9CE21518A051F2B15CD4370 /* RatepopupsucessmessegeView.swift */; };
		82047A302ACF6F11D6303A5F /* AuthViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5E438456C48EC439D0228BD /* AuthViewModel.swift */; };
		833E7A68A14C4EAD3CF2DA19 /* ShopDetailsServiceViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 68C511F3F7A60145B3648116 /* ShopDetailsServiceViewModel.swift */; };
		834FC997934F3D17346387AF /* RowtimeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE8957B2CCE57BD2513F85C8 /* RowtimeCell.swift */; };
		836A6B5E3DD11543BCDF6848 /* Rowrectangleten5Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4609FE900042C518C05F3C40 /* Rowrectangleten5Cell.swift */; };
		83D1D9F878928A0985393DB1 /* Rowrectangleten4Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 014FF76E520B32B3BAD64480 /* Rowrectangleten4Cell.swift */; };
		84125FF1D4ED023A0F61D23F /* SaloonMapView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D49C671BFBEB657EE440676C /* SaloonMapView.swift */; };
		884F3E5127B0E65A00963FC4 /* RoundedCornersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 884F3E5027B0E65A00963FC4 /* RoundedCornersView.swift */; };
		8853052627718DC500B04E6F /* ViewExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8853052527718DC500B04E6F /* ViewExtensions.swift */; };
		8853052A27718E2D00B04E6F /* EncodableExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8853052927718E2D00B04E6F /* EncodableExtension.swift */; };
		8853052C2771949600B04E6F /* UINavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8853052B2771949600B04E6F /* UINavigationController.swift */; };
		88680D1C2775C601002E964F /* ViewportHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88680D1B2775C601002E964F /* ViewportHelper.swift */; };
		88713F7EDC52F7B4529FF12A /* EReceiptView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11B9806A061E66B95ECC5917 /* EReceiptView.swift */; };
		888CB47527686A000041116C /* APIExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 888CB47427686A000041116C /* APIExtensions.swift */; };
		88ED74EC272FFE6F0088E3EF /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 88ED74EB272FFE6F0088E3EF /* Assets.xcassets */; };
		88ED74EF272FFE6F0088E3EF /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 88ED74EE272FFE6F0088E3EF /* Preview Assets.xcassets */; };
		88ED7500272FFECA0088E3EF /* Utilities.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88ED74F7272FFECA0088E3EF /* Utilities.swift */; };
		88ED7501272FFECA0088E3EF /* AppConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88ED74F9272FFECA0088E3EF /* AppConstants.swift */; };
		88ED7504272FFECA0088E3EF /* BookmeApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88ED74FF272FFECA0088E3EF /* BookmeApp.swift */; };
		89A2D0763C65B5068535D176 /* SettingsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C26F875F6325FD0DEC3F67A0 /* SettingsViewModel.swift */; };
		8B8DD373F4144012D02DA4EE /* AppointmentViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A90EA4B5EB1AEEF0169C0807 /* AppointmentViewModel.swift */; };
		8C2A13387EE5A6F281CF10B4 /* InputCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F016C94BD970A43EA301E38D /* InputCell.swift */; };
		8C66FFAEF7E090B4400F7BF3 /* RatepopupThreeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4A311F0D5D554CF3B60C98CF /* RatepopupThreeView.swift */; };
		8CA9E7457E6431D886A8F908 /* Artistitem4Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E6874B26D5E27A3FF2BE2696 /* Artistitem4Cell.swift */; };
		8D2A36ECF704BA091E494744 /* AppointmentUpcomingCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A15C6364B50C21D6A4032870 /* AppointmentUpcomingCell.swift */; };
		8E54D7F251791AC34495E880 /* FAQView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32D1A4E9917D92CBF0DFE019 /* FAQView.swift */; };
		8FD97F2F06E662F4E51C7880 /* RatepopupOneContainerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 34B2596C4529369EF0FE3A77 /* RatepopupOneContainerViewModel.swift */; };
		8FEAD052ECCFC9B6FA6101B2 /* AddreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E0BD1764907B102FA4712E2B /* AddreviewView.swift */; };
		906B3B37ECF189D144E069A4 /* ShopdetailsReviewViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3E92C9457D694607B4DF33BE /* ShopdetailsReviewViewModel.swift */; };
		9133420B65690330F64696CF /* LogoutpopupViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1485A31D8CD0E3FFA8BE0CE0 /* LogoutpopupViewModel.swift */; };
		950B32A222CD48173D082E95 /* AddreviewTwoViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 66DD2D6F64816A94A6CA4A4B /* AddreviewTwoViewModel.swift */; };
		97D0E0878C7D3E84A3750AC2 /* ArtistitemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8FC4BBA85ED1E696AA1C3D0 /* ArtistitemCell.swift */; };
		98EF95080962DE2F9C75ED13 /* Rowbookingdate1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8306749AF68E8488C882DABE /* Rowbookingdate1Cell.swift */; };
		9A7F2979A10EBD8288FB250E /* PaymentmethodeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00BF26C32DEEE6E4C2D6EFD1 /* PaymentmethodeViewModel.swift */; };
		9A8A6154E279E77F4DFE87F0 /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 48484A1E2DD83F2045AB3591 /* HomeView.swift */; };
		9A9D4A55AD18BF379E015198 /* ShopdetailsReviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B10CAB0607035A11B25158E /* ShopdetailsReviewView.swift */; };
		9B86A8D3E1E73B0E9E532ED9 /* Artistitem1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6220319E02A09E3CBE9404A4 /* Artistitem1Cell.swift */; };
		9E5D416BED9E84A4C31289DC /* Appoinmentitem3Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BEAA60B52A9D28713EF5616C /* Appoinmentitem3Cell.swift */; };
		9F22231DE803B097647C2970 /* Rowrectangleten6Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24A559BF6E241E117A8841C3 /* Rowrectangleten6Cell.swift */; };
		9F6C4D86D1E16F66DB1489F2 /* FontScheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 12B4D2AAEACE4FC95787DE0A /* FontScheme.swift */; };
		9FAA0717444F72BA60E99CDA /* Artistitem2Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 655A285A1829F15AF921ADD9 /* Artistitem2Cell.swift */; };
		A017D07D16BBDB5DE2B3B5A6 /* Rowkdcounter1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 62C97208D78369ECE0A6A1D5 /* Rowkdcounter1Cell.swift */; };
		A0FB68FEA62396F462B81807 /* ColorpatternViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D89D6A25389051B2DC4FF59C /* ColorpatternViewModel.swift */; };
		A1A26475F6AAC296AB0D3308 /* AppoinmentcancelledView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 374F6AEF4D2F6023C3D7837B /* AppoinmentcancelledView.swift */; };
		A1F9666C5F318DF07449BDFF /* AddreviewFour1ViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = F94F5577B4AEA120D05D3897 /* AddreviewFour1ViewModel.swift */; };
		A2C0A6C738507ABB6FB3B136 /* AddreviewFour2View.swift in Sources */ = {isa = PBXBuildFile; fileRef = A4058DFA680D5FA211885DE9 /* AddreviewFour2View.swift */; };
		A2F36A5B070A05DE082F336F /* MapSaloonCardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 572FB3411AC234B38F39C3F4 /* MapSaloonCardCell.swift */; };
		A36A9F47075E5075E600524E /* HelpCenterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08C1933085A30A8E6C42B10E /* HelpCenterView.swift */; };
		A70E760D933EC24C4D688BA8 /* RowrectangletenCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1231BCFBD775C8B509086064 /* RowrectangletenCell.swift */; };
		A8971A7BE2045C78072665B6 /* ShopDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 13588453732E17D146626872 /* ShopDetailsView.swift */; };
		************************ /* MyAccountViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4A975E63C96AD56F3305B76 /* MyAccountViewModel.swift */; };
		B2606D7786DECBBBB62960F2 /* Rowbookingdate2Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99DA37BE9503DEA91149896E /* Rowbookingdate2Cell.swift */; };
		B5363FB492F3E0041D02A637 /* MapPin1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E50D6212B408FE71146FFE2C /* MapPin1Cell.swift */; };
		B593F9CC5D7330BA623FC257 /* BookAppointmentViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 205D3347D81E6578F6C5AE9F /* BookAppointmentViewModel.swift */; };
		B74A17608C377E375600B188 /* ShopdetailsPortfolioView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 62BA7B6791BF67FF3CA0E13D /* ShopdetailsPortfolioView.swift */; };
		B9D54E170D550A3430B2EAB4 /* OnboardingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E11CAE3E2E04A86129449125 /* OnboardingViewModel.swift */; };
		BAF5D10F666BB956E9AF0033 /* AddAddressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 45307F3F86639C0D068FD0E6 /* AddAddressView.swift */; };
		BBD886C7E7CB6726AA294B19 /* SelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA145ED0EA67F23FDCDA482F /* SelectionView.swift */; };
		BC9D9B3C48B9B10CC6AC680A /* RowserviceiteCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E149A0286C2B9631153A5D26 /* RowserviceiteCell.swift */; };
		BD13B2F2A5032D3CF1D84523 /* SplashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C339EB5AB4A55EB8D08502A /* SplashView.swift */; };
		BD89359BFC7DA8C6F95E2CC2 /* TransactionViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CFAAC225ACFE3625539728AD /* TransactionViewModel.swift */; };
		BD8AB948A908E98B9F95EA1A /* RatepopupsucessmessegeViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D25F7B4C4A2567F36745543A /* RatepopupsucessmessegeViewModel.swift */; };
		************************ /* ProfileViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C62A33A20C5B76E73CAE83C /* ProfileViewModel.swift */; };
		BFA10541194B92DDBF66B85A /* WelcomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8F6890ACB3B3A4AC43F7DC24 /* WelcomeView.swift */; };
		C0111C49BB6C5D1E396772DC /* MyAccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8F7E6C01F3CC99535E6103DD /* MyAccountView.swift */; };
		C40AB9F82754D79E006BC5D7 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = C40AB9F72754D79E006BC5D7 /* AppDelegate.swift */; };
		C5EAEC1D503F0B156EEB2341 /* LogoutPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A85F2790DB4A79E0391C138 /* LogoutPopupView.swift */; };
		C6D943573292DC0E9C311CA9 /* Rowdateitem1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95E07F9D3B57A9C5354157C8 /* Rowdateitem1Cell.swift */; };
		C71E1440D4BAE5D375687046 /* SelectionViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 77425580602C779AAC85478F /* SelectionViewModel.swift */; };
		CAF7AB57581AE54EEFB4116E /* ExploreView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE8C756033BEE2FD998E9F6E /* ExploreView.swift */; };
		CBAB2C092762A53A174661E4 /* RowdateitemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* RowdateitemCell.swift */; };
		************************ /* AppoinmentcompleteViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 322F2E7405166D6E2490F92A /* AppoinmentcompleteViewModel.swift */; };
		CD3C71184BD9E9B63F433714 /* ShopDetailsServiceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90833ED202B5286972827F0A /* ShopDetailsServiceView.swift */; };
		CD96B0AEFAAC19B3D01012ED /* Rowrectangleten1Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3CFA18DA8A9BD4BA837A989 /* Rowrectangleten1Cell.swift */; };
		CF869B57967BB927F1AD1A72 /* StringScheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18372F3BFEF76398F3421469 /* StringScheme.swift */; };
		D026A679491032BDB0F41742 /* LinearProgress.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3B4D29C125863C8CC3C33DCC /* LinearProgress.swift */; };
		D5A965CC8BF283643E6D921F /* FabButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55FEDEEE35BF9B562B7463F8 /* FabButton.swift */; };
		D7E6B96481AAC855955916BB /* PagerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 29F5E6CB01794F3F3E6A64FE /* PagerView.swift */; };
		D8A6C4DB8BCBDEDA4443390A /* ColumnellipseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 66655C096CF00E09213036B9 /* ColumnellipseCell.swift */; };
		************************ /* ShopdetailsPortfolioViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 473FFE5F3A8D6DB1B7B41A57 /* ShopdetailsPortfolioViewModel.swift */; };
		DD19CEC00D196DB9FB9DF541 /* ContactUsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ADF318545CEA9EDAEBA3CC85 /* ContactUsView.swift */; };
		DF988DCC80FEC1C5328676A2 /* MontserratMedium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C6FF231CA984A637D19A591D /* MontserratMedium.ttf */; };
		E0B7432D7FA4DF7B6A51D947 /* AddreviewTwoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CA8A83A45A761C40C2F0D3DB /* AddreviewTwoView.swift */; };
		E20448A0F6F5690DB4BDE75F /* NunitoExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2C11C85F2B3286672CABA5DC /* NunitoExtraBold.ttf */; };
		E33DD65390BFF0A8316CEE7B /* ColorpatternView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74704DBA2278E8AEF4DC95F1 /* ColorpatternView.swift */; };
		E4548BCE4B3FAA7EB93BEAB5 /* AddreviewFour2ViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5025C6465A0ADF52FFBEBF5 /* AddreviewFour2ViewModel.swift */; };
		E53A1FB1A878F1245726A862 /* AddreviewFourOneView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F68D294E846C074E584257D /* AddreviewFourOneView.swift */; };
		E55D0A2C56160AAEC0EC813B /* NunitoBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7CDFD3999F2E0D0C5EB05B0B /* NunitoBold.ttf */; };
		E7F6276FFFD38433D4F9E413 /* BookAppointmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4F73CF655EDEA203BDB2C336 /* BookAppointmentView.swift */; };
		E8059A0999F8BD8A2AB38901 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21B173C73368FC1CA644C333 /* SettingsView.swift */; };
		E9829341F59C89448D066B96 /* FAQViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 075FD6074F250A30049D0BD2 /* FAQViewModel.swift */; };
		F729FC0835F88CE2665A5D1F /* RadioGroupButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = E51DFFB761A4EC02534FAC07 /* RadioGroupButton.swift */; };
		F849F1E0D8486804CB70B257 /* NunitoMedium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 539F0E5993329539C7722019 /* NunitoMedium.ttf */; };
		FA9A51A0DB62CE7D45E98F3A /* NunitoRegular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B769DA8710932D978AE5C830 /* NunitoRegular.ttf */; };
		FAF5353167C17087434E39FE /* Stackrectangle4024oneCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = EE295BF81125B3366EF7A2F1 /* Stackrectangle4024oneCell.swift */; };
		FC1E09A290A75356B659D1F9 /* ExploreViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 765657A8F64D3E438ADDD186 /* ExploreViewModel.swift */; };
		FE6188DF61A5ABD8226FCAB7 /* RatepopupOneViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24EA8E84EDA16FD16C31EB07 /* RatepopupOneViewModel.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		00BF26C32DEEE6E4C2D6EFD1 /* PaymentmethodeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PaymentmethodeViewModel.swift; sourceTree = "<group>"; };
		014FF76E520B32B3BAD64480 /* Rowrectangleten4Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowrectangleten4Cell.swift; sourceTree = "<group>"; };
		075FD6074F250A30049D0BD2 /* FAQViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FAQViewModel.swift; sourceTree = "<group>"; };
		07DB9F877046866C0AE68342 /* FSPagerViewSUI.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FSPagerViewSUI.swift; sourceTree = "<group>"; };
		08C1933085A30A8E6C42B10E /* HelpCenterView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = HelpCenterView.swift; sourceTree = "<group>"; };
		09F5200C1018D83C1EF669F8 /* RateiteCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RateiteCell.swift; sourceTree = "<group>"; };
		0D0FCF3657EDE31E1726DEEA /* BottomSheetView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = BottomSheetView.swift; sourceTree = "<group>"; };
		0D4EF4A8DAD78C9F31471940 /* MapviewpopupViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MapviewpopupViewModel.swift; sourceTree = "<group>"; };
		0DDE21E1972EEFEB807371B7 /* RatepopupThreeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupThreeViewModel.swift; sourceTree = "<group>"; };
		11B9806A061E66B95ECC5917 /* EReceiptView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = EReceiptView.swift; sourceTree = "<group>"; };
		1231BCFBD775C8B509086064 /* RowrectangletenCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RowrectangletenCell.swift; sourceTree = "<group>"; };
		12B4D2AAEACE4FC95787DE0A /* FontScheme.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FontScheme.swift; sourceTree = "<group>"; };
		13588453732E17D146626872 /* ShopDetailsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopDetailsView.swift; sourceTree = "<group>"; };
		13EEE21115A6A962A27B5C58 /* AddreviewFour1View.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewFour1View.swift; sourceTree = "<group>"; };
		1485A31D8CD0E3FFA8BE0CE0 /* LogoutpopupViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = LogoutpopupViewModel.swift; sourceTree = "<group>"; };
		1815DE532BC166D600FEEF15 /* FAQSampleModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FAQSampleModel.swift; sourceTree = "<group>"; };
		1816CDC42BDBC2830058E6B9 /* TabViewDynamicHeight.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabViewDynamicHeight.swift; sourceTree = "<group>"; };
		1816CDCA2BDCFBBE0058E6B9 /* UserDefaults.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDefaults.swift; sourceTree = "<group>"; };
		1816CDCF2BDCFC6D0058E6B9 /* UserModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserModel.swift; sourceTree = "<group>"; };
		181B7F682CB286AC006C17C6 /* FirstResponderTextField.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirstResponderTextField.swift; sourceTree = "<group>"; };
		181E38482CA19BB000780921 /* CustomPlaceholder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomPlaceholder.swift; sourceTree = "<group>"; };
		181E384C2CA1E98C00780921 /* CustomDatePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomDatePicker.swift; sourceTree = "<group>"; };
		182CE7062B87D6D900F2FEF8 /* SliderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SliderView.swift; sourceTree = "<group>"; };
		182FCE5D2BDFA5F7000C2717 /* SignUpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignUpView.swift; sourceTree = "<group>"; };
		18372F3BFEF76398F3421469 /* StringScheme.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = StringScheme.swift; sourceTree = "<group>"; };
		1847303A2C76411C006385D2 /* ForgotPasswordViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgotPasswordViewModel.swift; sourceTree = "<group>"; };
		1847303C2C764151006385D2 /* ForgotPasswordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgotPasswordView.swift; sourceTree = "<group>"; };
		1847BCF72CD3F45800020F9F /* finsh-loader.mov.lottie.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = "finsh-loader.mov.lottie.json"; sourceTree = "<group>"; };
		1847BCFC2CD3F58500020F9F /* LottieView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LottieView.swift; sourceTree = "<group>"; };
		1849D2272DAE908C0053CB32 /* assetlinks.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = assetlinks.json; sourceTree = "<group>"; };
		18566E9A2BBAB0600050A98B /* TabExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabExtension.swift; sourceTree = "<group>"; };
		185886C92BBC0B2100650239 /* AppointmentCompletedCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppointmentCompletedCell.swift; sourceTree = "<group>"; };
		185886CB2BBC0D3100650239 /* AppointmentCancelledCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppointmentCancelledCell.swift; sourceTree = "<group>"; };
		185886CE2BBC117600650239 /* WriteReviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WriteReviewView.swift; sourceTree = "<group>"; };
		185886D02BBC11C700650239 /* WriteReviewViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WriteReviewViewModel.swift; sourceTree = "<group>"; };
		185886D22BBC11E700650239 /* WriteReviewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WriteReviewModel.swift; sourceTree = "<group>"; };
		185A35A02BE8BE720006CB98 /* AddAddressModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddAddressModel.swift; sourceTree = "<group>"; };
		185A35A52BE933900006CB98 /* SaloonMapModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SaloonMapModel.swift; sourceTree = "<group>"; };
		185A35A72BEA1C3F0006CB98 /* CustomRichTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomRichTextView.swift; sourceTree = "<group>"; };
		185A35AC2BEA21C90006CB98 /* RichTextPageModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RichTextPageModel.swift; sourceTree = "<group>"; };
		185A35AE2BEA29BC0006CB98 /* MyAccountModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyAccountModel.swift; sourceTree = "<group>"; };
		185A35B02BEA46170006CB98 /* ContactUsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactUsModel.swift; sourceTree = "<group>"; };
		185A35B22BEA514E0006CB98 /* TransactionHistoryModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionHistoryModel.swift; sourceTree = "<group>"; };
		185A35B42BEAA6BC0006CB98 /* ShopDetailsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShopDetailsModel.swift; sourceTree = "<group>"; };
		185A35B62BEB829B0006CB98 /* BookAppoinmentModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BookAppoinmentModel.swift; sourceTree = "<group>"; };
		185B68082CBD8ED800D3DC0D /* MapLocationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MapLocationManager.swift; sourceTree = "<group>"; };
		185B680A2CBD8FAB00D3DC0D /* LocationDeniedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocationDeniedView.swift; sourceTree = "<group>"; };
		185B680C2CC1A82800D3DC0D /* WrappingHStack.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WrappingHStack.swift; sourceTree = "<group>"; };
		185B680E2CC3935800D3DC0D /* TopStaffCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopStaffCell.swift; sourceTree = "<group>"; };
		185B68102CC3A68600D3DC0D /* CustomBottomNavigationButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomBottomNavigationButton.swift; sourceTree = "<group>"; };
		185B68122CC3A6C200D3DC0D /* DateExtensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateExtensions.swift; sourceTree = "<group>"; };
		185EA9552C220433008554FB /* BookingAppointmentModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BookingAppointmentModel.swift; sourceTree = "<group>"; };
		185EA9572C249255008554FB /* ExploreModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExploreModel.swift; sourceTree = "<group>"; };
		1860940F2B6BDB9800B3C95F /* TabModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabModel.swift; sourceTree = "<group>"; };
		186094112B6BE01900B3C95F /* DashboardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardViewModel.swift; sourceTree = "<group>"; };
		186094132B6BE15100B3C95F /* Route.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Route.swift; sourceTree = "<group>"; };
		1865DB692C76478200C7C9D7 /* ForgotPasswordOtpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgotPasswordOtpView.swift; sourceTree = "<group>"; };
		1865DB6B2C7648FA00C7C9D7 /* OtpFormFieldView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OtpFormFieldView.swift; sourceTree = "<group>"; };
		1865DB6D2C76511A00C7C9D7 /* ChangePasswordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangePasswordView.swift; sourceTree = "<group>"; };
		1865DB6F2C76544400C7C9D7 /* ChangePasswordSuccessView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangePasswordSuccessView.swift; sourceTree = "<group>"; };
		1865DB742C79C7FF00C7C9D7 /* FilterShimmerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterShimmerView.swift; sourceTree = "<group>"; };
		186B7AAC2CABF9230002AE40 /* ShopDetailsStaffView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShopDetailsStaffView.swift; sourceTree = "<group>"; };
		186B7AAE2CABFA300002AE40 /* ShopDetailsStaffViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShopDetailsStaffViewModel.swift; sourceTree = "<group>"; };
		186BC7832C99DEBE00DA5131 /* BookingSuccessView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BookingSuccessView.swift; sourceTree = "<group>"; };
		186CFB4B2CC3D1B900117D6C /* TestView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TestView.swift; sourceTree = "<group>"; };
		186CFB4D2CC77F6D00117D6C /* VendorServicesCategoryModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VendorServicesCategoryModel.swift; sourceTree = "<group>"; };
		186CFB4F2CC7C6D700117D6C /* CartItemCellView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CartItemCellView.swift; sourceTree = "<group>"; };
		186CFB522CC8365500117D6C /* DebounceHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebounceHelper.swift; sourceTree = "<group>"; };
		186DC6052BE7C18B00B24DC2 /* NotificationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationView.swift; sourceTree = "<group>"; };
		186DC6072BE7C19A00B24DC2 /* NotificationModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationModel.swift; sourceTree = "<group>"; };
		186DC6092BE7C1A600B24DC2 /* NotificationViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationViewModel.swift; sourceTree = "<group>"; };
		186DC60B2BE7F31900B24DC2 /* NotificationCellView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationCellView.swift; sourceTree = "<group>"; };
		186DC60D2BE7F3EA00B24DC2 /* ColorExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorExtension.swift; sourceTree = "<group>"; };
		186DC6102BE8062000B24DC2 /* AddressListingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressListingView.swift; sourceTree = "<group>"; };
		186DC6122BE8076700B24DC2 /* AddressCellView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressCellView.swift; sourceTree = "<group>"; };
		186DC6142BE80ECC00B24DC2 /* AddressViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressViewModel.swift; sourceTree = "<group>"; };
		186DC6162BE8132600B24DC2 /* AddressModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressModel.swift; sourceTree = "<group>"; };
		186DC6182BE817A400B24DC2 /* AddAddressViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddAddressViewModel.swift; sourceTree = "<group>"; };
		187901F12C04532C00355246 /* MonthPopUpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MonthPopUpView.swift; sourceTree = "<group>"; };
		187FEEC82CD55C4000EAC51C /* NotificationSettingsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationSettingsModel.swift; sourceTree = "<group>"; };
		187FEECA2CD57B6E00EAC51C /* RegistrationOtpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegistrationOtpView.swift; sourceTree = "<group>"; };
		187FEECC2CD60CF400EAC51C /* logo.animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = logo.animation.json; sourceTree = "<group>"; };
		187FEED92CE3C24000EAC51C /* CheckoutView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckoutView.swift; sourceTree = "<group>"; };
		187FEEDB2CE654AB00EAC51C /* WebView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebView.swift; sourceTree = "<group>"; };
		187FEEDD2CE6611300EAC51C /* PaymentGatewayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentGatewayView.swift; sourceTree = "<group>"; };
		187FEEDF2CE6624800EAC51C /* PaymentGatewayViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentGatewayViewModel.swift; sourceTree = "<group>"; };
		187FEEE12CE66AD400EAC51C /* StringExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringExtension.swift; sourceTree = "<group>"; };
		187FEEE32CF1E9A300EAC51C /* PaymentCardModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentCardModel.swift; sourceTree = "<group>"; };
		187FEEE52CF1ED8B00EAC51C /* PaymentCardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentCardViewModel.swift; sourceTree = "<group>"; };
		188002E32BDE661B007375B0 /* ProfileModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileModel.swift; sourceTree = "<group>"; };
		188002E62BDEA27C007375B0 /* ImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePicker.swift; sourceTree = "<group>"; };
		188221F02CF50C30005FB2C9 /* Localizable.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = Localizable.xcstrings; sourceTree = "<group>"; };
		188222042CF70BB5005FB2C9 /* UPaymentCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UPaymentCardView.swift; sourceTree = "<group>"; };
		188222062CF70BDA005FB2C9 /* UPaymentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UPaymentView.swift; sourceTree = "<group>"; };
		188222082CF70C19005FB2C9 /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		189258D52D17214D00EB1D1D /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		18931CFE2CA4252E00E258DA /* DocumentPicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentPicker.swift; sourceTree = "<group>"; };
		189784B72C90538D00BA70A0 /* Bookme.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Bookme.entitlements; sourceTree = "<group>"; };
		18AF03FA2BBC1BBA002D374F /* RatingBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RatingBarView.swift; sourceTree = "<group>"; };
		18AFC0C42C7CE35500DF0F7A /* SideMenu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideMenu.swift; sourceTree = "<group>"; };
		18AFC0C62C7F103F00DF0F7A /* ExploreShimmerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExploreShimmerView.swift; sourceTree = "<group>"; };
		18AFC0C82C804FC300DF0F7A /* SaloonMapShimmerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SaloonMapShimmerView.swift; sourceTree = "<group>"; };
		18AFC0CA2C823AA600DF0F7A /* ForgotPasswordModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgotPasswordModel.swift; sourceTree = "<group>"; };
		18AFC0CC2C82FE0700DF0F7A /* NotificationShimmerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationShimmerView.swift; sourceTree = "<group>"; };
		18AFC0CE2C8300E700DF0F7A /* AddressListingShimmerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressListingShimmerView.swift; sourceTree = "<group>"; };
		18AFC0D02C831E6500DF0F7A /* AppointmentShimmerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppointmentShimmerView.swift; sourceTree = "<group>"; };
		18B0871C2D099F0F001F6DE8 /* InfoPlist.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = InfoPlist.xcstrings; sourceTree = "<group>"; };
		18B0871E2D09B770001F6DE8 /* LanguageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageView.swift; sourceTree = "<group>"; };
		18BD0A2B2D7B18DE00AB65A0 /* PasskeyManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PasskeyManager.swift; sourceTree = "<group>"; };
		18C2A5BC2D67C2ED001AE292 /* Message.apns */ = {isa = PBXFileReference; lastKnownFileType = text; path = Message.apns; sourceTree = "<group>"; };
		18CB62A82B690499009D60CE /* AppState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppState.swift; sourceTree = "<group>"; };
		18CB62AB2B690800009D60CE /* RouterManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouterManager.swift; sourceTree = "<group>"; };
		18CB62AE2B6908D3009D60CE /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		18CB62B02B698582009D60CE /* OnboardingModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingModel.swift; sourceTree = "<group>"; };
		18CB62B22B699E00009D60CE /* CalendarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarView.swift; sourceTree = "<group>"; };
		18CB62B92B6BD9B4009D60CE /* DashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardView.swift; sourceTree = "<group>"; };
		18CD42732D88401A0095378F /* ShopDetailsShimmerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShopDetailsShimmerView.swift; sourceTree = "<group>"; };
		18CD42752D8848D00095378F /* Swiftui Copilot Guidelines .md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = "Swiftui Copilot Guidelines .md"; sourceTree = "<group>"; };
		18CD427B2D9125840095378F /* NetworkMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkMonitor.swift; sourceTree = "<group>"; };
		18CDD1C52CA04B0D00D09B24 /* SavedModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SavedModel.swift; sourceTree = "<group>"; };
		18D44EA42D16F8A900A2C54A /* DeepLinkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeepLinkManager.swift; sourceTree = "<group>"; };
		18D44EA62D16FC9D00A2C54A /* apple-app-site-association.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = "apple-app-site-association.json"; sourceTree = "<group>"; };
		18DA00862B9F36DF00AAFC10 /* TestCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TestCell.swift; sourceTree = "<group>"; };
		18E779192D358B85000E18CB /* NotificationReminderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationReminderView.swift; sourceTree = "<group>"; };
		18E7791B2D358CE3000E18CB /* NotificationReminderViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationReminderViewModel.swift; sourceTree = "<group>"; };
		18E7791D2D359959000E18CB /* ReminderNotificationModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReminderNotificationModel.swift; sourceTree = "<group>"; };
		18E7791F2D35A7E0000E18CB /* ServiceForView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServiceForView.swift; sourceTree = "<group>"; };
		18EED2832C90899500FFBCCA /* AuthService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthService.swift; sourceTree = "<group>"; };
		18EED2972C959E3B00FFBCCA /* AddCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddCardView.swift; sourceTree = "<group>"; };
		18EED29A2C959E9800FFBCCA /* AddCardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddCardViewModel.swift; sourceTree = "<group>"; };
		18EED29C2C95AE7300FFBCCA /* UserPaymentCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPaymentCardView.swift; sourceTree = "<group>"; };
		18EED29E2C95AED200FFBCCA /* RowellipseCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RowellipseCell.swift; sourceTree = "<group>"; };
		18EED2A02C95B87000FFBCCA /* PaymentCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentCardView.swift; sourceTree = "<group>"; };
		18F022602B97209200A35420 /* MainScrollBody.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainScrollBody.swift; sourceTree = "<group>"; };
		18F34D042BD6432500A9DDC2 /* TargetType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TargetType.swift; sourceTree = "<group>"; };
		18F34D062BD644B900A9DDC2 /* BaseAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseAPI.swift; sourceTree = "<group>"; };
		18F34D0B2BD6456A00A9DDC2 /* APIEndPoints.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIEndPoints.swift; sourceTree = "<group>"; };
		18F34D0D2BD645F100A9DDC2 /* RepositoriesNetworking.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepositoriesNetworking.swift; sourceTree = "<group>"; };
		18F34D0F2BD6464500A9DDC2 /* RepositoriesAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepositoriesAPI.swift; sourceTree = "<group>"; };
		18F34D142BD646FF00A9DDC2 /* SuperModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperModel.swift; sourceTree = "<group>"; };
		18F34D162BD64D3E00A9DDC2 /* SuperViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperViewModel.swift; sourceTree = "<group>"; };
		18F34D182BD6D8A200A9DDC2 /* HomeModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeModel.swift; sourceTree = "<group>"; };
		18F34D1B2BD6E16400A9DDC2 /* NetworkImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkImageView.swift; sourceTree = "<group>"; };
		18F34D2E2BD6E62A00A9DDC2 /* LocationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocationManager.swift; sourceTree = "<group>"; };
		18F34D302BD7AF1A00A9DDC2 /* FiltersModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FiltersModel.swift; sourceTree = "<group>"; };
		18F34D322BD7C41F00A9DDC2 /* VendorDetailsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VendorDetailsModel.swift; sourceTree = "<group>"; };
		18F34D3B2BDAA28400A9DDC2 /* SuperView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuperView.swift; sourceTree = "<group>"; };
		18F34D3E2BDAA45300A9DDC2 /* ActivityLoaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActivityLoaderView.swift; sourceTree = "<group>"; };
		18F34D412BDAA4C400A9DDC2 /* AlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertView.swift; sourceTree = "<group>"; };
		18F735D12CCBD2D200F68F62 /* MainCategoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainCategoryView.swift; sourceTree = "<group>"; };
		18F736182CCF74B500F68F62 /* UIApplicationExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIApplicationExtension.swift; sourceTree = "<group>"; };
		18F7361F2CCF959300F68F62 /* UserNotificationSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserNotificationSettingsView.swift; sourceTree = "<group>"; };
		18F736212CD2425500F68F62 /* AppStorageKey.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppStorageKey.swift; sourceTree = "<group>"; };
		1A0FE3A43DAFE9B3390E7B93 /* ShopdetailsabouutViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopdetailsabouutViewModel.swift; sourceTree = "<group>"; };
		1A85F2790DB4A79E0391C138 /* LogoutPopupView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = LogoutPopupView.swift; sourceTree = "<group>"; };
		1C339EB5AB4A55EB8D08502A /* SplashView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SplashView.swift; sourceTree = "<group>"; };
		1C3F388FA9E3BD0C82EB0DC4 /* Shopdetails1View.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Shopdetails1View.swift; sourceTree = "<group>"; };
		1C62A33A20C5B76E73CAE83C /* ProfileViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProfileViewModel.swift; sourceTree = "<group>"; };
		1D5DCD7809B7E4F0692568A1 /* SavedViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SavedViewModel.swift; sourceTree = "<group>"; };
		1FA512405431EFEE8DC23D05 /* ProfileView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		205D3347D81E6578F6C5AE9F /* BookAppointmentViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = BookAppointmentViewModel.swift; sourceTree = "<group>"; };
		21B173C73368FC1CA644C333 /* SettingsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		22537CF02841E18BE0784A18 /* TransactionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TransactionView.swift; sourceTree = "<group>"; };
		23CA2090F2F413108567ADD2 /* TimeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TimeView.swift; sourceTree = "<group>"; };
		2454E66944C46A9C50CE784D /* SaloonMapViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SaloonMapViewModel.swift; sourceTree = "<group>"; };
		24A559BF6E241E117A8841C3 /* Rowrectangleten6Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowrectangleten6Cell.swift; sourceTree = "<group>"; };
		24EA8E84EDA16FD16C31EB07 /* RatepopupOneViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupOneViewModel.swift; sourceTree = "<group>"; };
		289BD4426287B5E8E9846FE3 /* RadiogroupgreatexperiencCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RadiogroupgreatexperiencCell.swift; sourceTree = "<group>"; };
		2940D7B41F325169FAFFB9E3 /* SplashViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SplashViewModel.swift; sourceTree = "<group>"; };
		298EFA764F7A2363385A6291 /* AppoinmentcancelledViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AppoinmentcancelledViewModel.swift; sourceTree = "<group>"; };
		29F5E6CB01794F3F3E6A64FE /* PagerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PagerView.swift; sourceTree = "<group>"; };
		2C11C85F2B3286672CABA5DC /* NunitoExtraBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoExtraBold.ttf; sourceTree = "<group>"; };
		2F68D294E846C074E584257D /* AddreviewFourOneView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewFourOneView.swift; sourceTree = "<group>"; };
		322F2E7405166D6E2490F92A /* AppoinmentcompleteViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AppoinmentcompleteViewModel.swift; sourceTree = "<group>"; };
		32413C28985DD80026544535 /* Shopdetails1ViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Shopdetails1ViewModel.swift; sourceTree = "<group>"; };
		3273704861873543B7CC6E95 /* Ratepopup1ViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Ratepopup1ViewModel.swift; sourceTree = "<group>"; };
		32D1A4E9917D92CBF0DFE019 /* FAQView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FAQView.swift; sourceTree = "<group>"; };
		34B2596C4529369EF0FE3A77 /* RatepopupOneContainerViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupOneContainerViewModel.swift; sourceTree = "<group>"; };
		352ABB27C143678799D9E7BE /* TransactionareCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TransactionareCell.swift; sourceTree = "<group>"; };
		35BD570585104FCE6D563F2B /* EReceptViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = EReceptViewModel.swift; sourceTree = "<group>"; };
		374F6AEF4D2F6023C3D7837B /* AppoinmentcancelledView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AppoinmentcancelledView.swift; sourceTree = "<group>"; };
		3B4D29C125863C8CC3C33DCC /* LinearProgress.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = LinearProgress.swift; sourceTree = "<group>"; };
		3BC67228A0A7B91B4AC455CE /* CircularProgress.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CircularProgress.swift; sourceTree = "<group>"; };
		3C13A3833F24813541EBA28B /* FSPagerViewSUIOrigin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FSPagerViewSUIOrigin.swift; sourceTree = "<group>"; };
		3E92C9457D694607B4DF33BE /* ShopdetailsReviewViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopdetailsReviewViewModel.swift; sourceTree = "<group>"; };
		3EFEE4514E87D38A7E3C66C1 /* CustomRichTextPageViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CustomRichTextPageViewModel.swift; sourceTree = "<group>"; };
		426FCD196113C9BBCB75FA78 /* Ratepopup1View.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Ratepopup1View.swift; sourceTree = "<group>"; };
		45307F3F86639C0D068FD0E6 /* AddAddressView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddAddressView.swift; sourceTree = "<group>"; };
		4609FE900042C518C05F3C40 /* Rowrectangleten5Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowrectangleten5Cell.swift; sourceTree = "<group>"; };
		473FFE5F3A8D6DB1B7B41A57 /* ShopdetailsPortfolioViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopdetailsPortfolioViewModel.swift; sourceTree = "<group>"; };
		48484A1E2DD83F2045AB3591 /* HomeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		496E7AD02FFAF5A360FF5EC4 /* SignInView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SignInView.swift; sourceTree = "<group>"; };
		4A311F0D5D554CF3B60C98CF /* RatepopupThreeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupThreeView.swift; sourceTree = "<group>"; };
		4A716227FAD6DAFB836E3ED2 /* PasswordManagerViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PasswordManagerViewModel.swift; sourceTree = "<group>"; };
		4BD62E1311AD397DEA979D58 /* ShopdetailsabouutView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopdetailsabouutView.swift; sourceTree = "<group>"; };
		4F73CF655EDEA203BDB2C336 /* BookAppointmentView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = BookAppointmentView.swift; sourceTree = "<group>"; };
		4F9C244AB993E1460414A861 /* ShopDetailsViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopDetailsViewModel.swift; sourceTree = "<group>"; };
		4FAF4E03AD06A81A2E8A23B0 /* Appoinmentitem2Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Appoinmentitem2Cell.swift; sourceTree = "<group>"; };
		529A95E8FE6F29260C311EAE /* AddreviewThreeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewThreeViewModel.swift; sourceTree = "<group>"; };
		539F0E5993329539C7722019 /* NunitoMedium.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoMedium.ttf; sourceTree = "<group>"; };
		55FEDEEE35BF9B562B7463F8 /* FabButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FabButton.swift; sourceTree = "<group>"; };
		572FB3411AC234B38F39C3F4 /* MapSaloonCardCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MapSaloonCardCell.swift; sourceTree = "<group>"; };
		57560E13D7D6EFBE27F3B90A /* RatepopupOneContainerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupOneContainerView.swift; sourceTree = "<group>"; };
		588BD6C454432C1260EAC106 /* AppointmentView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AppointmentView.swift; sourceTree = "<group>"; };
		5DD0ED80654454421B558145 /* MapviewpopupView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MapviewpopupView.swift; sourceTree = "<group>"; };
		6220319E02A09E3CBE9404A4 /* Artistitem1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Artistitem1Cell.swift; sourceTree = "<group>"; };
		62BA7B6791BF67FF3CA0E13D /* ShopdetailsPortfolioView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopdetailsPortfolioView.swift; sourceTree = "<group>"; };
		62C97208D78369ECE0A6A1D5 /* Rowkdcounter1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowkdcounter1Cell.swift; sourceTree = "<group>"; };
		647ECD8F27ADC8020AC6FDFA /* CancelmodifyViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CancelmodifyViewModel.swift; sourceTree = "<group>"; };
		64C93F3D6A16F6DF47423661 /* RowkdcounterCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RowkdcounterCell.swift; sourceTree = "<group>"; };
		655A285A1829F15AF921ADD9 /* Artistitem2Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Artistitem2Cell.swift; sourceTree = "<group>"; };
		66655C096CF00E09213036B9 /* ColumnellipseCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ColumnellipseCell.swift; sourceTree = "<group>"; };
		66B8C2A31A7480EC8D957251 /* CancelModifyView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CancelModifyView.swift; sourceTree = "<group>"; };
		66DD2D6F64816A94A6CA4A4B /* AddreviewTwoViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewTwoViewModel.swift; sourceTree = "<group>"; };
		66FDDFAEA96593E2D197A0E5 /* Rowkdcounter2Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowkdcounter2Cell.swift; sourceTree = "<group>"; };
		68C511F3F7A60145B3648116 /* ShopDetailsServiceViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopDetailsServiceViewModel.swift; sourceTree = "<group>"; };
		6E3718B44F3912B8D6684EF0 /* Rowhairextension1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowhairextension1Cell.swift; sourceTree = "<group>"; };
		71D21200D0C42BDF5C591D71 /* RowbookingdateCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RowbookingdateCell.swift; sourceTree = "<group>"; };
		74704DBA2278E8AEF4DC95F1 /* ColorpatternView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ColorpatternView.swift; sourceTree = "<group>"; };
		74D199077DBA5E9CDE9AD755 /* PaymentMethodView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PaymentMethodView.swift; sourceTree = "<group>"; };
		75434BAEDCA5814DF1AFCD60 /* SaloonCardCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SaloonCardCell.swift; sourceTree = "<group>"; };
		765657A8F64D3E438ADDD186 /* ExploreViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ExploreViewModel.swift; sourceTree = "<group>"; };
		77425580602C779AAC85478F /* SelectionViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SelectionViewModel.swift; sourceTree = "<group>"; };
		79F799C23A3762C0CB22EECA /* FilterViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FilterViewModel.swift; sourceTree = "<group>"; };
		7CDFD3999F2E0D0C5EB05B0B /* NunitoBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoBold.ttf; sourceTree = "<group>"; };
		7D37AE73ADEC24E180B41C67 /* Rowrectangleten3Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowrectangleten3Cell.swift; sourceTree = "<group>"; };
		7F44DBA027422A5D00171570 /* ActivityLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActivityLoader.swift; sourceTree = "<group>"; };
		8191F5E3981071AD9CC36383 /* RatepopupTwoViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupTwoViewModel.swift; sourceTree = "<group>"; };
		8306749AF68E8488C882DABE /* Rowbookingdate1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowbookingdate1Cell.swift; sourceTree = "<group>"; };
		83C3AC99758D07D7198071BB /* AddreviewThreeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewThreeView.swift; sourceTree = "<group>"; };
		861ABF7F5C33AA8EE90A5C32 /* NunitoSemiBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoSemiBold.ttf; sourceTree = "<group>"; };
		87A58454AF817ADDCEC787F3 /* Validation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Validation.swift; sourceTree = "<group>"; };
		884F3E5027B0E65A00963FC4 /* RoundedCornersView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RoundedCornersView.swift; sourceTree = "<group>"; };
		8853052527718DC500B04E6F /* ViewExtensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewExtensions.swift; sourceTree = "<group>"; };
		8853052927718E2D00B04E6F /* EncodableExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EncodableExtension.swift; sourceTree = "<group>"; };
		8853052B2771949600B04E6F /* UINavigationController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UINavigationController.swift; sourceTree = "<group>"; };
		88680D1B2775C601002E964F /* ViewportHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewportHelper.swift; sourceTree = "<group>"; };
		888CB47427686A000041116C /* APIExtensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIExtensions.swift; sourceTree = "<group>"; };
		88ED74E4272FFE6E0088E3EF /* Bookme.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Bookme.app; sourceTree = BUILT_PRODUCTS_DIR; };
		88ED74EB272FFE6F0088E3EF /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		88ED74EE272FFE6F0088E3EF /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		88ED74F0272FFE6F0088E3EF /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		88ED74F7272FFECA0088E3EF /* Utilities.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Utilities.swift; sourceTree = "<group>"; };
		88ED74F9272FFECA0088E3EF /* AppConstants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppConstants.swift; sourceTree = "<group>"; };
		88ED74FF272FFECA0088E3EF /* BookmeApp.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BookmeApp.swift; sourceTree = "<group>"; };
		8B10CAB0607035A11B25158E /* ShopdetailsReviewView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopdetailsReviewView.swift; sourceTree = "<group>"; };
		8DAE942D25AFB6435A52E112 /* DateandtimeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DateandtimeViewModel.swift; sourceTree = "<group>"; };
		8F6890ACB3B3A4AC43F7DC24 /* WelcomeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = WelcomeView.swift; sourceTree = "<group>"; };
		8F7E6C01F3CC99535E6103DD /* MyAccountView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MyAccountView.swift; sourceTree = "<group>"; };
		90833ED202B5286972827F0A /* ShopDetailsServiceView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ShopDetailsServiceView.swift; sourceTree = "<group>"; };
		9159EB55DD88DDF0BDAE5965 /* ColorConstants.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ColorConstants.swift; sourceTree = "<group>"; };
		9288160833C831BD3F6D29E1 /* CustomRichTextPageView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CustomRichTextPageView.swift; sourceTree = "<group>"; };
		947A35BA09A8025FDC8C5F7F /* Radiogroupgreatexperienc1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Radiogroupgreatexperienc1Cell.swift; sourceTree = "<group>"; };
		957FBC9615CD14885AFD7BDE /* Artistitem3Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Artistitem3Cell.swift; sourceTree = "<group>"; };
		95E07F9D3B57A9C5354157C8 /* Rowdateitem1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowdateitem1Cell.swift; sourceTree = "<group>"; };
		99DA37BE9503DEA91149896E /* Rowbookingdate2Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowbookingdate2Cell.swift; sourceTree = "<group>"; };
		9C225A3C75CAB5D48C4DAC3D /* FaqitemCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FaqitemCell.swift; sourceTree = "<group>"; };
		A0042B3BC9F651A6A77EDC34 /* DateandtimeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = DateandtimeView.swift; sourceTree = "<group>"; };
		A15C6364B50C21D6A4032870 /* AppointmentUpcomingCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AppointmentUpcomingCell.swift; sourceTree = "<group>"; };
		A4058DFA680D5FA211885DE9 /* AddreviewFour2View.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewFour2View.swift; sourceTree = "<group>"; };
		A6C092156BAFBB7773D6523A /* AppoinmentcompleteView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AppoinmentcompleteView.swift; sourceTree = "<group>"; };
		A865747F9E855DA5560AFF9B /* MapPinCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MapPinCell.swift; sourceTree = "<group>"; };
		A884E9014DE2C84B43773177 /* RowhairextensionCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RowhairextensionCell.swift; sourceTree = "<group>"; };
		A90EA4B5EB1AEEF0169C0807 /* AppointmentViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AppointmentViewModel.swift; sourceTree = "<group>"; };
		AA145ED0EA67F23FDCDA482F /* SelectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SelectionView.swift; sourceTree = "<group>"; };
		AD2534C5A37A1FAFC7DB65F7 /* Rowtime1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowtime1Cell.swift; sourceTree = "<group>"; };
		ADF318545CEA9EDAEBA3CC85 /* ContactUsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ContactUsView.swift; sourceTree = "<group>"; };
		************************ /* RowdateitemCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RowdateitemCell.swift; sourceTree = "<group>"; };
		AE8C756033BEE2FD998E9F6E /* ExploreView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ExploreView.swift; sourceTree = "<group>"; };
		AFBBD4402E23412D642341DC /* WelcomeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = WelcomeViewModel.swift; sourceTree = "<group>"; };
		B769DA8710932D978AE5C830 /* NunitoRegular.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = NunitoRegular.ttf; sourceTree = "<group>"; };
		BEAA60B52A9D28713EF5616C /* Appoinmentitem3Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Appoinmentitem3Cell.swift; sourceTree = "<group>"; };
		C26F875F6325FD0DEC3F67A0 /* SettingsViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SettingsViewModel.swift; sourceTree = "<group>"; };
		C40AB9F72754D79E006BC5D7 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		C4A975E63C96AD56F3305B76 /* MyAccountViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MyAccountViewModel.swift; sourceTree = "<group>"; };
		C5025C6465A0ADF52FFBEBF5 /* AddreviewFour2ViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewFour2ViewModel.swift; sourceTree = "<group>"; };
		C5060714CFF68337B5BF5E77 /* MontserratSemiBold.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = MontserratSemiBold.ttf; sourceTree = "<group>"; };
		C5E438456C48EC439D0228BD /* AuthViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AuthViewModel.swift; sourceTree = "<group>"; };
		C6C43755E9B7F8B79D77D16C /* TimeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TimeViewModel.swift; sourceTree = "<group>"; };
		C6FF231CA984A637D19A591D /* MontserratMedium.ttf */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = file; path = MontserratMedium.ttf; sourceTree = "<group>"; };
		C8115FEAE118E245A91E09B1 /* PageIndicator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PageIndicator.swift; sourceTree = "<group>"; };
		C839DB3248481D896457DF7F /* HomeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = HomeViewModel.swift; sourceTree = "<group>"; };
		C9CE21518A051F2B15CD4370 /* RatepopupsucessmessegeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupsucessmessegeView.swift; sourceTree = "<group>"; };
		CA8A83A45A761C40C2F0D3DB /* AddreviewTwoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewTwoView.swift; sourceTree = "<group>"; };
		CE8957B2CCE57BD2513F85C8 /* RowtimeCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RowtimeCell.swift; sourceTree = "<group>"; };
		CFAAC225ACFE3625539728AD /* TransactionViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TransactionViewModel.swift; sourceTree = "<group>"; };
		D146F6AFB9B383710422FCF2 /* TitleCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TitleCell.swift; sourceTree = "<group>"; };
		D1A5B0968B143938EAC3AA12 /* ProfileOneViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ProfileOneViewModel.swift; sourceTree = "<group>"; };
		D25F7B4C4A2567F36745543A /* RatepopupsucessmessegeViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupsucessmessegeViewModel.swift; sourceTree = "<group>"; };
		D49C671BFBEB657EE440676C /* SaloonMapView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SaloonMapView.swift; sourceTree = "<group>"; };
		D4EE1D17EF96B8E758E8DA23 /* PasswordManagerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = PasswordManagerView.swift; sourceTree = "<group>"; };
		D7403D06676355688772804C /* OnboardingView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OnboardingView.swift; sourceTree = "<group>"; };
		D89D6A25389051B2DC4FF59C /* ColorpatternViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ColorpatternViewModel.swift; sourceTree = "<group>"; };
		D930045FAACE59AFF24C8A6A /* Transactionare1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Transactionare1Cell.swift; sourceTree = "<group>"; };
		DBE4773819FD9699756A31AB /* AddreviewFourOneViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewFourOneViewModel.swift; sourceTree = "<group>"; };
		DC8B4A3848D007E74E32757C /* TabsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = TabsView.swift; sourceTree = "<group>"; };
		E0BD1764907B102FA4712E2B /* AddreviewView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewView.swift; sourceTree = "<group>"; };
		E11CAE3E2E04A86129449125 /* OnboardingViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = OnboardingViewModel.swift; sourceTree = "<group>"; };
		E139C3DA4506C3688F40F0EC /* FilterView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FilterView.swift; sourceTree = "<group>"; };
		E149A0286C2B9631153A5D26 /* RowserviceiteCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RowserviceiteCell.swift; sourceTree = "<group>"; };
		E491B4BF03311FC15008DB0A /* AddreviewViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewViewModel.swift; sourceTree = "<group>"; };
		E50D6212B408FE71146FFE2C /* MapPin1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = MapPin1Cell.swift; sourceTree = "<group>"; };
		E51DFFB761A4EC02534FAC07 /* RadioGroupButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RadioGroupButton.swift; sourceTree = "<group>"; };
		E6874B26D5E27A3FF2BE2696 /* Artistitem4Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Artistitem4Cell.swift; sourceTree = "<group>"; };
		EC307C65E9A49A79891B5130 /* RatepopupOneView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupOneView.swift; sourceTree = "<group>"; };
		EE295BF81125B3366EF7A2F1 /* Stackrectangle4024oneCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Stackrectangle4024oneCell.swift; sourceTree = "<group>"; };
		F016C94BD970A43EA301E38D /* InputCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = InputCell.swift; sourceTree = "<group>"; };
		F31E1B4B13DCB0B4B14BAFA8 /* SavedView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SavedView.swift; sourceTree = "<group>"; };
		F32EB3B384713AAFEE491E34 /* Appoinmentitem1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Appoinmentitem1Cell.swift; sourceTree = "<group>"; };
		F3CFA18DA8A9BD4BA837A989 /* Rowrectangleten1Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Rowrectangleten1Cell.swift; sourceTree = "<group>"; };
		F4242827EFFAD75839AC966D /* RatepopupTwoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = RatepopupTwoView.swift; sourceTree = "<group>"; };
		F8FC4BBA85ED1E696AA1C3D0 /* ArtistitemCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ArtistitemCell.swift; sourceTree = "<group>"; };
		F94DC0D97B7931A279BCFC2F /* HelpCenterViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = HelpCenterViewModel.swift; sourceTree = "<group>"; };
		F94F5577B4AEA120D05D3897 /* AddreviewFour1ViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = AddreviewFour1ViewModel.swift; sourceTree = "<group>"; };
		FBFB7EF0B9853E64FFF259A8 /* ContactUsViewModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = ContactUsViewModel.swift; sourceTree = "<group>"; };
		FD7FF113C67CFE1174201B01 /* FSPageControlSUI.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = FSPageControlSUI.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		88ED74E1272FFE6E0088E3EF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				18F34D2D2BD6E45900A9DDC2 /* SDWebImageSwiftUI in Frameworks */,
				18CB62A22B681142009D60CE /* FSPagerView in Frameworks */,
				18EED2872C9089FD00FFBCCA /* GoogleSignIn in Frameworks */,
				185A35AB2BEA1C8A0006CB98 /* RichText in Frameworks */,
				18F7361C2CCF819400F68F62 /* CropViewController in Frameworks */,
				18F34D272BD6E31C00A9DDC2 /* SwiftUIImageViewer in Frameworks */,
				182CE7052B87D6A400F2FEF8 /* MultiSlider in Frameworks */,
				1816CDCE2BDCFBF90058E6B9 /* SecureDefaults in Frameworks */,
				18F7361E2CCF819400F68F62 /* TOCropViewController in Frameworks */,
				18EED27E2C90687E00FFBCCA /* FirebaseAuth in Frameworks */,
				187028162DD8C93B009C93E6 /* FirebaseAnalyticsOnDeviceConversion in Frameworks */,
				1847BCFB2CD3F49300020F9F /* Lottie in Frameworks */,
				1885E6192D0D7AB600030465 /* FirebaseDynamicLinks in Frameworks */,
				18EED2892C9089FD00FFBCCA /* GoogleSignInSwift in Frameworks */,
				182CE7022B86896900F2FEF8 /* WrappingHStack in Frameworks */,
				187028182DD8C93B009C93E6 /* FirebaseAnalyticsWithoutAdIdSupport in Frameworks */,
				18F34D0A2BD644FF00A9DDC2 /* SwiftyJSON in Frameworks */,
				181B7F672CB159D5006C17C6 /* FirebaseMessaging in Frameworks */,
				18CB629F2B680F93009D60CE /* Alamofire in Frameworks */,
				187028142DD8BEC8009C93E6 /* FirebaseCrashlytics in Frameworks */,
				187028122DD8BDBE009C93E6 /* FirebaseAnalytics in Frameworks */,
				1865DB732C79B8C700C7C9D7 /* Shimmer in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		03052F244A64FDAB4E9BB494 /* HelpCentercontact1View */ = {
			isa = PBXGroup;
			children = (
				08C1933085A30A8E6C42B10E /* HelpCenterView.swift */,
				F94DC0D97B7931A279BCFC2F /* HelpCenterViewModel.swift */,
			);
			path = HelpCentercontact1View;
			sourceTree = "<group>";
		};
		034A157C01138A19B1665039 /* BookappoinmentView */ = {
			isa = PBXGroup;
			children = (
				188222062CF70BDA005FB2C9 /* UPaymentView.swift */,
				188222042CF70BB5005FB2C9 /* UPaymentCardView.swift */,
				187FEEDF2CE6624800EAC51C /* PaymentGatewayViewModel.swift */,
				187FEEDD2CE6611300EAC51C /* PaymentGatewayView.swift */,
				187FEEDB2CE654AB00EAC51C /* WebView.swift */,
				185A35B62BEB829B0006CB98 /* BookAppoinmentModel.swift */,
				4F73CF655EDEA203BDB2C336 /* BookAppointmentView.swift */,
				205D3347D81E6578F6C5AE9F /* BookAppointmentViewModel.swift */,
				187FEED92CE3C24000EAC51C /* CheckoutView.swift */,
				9F635686A44F47EA553A27E5 /* Cell */,
			);
			path = BookappoinmentView;
			sourceTree = "<group>";
		};
		089C70EFC6D3EFF435A7CEB7 /* Cell */ = {
			isa = PBXGroup;
			children = (
				F32EB3B384713AAFEE491E34 /* Appoinmentitem1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		08AF5232186F8617C3485BA8 /* RatepopupThreeView */ = {
			isa = PBXGroup;
			children = (
				4A311F0D5D554CF3B60C98CF /* RatepopupThreeView.swift */,
				4170B57B6FA90FD20F178970 /* Cell */,
				0DDE21E1972EEFEB807371B7 /* RatepopupThreeViewModel.swift */,
			);
			path = RatepopupThreeView;
			sourceTree = "<group>";
		};
		0D8BBC2E825663BF50A998B5 /* PasswordManagerView */ = {
			isa = PBXGroup;
			children = (
				D4EE1D17EF96B8E758E8DA23 /* PasswordManagerView.swift */,
				4A716227FAD6DAFB836E3ED2 /* PasswordManagerViewModel.swift */,
			);
			path = PasswordManagerView;
			sourceTree = "<group>";
		};
		0FE60057EB377E8C95519B6A /* Cell */ = {
			isa = PBXGroup;
			children = (
				947A35BA09A8025FDC8C5F7F /* Radiogroupgreatexperienc1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		11899230C0FC05DE6F770522 /* TabAndPager */ = {
			isa = PBXGroup;
			children = (
				DC8B4A3848D007E74E32757C /* TabsView.swift */,
				29F5E6CB01794F3F3E6A64FE /* PagerView.swift */,
			);
			path = TabAndPager;
			sourceTree = "<group>";
		};
		1816CDC92BDCFBB20058E6B9 /* UserDefaults */ = {
			isa = PBXGroup;
			children = (
				1816CDCA2BDCFBBE0058E6B9 /* UserDefaults.swift */,
			);
			path = UserDefaults;
			sourceTree = "<group>";
		};
		181E38442CA19B8300780921 /* Placeholders */ = {
			isa = PBXGroup;
			children = (
				181E38452CA19B8D00780921 /* View */,
			);
			path = Placeholders;
			sourceTree = "<group>";
		};
		181E38452CA19B8D00780921 /* View */ = {
			isa = PBXGroup;
			children = (
				181E38482CA19BB000780921 /* CustomPlaceholder.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		184730362C76408A006385D2 /* ForgotPassword */ = {
			isa = PBXGroup;
			children = (
				184730372C7640C4006385D2 /* Model */,
				184730382C7640D7006385D2 /* View */,
				184730392C7640E1006385D2 /* ViewModel */,
			);
			path = ForgotPassword;
			sourceTree = "<group>";
		};
		184730372C7640C4006385D2 /* Model */ = {
			isa = PBXGroup;
			children = (
				18AFC0CA2C823AA600DF0F7A /* ForgotPasswordModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		184730382C7640D7006385D2 /* View */ = {
			isa = PBXGroup;
			children = (
				1847303C2C764151006385D2 /* ForgotPasswordView.swift */,
				1865DB692C76478200C7C9D7 /* ForgotPasswordOtpView.swift */,
				1865DB6B2C7648FA00C7C9D7 /* OtpFormFieldView.swift */,
				1865DB6D2C76511A00C7C9D7 /* ChangePasswordView.swift */,
				1865DB6F2C76544400C7C9D7 /* ChangePasswordSuccessView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		184730392C7640E1006385D2 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				1847303A2C76411C006385D2 /* ForgotPasswordViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		1847BCF62CD3F44600020F9F /* Animations */ = {
			isa = PBXGroup;
			children = (
				187FEECC2CD60CF400EAC51C /* logo.animation.json */,
				1847BCF72CD3F45800020F9F /* finsh-loader.mov.lottie.json */,
			);
			path = Animations;
			sourceTree = "<group>";
		};
		185886CD2BBC115A00650239 /* WriteReview */ = {
			isa = PBXGroup;
			children = (
				185886D22BBC11E700650239 /* WriteReviewModel.swift */,
				185886CE2BBC117600650239 /* WriteReviewView.swift */,
				185886D02BBC11C700650239 /* WriteReviewViewModel.swift */,
			);
			path = WriteReview;
			sourceTree = "<group>";
		};
		185A359F2BE8BE430006CB98 /* AddAddress */ = {
			isa = PBXGroup;
			children = (
				185A35A02BE8BE720006CB98 /* AddAddressModel.swift */,
				45307F3F86639C0D068FD0E6 /* AddAddressView.swift */,
				186DC6182BE817A400B24DC2 /* AddAddressViewModel.swift */,
			);
			path = AddAddress;
			sourceTree = "<group>";
		};
		185A35A22BE8BE8B0006CB98 /* View */ = {
			isa = PBXGroup;
			children = (
				186DC6102BE8062000B24DC2 /* AddressListingView.swift */,
				186DC6122BE8076700B24DC2 /* AddressCellView.swift */,
				18AFC0CE2C8300E700DF0F7A /* AddressListingShimmerView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		185A35A32BE8BE940006CB98 /* Model */ = {
			isa = PBXGroup;
			children = (
				186DC6162BE8132600B24DC2 /* AddressModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		185A35A42BE8BEAB0006CB98 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				186DC6142BE80ECC00B24DC2 /* AddressViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		186CFB512CC8364C00117D6C /* Helper Class */ = {
			isa = PBXGroup;
			children = (
				18F736212CD2425500F68F62 /* AppStorageKey.swift */,
				186CFB522CC8365500117D6C /* DebounceHelper.swift */,
			);
			path = "Helper Class";
			sourceTree = "<group>";
		};
		186DC6042BE7C16D00B24DC2 /* Notifications */ = {
			isa = PBXGroup;
			children = (
				186DC6072BE7C19A00B24DC2 /* NotificationModel.swift */,
				186DC6052BE7C18B00B24DC2 /* NotificationView.swift */,
				186DC60B2BE7F31900B24DC2 /* NotificationCellView.swift */,
				186DC6092BE7C1A600B24DC2 /* NotificationViewModel.swift */,
				18AFC0CC2C82FE0700DF0F7A /* NotificationShimmerView.swift */,
			);
			path = Notifications;
			sourceTree = "<group>";
		};
		186DC60F2BE8060200B24DC2 /* AddressListing */ = {
			isa = PBXGroup;
			children = (
				185A35A32BE8BE940006CB98 /* Model */,
				185A35A22BE8BE8B0006CB98 /* View */,
				185A35A42BE8BEAB0006CB98 /* ViewModel */,
			);
			path = AddressListing;
			sourceTree = "<group>";
		};
		188002E52BDEA26D007375B0 /* ImagePicker */ = {
			isa = PBXGroup;
			children = (
				188002E62BDEA27C007375B0 /* ImagePicker.swift */,
			);
			path = ImagePicker;
			sourceTree = "<group>";
		};
		18AF03F92BBC1BAB002D374F /* RatingBar */ = {
			isa = PBXGroup;
			children = (
				18AF03FA2BBC1BBA002D374F /* RatingBarView.swift */,
			);
			path = RatingBar;
			sourceTree = "<group>";
		};
		18AFC0C32C7CE32A00DF0F7A /* Menu */ = {
			isa = PBXGroup;
			children = (
				18AFC0C42C7CE35500DF0F7A /* SideMenu.swift */,
			);
			path = Menu;
			sourceTree = "<group>";
		};
		18CB62A62B690482009D60CE /* Core */ = {
			isa = PBXGroup;
			children = (
				18F34D012BD642DC00A9DDC2 /* APIServices */,
				18CB62A72B69048D009D60CE /* AppState */,
				18CB62AA2B6907E6009D60CE /* Route */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		18CB62A72B69048D009D60CE /* AppState */ = {
			isa = PBXGroup;
			children = (
				18D44EA42D16F8A900A2C54A /* DeepLinkManager.swift */,
				18CB62A82B690499009D60CE /* AppState.swift */,
				1816CDCF2BDCFC6D0058E6B9 /* UserModel.swift */,
			);
			path = AppState;
			sourceTree = "<group>";
		};
		18CB62AA2B6907E6009D60CE /* Route */ = {
			isa = PBXGroup;
			children = (
				18CB62AB2B690800009D60CE /* RouterManager.swift */,
				186094132B6BE15100B3C95F /* Route.swift */,
			);
			path = Route;
			sourceTree = "<group>";
		};
		18CB62AD2B6908B5009D60CE /* Main */ = {
			isa = PBXGroup;
			children = (
				18CD427B2D9125840095378F /* NetworkMonitor.swift */,
				185B680A2CBD8FAB00D3DC0D /* LocationDeniedView.swift */,
				18CB62AE2B6908D3009D60CE /* MainView.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		18CB62B42B6BD8B6009D60CE /* Dashboard */ = {
			isa = PBXGroup;
			children = (
				18CB62B52B6BD8C8009D60CE /* Model */,
				18CB62B62B6BD8D5009D60CE /* View */,
				18CB62B72B6BD8DA009D60CE /* ViewModel */,
				18CB62B82B6BD906009D60CE /* Tabs */,
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
		18CB62B52B6BD8C8009D60CE /* Model */ = {
			isa = PBXGroup;
			children = (
				1860940F2B6BDB9800B3C95F /* TabModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		18CB62B62B6BD8D5009D60CE /* View */ = {
			isa = PBXGroup;
			children = (
				18CB62B92B6BD9B4009D60CE /* DashboardView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18CB62B72B6BD8DA009D60CE /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				186094112B6BE01900B3C95F /* DashboardViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18CB62B82B6BD906009D60CE /* Tabs */ = {
			isa = PBXGroup;
			children = (
				18566E9A2BBAB0600050A98B /* TabExtension.swift */,
			);
			path = Tabs;
			sourceTree = "<group>";
		};
		18EED2962C959D0000FFBCCA /* New Group */ = {
			isa = PBXGroup;
			children = (
			);
			path = "New Group";
			sourceTree = "<group>";
		};
		18EED2992C959E6800FFBCCA /* AddCard */ = {
			isa = PBXGroup;
			children = (
				18EED2972C959E3B00FFBCCA /* AddCardView.swift */,
				18EED29A2C959E9800FFBCCA /* AddCardViewModel.swift */,
			);
			path = AddCard;
			sourceTree = "<group>";
		};
		18F0225F2B97204E00A35420 /* SuperBase */ = {
			isa = PBXGroup;
			children = (
				18F34D122BD646D400A9DDC2 /* Model */,
				18F34D112BD646B300A9DDC2 /* View */,
				18F34D132BD646DA00A9DDC2 /* ViewModel */,
			);
			path = SuperBase;
			sourceTree = "<group>";
		};
		18F34D012BD642DC00A9DDC2 /* APIServices */ = {
			isa = PBXGroup;
			children = (
				18F34D022BD642F100A9DDC2 /* Repositories */,
				18F34D032BD642FE00A9DDC2 /* BaseAPI */,
			);
			path = APIServices;
			sourceTree = "<group>";
		};
		18F34D022BD642F100A9DDC2 /* Repositories */ = {
			isa = PBXGroup;
			children = (
				18F34D0F2BD6464500A9DDC2 /* RepositoriesAPI.swift */,
				18F34D0D2BD645F100A9DDC2 /* RepositoriesNetworking.swift */,
				18F34D0B2BD6456A00A9DDC2 /* APIEndPoints.swift */,
			);
			path = Repositories;
			sourceTree = "<group>";
		};
		18F34D032BD642FE00A9DDC2 /* BaseAPI */ = {
			isa = PBXGroup;
			children = (
				18F34D042BD6432500A9DDC2 /* TargetType.swift */,
				18F34D062BD644B900A9DDC2 /* BaseAPI.swift */,
			);
			path = BaseAPI;
			sourceTree = "<group>";
		};
		18F34D112BD646B300A9DDC2 /* View */ = {
			isa = PBXGroup;
			children = (
				18F022602B97209200A35420 /* MainScrollBody.swift */,
				18F34D3B2BDAA28400A9DDC2 /* SuperView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		18F34D122BD646D400A9DDC2 /* Model */ = {
			isa = PBXGroup;
			children = (
				18F34D142BD646FF00A9DDC2 /* SuperModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		18F34D132BD646DA00A9DDC2 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				18F34D162BD64D3E00A9DDC2 /* SuperViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		18F34D1A2BD6E15700A9DDC2 /* NetworkImage */ = {
			isa = PBXGroup;
			children = (
				18F34D1B2BD6E16400A9DDC2 /* NetworkImageView.swift */,
			);
			path = NetworkImage;
			sourceTree = "<group>";
		};
		18F34D3D2BDAA44400A9DDC2 /* ActivityLoader */ = {
			isa = PBXGroup;
			children = (
				188222082CF70C19005FB2C9 /* LoadingView.swift */,
				1847BCFC2CD3F58500020F9F /* LottieView.swift */,
				18F34D3E2BDAA45300A9DDC2 /* ActivityLoaderView.swift */,
			);
			path = ActivityLoader;
			sourceTree = "<group>";
		};
		18F34D402BDAA4B000A9DDC2 /* AlertComponent */ = {
			isa = PBXGroup;
			children = (
				18F34D412BDAA4C400A9DDC2 /* AlertView.swift */,
			);
			path = AlertComponent;
			sourceTree = "<group>";
		};
		18F735D02CCBD2C300F68F62 /* Category */ = {
			isa = PBXGroup;
			children = (
				18F735D12CCBD2D200F68F62 /* MainCategoryView.swift */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		19DE9B8A47E3AF00F14AF998 /* AppoinmentView */ = {
			isa = PBXGroup;
			children = (
				185B680C2CC1A82800D3DC0D /* WrappingHStack.swift */,
				185EA9552C220433008554FB /* BookingAppointmentModel.swift */,
				588BD6C454432C1260EAC106 /* AppointmentView.swift */,
				A90EA4B5EB1AEEF0169C0807 /* AppointmentViewModel.swift */,
				311A07F7E207FAFA7A1B5D4A /* Cell */,
				185886CD2BBC115A00650239 /* WriteReview */,
				18AFC0D02C831E6500DF0F7A /* AppointmentShimmerView.swift */,
			);
			path = AppoinmentView;
			sourceTree = "<group>";
		};
		1D3B9243310137611F04567D /* Cell */ = {
			isa = PBXGroup;
			children = (
				6220319E02A09E3CBE9404A4 /* Artistitem1Cell.swift */,
				75434BAEDCA5814DF1AFCD60 /* SaloonCardCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		1DB4B653653B29834CA13336 /* RadioGroup */ = {
			isa = PBXGroup;
			children = (
				E51DFFB761A4EC02534FAC07 /* RadioGroupButton.swift */,
			);
			path = RadioGroup;
			sourceTree = "<group>";
		};
		1E0C2E724705C23309F3AFEE /* ShopdetailsPortfolioView */ = {
			isa = PBXGroup;
			children = (
				62BA7B6791BF67FF3CA0E13D /* ShopdetailsPortfolioView.swift */,
				A2C60F160F19162FFD1FD6BA /* Cell */,
				473FFE5F3A8D6DB1B7B41A57 /* ShopdetailsPortfolioViewModel.swift */,
			);
			path = ShopdetailsPortfolioView;
			sourceTree = "<group>";
		};
		1ECD300C7795C1A2B30732B2 /* EReceptView */ = {
			isa = PBXGroup;
			children = (
				18931CFE2CA4252E00E258DA /* DocumentPicker.swift */,
				11B9806A061E66B95ECC5917 /* EReceiptView.swift */,
				35BD570585104FCE6D563F2B /* EReceptViewModel.swift */,
			);
			path = EReceptView;
			sourceTree = "<group>";
		};
		2172E71BD7D9B43BDB4AC492 /* TransactionView */ = {
			isa = PBXGroup;
			children = (
				185A35B22BEA514E0006CB98 /* TransactionHistoryModel.swift */,
				22537CF02841E18BE0784A18 /* TransactionView.swift */,
				464D04E69A58CAFE479210B8 /* Cell */,
				CFAAC225ACFE3625539728AD /* TransactionViewModel.swift */,
			);
			path = TransactionView;
			sourceTree = "<group>";
		};
		270049F3DF5C3B4A1C550D67 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				7CDFD3999F2E0D0C5EB05B0B /* NunitoBold.ttf */,
				C5060714CFF68337B5BF5E77 /* MontserratSemiBold.ttf */,
				861ABF7F5C33AA8EE90A5C32 /* NunitoSemiBold.ttf */,
				2C11C85F2B3286672CABA5DC /* NunitoExtraBold.ttf */,
				C6FF231CA984A637D19A591D /* MontserratMedium.ttf */,
				539F0E5993329539C7722019 /* NunitoMedium.ttf */,
				B769DA8710932D978AE5C830 /* NunitoRegular.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		2A84E9F20B5C30B5097D7CBD /* DateandtimeView */ = {
			isa = PBXGroup;
			children = (
				A0042B3BC9F651A6A77EDC34 /* DateandtimeView.swift */,
				585D125856A5C94C13A954C6 /* Cell */,
				8DAE942D25AFB6435A52E112 /* DateandtimeViewModel.swift */,
			);
			path = DateandtimeView;
			sourceTree = "<group>";
		};
		2AD12A22D11F94DD6D4BBC47 /* AddreviewTwoView */ = {
			isa = PBXGroup;
			children = (
				CA8A83A45A761C40C2F0D3DB /* AddreviewTwoView.swift */,
				0FE60057EB377E8C95519B6A /* Cell */,
				66DD2D6F64816A94A6CA4A4B /* AddreviewTwoViewModel.swift */,
			);
			path = AddreviewTwoView;
			sourceTree = "<group>";
		};
		2D4879F8F598BA557A0461FF /* ShopDetails */ = {
			isa = PBXGroup;
			children = (
				185A35B42BEAA6BC0006CB98 /* ShopDetailsModel.swift */,
				13588453732E17D146626872 /* ShopDetailsView.swift */,
				18CD42732D88401A0095378F /* ShopDetailsShimmerView.swift */,
				4F9C244AB993E1460414A861 /* ShopDetailsViewModel.swift */,
				18F34D322BD7C41F00A9DDC2 /* VendorDetailsModel.swift */,
				1816CDC42BDBC2830058E6B9 /* TabViewDynamicHeight.swift */,
			);
			path = ShopDetails;
			sourceTree = "<group>";
		};
		2D915B1FE0B027A88FECE537 /* Cell */ = {
			isa = PBXGroup;
			children = (
				A884E9014DE2C84B43773177 /* RowhairextensionCell.swift */,
				18DA00862B9F36DF00AAFC10 /* TestCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		311A07F7E207FAFA7A1B5D4A /* Cell */ = {
			isa = PBXGroup;
			children = (
				A15C6364B50C21D6A4032870 /* AppointmentUpcomingCell.swift */,
				185886C92BBC0B2100650239 /* AppointmentCompletedCell.swift */,
				185886CB2BBC0D3100650239 /* AppointmentCancelledCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		344B6660A71BC894F63EC287 /* BottomSheetView */ = {
			isa = PBXGroup;
			children = (
				0D0FCF3657EDE31E1726DEEA /* BottomSheetView.swift */,
			);
			path = BottomSheetView;
			sourceTree = "<group>";
		};
		360AF79413C5D5D6243D0902 /* MapviewView */ = {
			isa = PBXGroup;
			children = (
				185B68082CBD8ED800D3DC0D /* MapLocationManager.swift */,
				185A35A52BE933900006CB98 /* SaloonMapModel.swift */,
				D49C671BFBEB657EE440676C /* SaloonMapView.swift */,
				6E5FA2C4BB64E7C81BDB0799 /* Cell */,
				2454E66944C46A9C50CE784D /* SaloonMapViewModel.swift */,
				18AFC0C82C804FC300DF0F7A /* SaloonMapShimmerView.swift */,
			);
			path = MapviewView;
			sourceTree = "<group>";
		};
		37E2E0AF5957188C112647CA /* MyaccountView */ = {
			isa = PBXGroup;
			children = (
				8F7E6C01F3CC99535E6103DD /* MyAccountView.swift */,
				C4A975E63C96AD56F3305B76 /* MyAccountViewModel.swift */,
				185A35AE2BEA29BC0006CB98 /* MyAccountModel.swift */,
			);
			path = MyaccountView;
			sourceTree = "<group>";
		};
		4170B57B6FA90FD20F178970 /* Cell */ = {
			isa = PBXGroup;
			children = (
				957FBC9615CD14885AFD7BDE /* Artistitem3Cell.swift */,
				014FF76E520B32B3BAD64480 /* Rowrectangleten4Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		41A69F2AA4E42040E6B095E6 /* AppoinmentcancelledView */ = {
			isa = PBXGroup;
			children = (
				374F6AEF4D2F6023C3D7837B /* AppoinmentcancelledView.swift */,
				8E2DAA8202BA207095E65247 /* Cell */,
				298EFA764F7A2363385A6291 /* AppoinmentcancelledViewModel.swift */,
			);
			path = AppoinmentcancelledView;
			sourceTree = "<group>";
		};
		454F9DFAC4C19D126B77BA1B /* SplashView */ = {
			isa = PBXGroup;
			children = (
				1C339EB5AB4A55EB8D08502A /* SplashView.swift */,
				2940D7B41F325169FAFFB9E3 /* SplashViewModel.swift */,
			);
			path = SplashView;
			sourceTree = "<group>";
		};
		464D04E69A58CAFE479210B8 /* Cell */ = {
			isa = PBXGroup;
			children = (
				352ABB27C143678799D9E7BE /* TransactionareCell.swift */,
				D930045FAACE59AFF24C8A6A /* Transactionare1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		4669EC9FFF3CCA7BC9B09552 /* Cell */ = {
			isa = PBXGroup;
			children = (
				F8FC4BBA85ED1E696AA1C3D0 /* ArtistitemCell.swift */,
				1231BCFBD775C8B509086064 /* RowrectangletenCell.swift */,
				289BD4426287B5E8E9846FE3 /* RadiogroupgreatexperiencCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		4DE0305D47C8EF047A8A6704 /* Cell */ = {
			isa = PBXGroup;
			children = (
				655A285A1829F15AF921ADD9 /* Artistitem2Cell.swift */,
				7D37AE73ADEC24E180B41C67 /* Rowrectangleten3Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		4EF51267C0EB61DF76A81FFA /* SigninView */ = {
			isa = PBXGroup;
			children = (
				18BD0A2B2D7B18DE00AB65A0 /* PasskeyManager.swift */,
				187FEECA2CD57B6E00EAC51C /* RegistrationOtpView.swift */,
				496E7AD02FFAF5A360FF5EC4 /* SignInView.swift */,
				182FCE5D2BDFA5F7000C2717 /* SignUpView.swift */,
				C5E438456C48EC439D0228BD /* AuthViewModel.swift */,
				18EED2832C90899500FFBCCA /* AuthService.swift */,
			);
			path = SigninView;
			sourceTree = "<group>";
		};
		51080A97FCF35A4E255C0730 /* AppoinmentcompleteView */ = {
			isa = PBXGroup;
			children = (
				A6C092156BAFBB7773D6523A /* AppoinmentcompleteView.swift */,
				089C70EFC6D3EFF435A7CEB7 /* Cell */,
				322F2E7405166D6E2490F92A /* AppoinmentcompleteViewModel.swift */,
			);
			path = AppoinmentcompleteView;
			sourceTree = "<group>";
		};
		5193787D0B0E9FB771C19267 /* PaymentmethodeView */ = {
			isa = PBXGroup;
			children = (
				187FEEE52CF1ED8B00EAC51C /* PaymentCardViewModel.swift */,
				187FEEE32CF1E9A300EAC51C /* PaymentCardModel.swift */,
				74D199077DBA5E9CDE9AD755 /* PaymentMethodView.swift */,
				18EED29C2C95AE7300FFBCCA /* UserPaymentCardView.swift */,
				00BF26C32DEEE6E4C2D6EFD1 /* PaymentmethodeViewModel.swift */,
				18EED2992C959E6800FFBCCA /* AddCard */,
				623799C4A1ABC8DDE3435362 /* Cell */,
				186BC7832C99DEBE00DA5131 /* BookingSuccessView.swift */,
			);
			path = PaymentmethodeView;
			sourceTree = "<group>";
		};
		52D2585B6E2A2702CC36A894 /* Shopdetails1View */ = {
			isa = PBXGroup;
			children = (
				1C3F388FA9E3BD0C82EB0DC4 /* Shopdetails1View.swift */,
				CDDDDA12B2C020A42383C925 /* Cell */,
				32413C28985DD80026544535 /* Shopdetails1ViewModel.swift */,
			);
			path = Shopdetails1View;
			sourceTree = "<group>";
		};
		5544375D63656459366E98DE /* Cell */ = {
			isa = PBXGroup;
			children = (
				09F5200C1018D83C1EF669F8 /* RateiteCell.swift */,
				66655C096CF00E09213036B9 /* ColumnellipseCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		57B83B0153F7CA8E4925BCB1 /* SettingsView */ = {
			isa = PBXGroup;
			children = (
				18E7791F2D35A7E0000E18CB /* ServiceForView.swift */,
				18E7791D2D359959000E18CB /* ReminderNotificationModel.swift */,
				18E7791B2D358CE3000E18CB /* NotificationReminderViewModel.swift */,
				18E779192D358B85000E18CB /* NotificationReminderView.swift */,
				18B0871E2D09B770001F6DE8 /* LanguageView.swift */,
				187FEEC82CD55C4000EAC51C /* NotificationSettingsModel.swift */,
				18F7361F2CCF959300F68F62 /* UserNotificationSettingsView.swift */,
				21B173C73368FC1CA644C333 /* SettingsView.swift */,
				C26F875F6325FD0DEC3F67A0 /* SettingsViewModel.swift */,
			);
			path = SettingsView;
			sourceTree = "<group>";
		};
		585D125856A5C94C13A954C6 /* Cell */ = {
			isa = PBXGroup;
			children = (
				8306749AF68E8488C882DABE /* Rowbookingdate1Cell.swift */,
				62C97208D78369ECE0A6A1D5 /* Rowkdcounter1Cell.swift */,
				95E07F9D3B57A9C5354157C8 /* Rowdateitem1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		596DD832727ACDA7E7CD079E /* CancelmodifyView */ = {
			isa = PBXGroup;
			children = (
				66B8C2A31A7480EC8D957251 /* CancelModifyView.swift */,
				C1EBF4EADB845235D9585B94 /* Cell */,
				647ECD8F27ADC8020AC6FDFA /* CancelmodifyViewModel.swift */,
			);
			path = CancelmodifyView;
			sourceTree = "<group>";
		};
		598583C7C6DEF0D71B60145E /* Cell */ = {
			isa = PBXGroup;
			children = (
				E149A0286C2B9631153A5D26 /* RowserviceiteCell.swift */,
				CE8957B2CCE57BD2513F85C8 /* RowtimeCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		5B62090E6D1A4916B764FD23 /* HomeView */ = {
			isa = PBXGroup;
			children = (
				1D3B9243310137611F04567D /* Cell */,
				18F34D182BD6D8A200A9DDC2 /* HomeModel.swift */,
				48484A1E2DD83F2045AB3591 /* HomeView.swift */,
				18CB62B22B699E00009D60CE /* CalendarView.swift */,
				C839DB3248481D896457DF7F /* HomeViewModel.swift */,
				18F34D2E2BD6E62A00A9DDC2 /* LocationManager.swift */,
			);
			path = HomeView;
			sourceTree = "<group>";
		};
		5D4F330B0258291286F5EDFE /* ProfileOneView */ = {
			isa = PBXGroup;
			children = (
				D1A5B0968B143938EAC3AA12 /* ProfileOneViewModel.swift */,
			);
			path = ProfileOneView;
			sourceTree = "<group>";
		};
		602983C17E9F585F25DEDDD2 /* ProfileView */ = {
			isa = PBXGroup;
			children = (
				181E384C2CA1E98C00780921 /* CustomDatePicker.swift */,
				188002E32BDE661B007375B0 /* ProfileModel.swift */,
				1FA512405431EFEE8DC23D05 /* ProfileView.swift */,
				1C62A33A20C5B76E73CAE83C /* ProfileViewModel.swift */,
			);
			path = ProfileView;
			sourceTree = "<group>";
		};
		623799C4A1ABC8DDE3435362 /* Cell */ = {
			isa = PBXGroup;
			children = (
				F016C94BD970A43EA301E38D /* InputCell.swift */,
				18EED29E2C95AED200FFBCCA /* RowellipseCell.swift */,
				18EED2A02C95B87000FFBCCA /* PaymentCardView.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		64F097860931F8D1663F0098 /* TimeView */ = {
			isa = PBXGroup;
			children = (
				23CA2090F2F413108567ADD2 /* TimeView.swift */,
				A60D5F30D80C863A42254EAD /* Cell */,
				C6C43755E9B7F8B79D77D16C /* TimeViewModel.swift */,
			);
			path = TimeView;
			sourceTree = "<group>";
		};
		65C4354761F4C347EC14963A /* Cell */ = {
			isa = PBXGroup;
			children = (
				F3CFA18DA8A9BD4BA837A989 /* Rowrectangleten1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6AB9C09CAA6D34530804274D /* ColorpatternView */ = {
			isa = PBXGroup;
			children = (
				74704DBA2278E8AEF4DC95F1 /* ColorpatternView.swift */,
				EB790E7D8F1B3339F5CDD254 /* Cell */,
				D89D6A25389051B2DC4FF59C /* ColorpatternViewModel.swift */,
			);
			path = ColorpatternView;
			sourceTree = "<group>";
		};
		6E5FA2C4BB64E7C81BDB0799 /* Cell */ = {
			isa = PBXGroup;
			children = (
				A865747F9E855DA5560AFF9B /* MapPinCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		7288A55056CD426040150C21 /* WelcomeView */ = {
			isa = PBXGroup;
			children = (
				8F6890ACB3B3A4AC43F7DC24 /* WelcomeView.swift */,
				AFBBD4402E23412D642341DC /* WelcomeViewModel.swift */,
			);
			path = WelcomeView;
			sourceTree = "<group>";
		};
		76A4E9085647BF6D06888E77 /* Progressbar */ = {
			isa = PBXGroup;
			children = (
				3B4D29C125863C8CC3C33DCC /* LinearProgress.swift */,
				3BC67228A0A7B91B4AC455CE /* CircularProgress.swift */,
			);
			path = Progressbar;
			sourceTree = "<group>";
		};
		7F44DB9E27422A4200171570 /* Helper */ = {
			isa = PBXGroup;
			children = (
				186CFB512CC8364C00117D6C /* Helper Class */,
				188002E52BDEA26D007375B0 /* ImagePicker */,
				1816CDC92BDCFBB20058E6B9 /* UserDefaults */,
				88680D1A2775C5BD002E964F /* Viewport */,
				7F44DB9F27422A4200171570 /* ActivityLoader */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		7F44DB9F27422A4200171570 /* ActivityLoader */ = {
			isa = PBXGroup;
			children = (
				7F44DBA027422A5D00171570 /* ActivityLoader.swift */,
			);
			path = ActivityLoader;
			sourceTree = "<group>";
		};
		809AB9325C411A9B57497528 /* RatepopupOneContainerView */ = {
			isa = PBXGroup;
			children = (
				57560E13D7D6EFBE27F3B90A /* RatepopupOneContainerView.swift */,
				34B2596C4529369EF0FE3A77 /* RatepopupOneContainerViewModel.swift */,
			);
			path = RatepopupOneContainerView;
			sourceTree = "<group>";
		};
		8332F7C83C14D86D39D7DCB8 /* ShopdetailsReviewView */ = {
			isa = PBXGroup;
			children = (
				8B10CAB0607035A11B25158E /* ShopdetailsReviewView.swift */,
				5544375D63656459366E98DE /* Cell */,
				3E92C9457D694607B4DF33BE /* ShopdetailsReviewViewModel.swift */,
			);
			path = ShopdetailsReviewView;
			sourceTree = "<group>";
		};
		835ADDAB500A662F6E4C483B /* RatepopupTwoView */ = {
			isa = PBXGroup;
			children = (
				F4242827EFFAD75839AC966D /* RatepopupTwoView.swift */,
				4DE0305D47C8EF047A8A6704 /* Cell */,
				8191F5E3981071AD9CC36383 /* RatepopupTwoViewModel.swift */,
			);
			path = RatepopupTwoView;
			sourceTree = "<group>";
		};
		84841587FCFB9689A5CE3D00 /* Modules */ = {
			isa = PBXGroup;
			children = (
				18EED2962C959D0000FFBCCA /* New Group */,
				18CB62AD2B6908B5009D60CE /* Main */,
				18F0225F2B97204E00A35420 /* SuperBase */,
				454F9DFAC4C19D126B77BA1B /* SplashView */,
				7288A55056CD426040150C21 /* WelcomeView */,
				D6DF6B590A2873B037957C38 /* Onboarding */,
				A23275F3878F073EC3D86168 /* SelectionView */,
				18AFC0C32C7CE32A00DF0F7A /* Menu */,
				18CB62B42B6BD8B6009D60CE /* Dashboard */,
				5B62090E6D1A4916B764FD23 /* HomeView */,
				18F735D02CCBD2C300F68F62 /* Category */,
				2D4879F8F598BA557A0461FF /* ShopDetails */,
				034A157C01138A19B1665039 /* BookappoinmentView */,
				186DC6042BE7C16D00B24DC2 /* Notifications */,
				AFB3793D479FD6E6BF052431 /* RatepopupOneView */,
				EAB9E5136E25A5070C4327F3 /* ExploreView */,
				19DE9B8A47E3AF00F14AF998 /* AppoinmentView */,
				6AB9C09CAA6D34530804274D /* ColorpatternView */,
				360AF79413C5D5D6243D0902 /* MapviewView */,
				BA10BA432C587C521C083822 /* MapviewpopupView */,
				A81A52E3EC209BF60824B761 /* FilterView */,
				184730362C76408A006385D2 /* ForgotPassword */,
				E72407568C22C7F068781EA5 /* ShopDetailsService */,
				52D2585B6E2A2702CC36A894 /* Shopdetails1View */,
				A32173A2EB0B3A83CC89D98E /* ShopdetailsabouutView */,
				8332F7C83C14D86D39D7DCB8 /* ShopdetailsReviewView */,
				1E0C2E724705C23309F3AFEE /* ShopdetailsPortfolioView */,
				4EF51267C0EB61DF76A81FFA /* SigninView */,
				37E2E0AF5957188C112647CA /* MyaccountView */,
				1ECD300C7795C1A2B30732B2 /* EReceptView */,
				596DD832727ACDA7E7CD079E /* CancelmodifyView */,
				2A84E9F20B5C30B5097D7CBD /* DateandtimeView */,
				64F097860931F8D1663F0098 /* TimeView */,
				51080A97FCF35A4E255C0730 /* AppoinmentcompleteView */,
				41A69F2AA4E42040E6B095E6 /* AppoinmentcancelledView */,
				************************ /* AddreviewView */,
				2AD12A22D11F94DD6D4BBC47 /* AddreviewTwoView */,
				88D242392671F8DCF919214D /* AddreviewThreeView */,
				A4ADD531EDEBC2B742112436 /* AddreviewFourOneView */,
				C4A7CA6E4DABDFADE5D1623D /* AddreviewFour1View */,
				C7C736EA19452876E5A70158 /* AddreviewFour2View */,
				D5806F9E480C2BBDCBD49672 /* Ratepopup1View */,
				835ADDAB500A662F6E4C483B /* RatepopupTwoView */,
				08AF5232186F8617C3485BA8 /* RatepopupThreeView */,
				************************ /* RatepopupsucessmessegeView */,
				186DC60F2BE8060200B24DC2 /* AddressListing */,
				185A359F2BE8BE430006CB98 /* AddAddress */,
				602983C17E9F585F25DEDDD2 /* ProfileView */,
				5D4F330B0258291286F5EDFE /* ProfileOneView */,
				5193787D0B0E9FB771C19267 /* PaymentmethodeView */,
				CCA0BB548F173187AC1F381D /* SavedView */,
				57B83B0153F7CA8E4925BCB1 /* SettingsView */,
				2172E71BD7D9B43BDB4AC492 /* TransactionView */,
				8599D93218D5E5BA3BEDD135 /* HelpCenterView */,
				EFEDE2624399A82E1E774812 /* HelpCentercontactView */,
				03052F244A64FDAB4E9BB494 /* HelpCentercontact1View */,
				D81AD3E507FB0DCD4CEAAACA /* PrivacyPolicyView */,
				0D8BBC2E825663BF50A998B5 /* PasswordManagerView */,
				F8E547F93CBAF0A983D0ED2A /* LogoutpopupView */,
				809AB9325C411A9B57497528 /* RatepopupOneContainerView */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		8599D93218D5E5BA3BEDD135 /* HelpCenterView */ = {
			isa = PBXGroup;
			children = (
				32D1A4E9917D92CBF0DFE019 /* FAQView.swift */,
				075FD6074F250A30049D0BD2 /* FAQViewModel.swift */,
				1815DE532BC166D600FEEF15 /* FAQSampleModel.swift */,
			);
			path = HelpCenterView;
			sourceTree = "<group>";
		};
		86E79B63F95225578A3F2B26 /* Cell */ = {
			isa = PBXGroup;
			children = (
				E6874B26D5E27A3FF2BE2696 /* Artistitem4Cell.swift */,
				4609FE900042C518C05F3C40 /* Rowrectangleten5Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		88680D1A2775C5BD002E964F /* Viewport */ = {
			isa = PBXGroup;
			children = (
				88680D1B2775C601002E964F /* ViewportHelper.swift */,
			);
			path = Viewport;
			sourceTree = "<group>";
		};
		88D242392671F8DCF919214D /* AddreviewThreeView */ = {
			isa = PBXGroup;
			children = (
				83C3AC99758D07D7198071BB /* AddreviewThreeView.swift */,
				529A95E8FE6F29260C311EAE /* AddreviewThreeViewModel.swift */,
			);
			path = AddreviewThreeView;
			sourceTree = "<group>";
		};
		88ED74DB272FFE6E0088E3EF = {
			isa = PBXGroup;
			children = (
				88ED74E6272FFE6E0088E3EF /* Bookme */,
				88ED74E5272FFE6E0088E3EF /* Products */,
			);
			sourceTree = "<group>";
		};
		88ED74E5272FFE6E0088E3EF /* Products */ = {
			isa = PBXGroup;
			children = (
				88ED74E4272FFE6E0088E3EF /* Bookme.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		88ED74E6272FFE6E0088E3EF /* Bookme */ = {
			isa = PBXGroup;
			children = (
				1849D2272DAE908C0053CB32 /* assetlinks.json */,
				189784B72C90538D00BA70A0 /* Bookme.entitlements */,
				188221F02CF50C30005FB2C9 /* Localizable.xcstrings */,
				88ED74FE272FFECA0088E3EF /* Application */,
				18CB62A62B690482009D60CE /* Core */,
				84841587FCFB9689A5CE3D00 /* Modules */,
				88ED74F8272FFECA0088E3EF /* Constants */,
				88ED74FA272FFECA0088E3EF /* Extensions */,
				88ED74F6272FFECA0088E3EF /* Utilities */,
				7F44DB9E27422A4200171570 /* Helper */,
				D7C589974E21419135CDC5B9 /* Components */,
				DF2BC6389F54F9765117BC20 /* Resources */,
				88ED74ED272FFE6F0088E3EF /* Preview Content */,
				88ED74F0272FFE6F0088E3EF /* Info.plist */,
				189258D52D17214D00EB1D1D /* GoogleService-Info.plist */,
				88ED74EB272FFE6F0088E3EF /* Assets.xcassets */,
				18B0871C2D099F0F001F6DE8 /* InfoPlist.xcstrings */,
			);
			path = Bookme;
			sourceTree = "<group>";
		};
		88ED74ED272FFE6F0088E3EF /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				88ED74EE272FFE6F0088E3EF /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		88ED74F6272FFECA0088E3EF /* Utilities */ = {
			isa = PBXGroup;
			children = (
				88ED74F7272FFECA0088E3EF /* Utilities.swift */,
				87A58454AF817ADDCEC787F3 /* Validation.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		88ED74F8272FFECA0088E3EF /* Constants */ = {
			isa = PBXGroup;
			children = (
				88ED74F9272FFECA0088E3EF /* AppConstants.swift */,
				18372F3BFEF76398F3421469 /* StringScheme.swift */,
				9159EB55DD88DDF0BDAE5965 /* ColorConstants.swift */,
				12B4D2AAEACE4FC95787DE0A /* FontScheme.swift */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		88ED74FA272FFECA0088E3EF /* Extensions */ = {
			isa = PBXGroup;
			children = (
				187FEEE12CE66AD400EAC51C /* StringExtension.swift */,
				18F736182CCF74B500F68F62 /* UIApplicationExtension.swift */,
				185B68122CC3A6C200D3DC0D /* DateExtensions.swift */,
				888CB47427686A000041116C /* APIExtensions.swift */,
				8853052527718DC500B04E6F /* ViewExtensions.swift */,
				8853052B2771949600B04E6F /* UINavigationController.swift */,
				8853052927718E2D00B04E6F /* EncodableExtension.swift */,
				884F3E5027B0E65A00963FC4 /* RoundedCornersView.swift */,
				186DC60D2BE7F3EA00B24DC2 /* ColorExtension.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		88ED74FE272FFECA0088E3EF /* Application */ = {
			isa = PBXGroup;
			children = (
				18D44EA62D16FC9D00A2C54A /* apple-app-site-association.json */,
				18C2A5BC2D67C2ED001AE292 /* Message.apns */,
				C40AB9F72754D79E006BC5D7 /* AppDelegate.swift */,
				88ED74FF272FFECA0088E3EF /* BookmeApp.swift */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		8E2DAA8202BA207095E65247 /* Cell */ = {
			isa = PBXGroup;
			children = (
				4FAF4E03AD06A81A2E8A23B0 /* Appoinmentitem2Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		9F635686A44F47EA553A27E5 /* Cell */ = {
			isa = PBXGroup;
			children = (
				186CFB4F2CC7C6D700117D6C /* CartItemCellView.swift */,
				185B68102CC3A68600D3DC0D /* CustomBottomNavigationButton.swift */,
				187901F12C04532C00355246 /* MonthPopUpView.swift */,
				185B680E2CC3935800D3DC0D /* TopStaffCell.swift */,
				************************ /* RowdateitemCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		A23275F3878F073EC3D86168 /* SelectionView */ = {
			isa = PBXGroup;
			children = (
				AA145ED0EA67F23FDCDA482F /* SelectionView.swift */,
				77425580602C779AAC85478F /* SelectionViewModel.swift */,
			);
			path = SelectionView;
			sourceTree = "<group>";
		};
		A2C60F160F19162FFD1FD6BA /* Cell */ = {
			isa = PBXGroup;
			children = (
				EE295BF81125B3366EF7A2F1 /* Stackrectangle4024oneCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		A32173A2EB0B3A83CC89D98E /* ShopdetailsabouutView */ = {
			isa = PBXGroup;
			children = (
				4BD62E1311AD397DEA979D58 /* ShopdetailsabouutView.swift */,
				1A0FE3A43DAFE9B3390E7B93 /* ShopdetailsabouutViewModel.swift */,
			);
			path = ShopdetailsabouutView;
			sourceTree = "<group>";
		};
		A4ADD531EDEBC2B742112436 /* AddreviewFourOneView */ = {
			isa = PBXGroup;
			children = (
				2F68D294E846C074E584257D /* AddreviewFourOneView.swift */,
				DBE4773819FD9699756A31AB /* AddreviewFourOneViewModel.swift */,
			);
			path = AddreviewFourOneView;
			sourceTree = "<group>";
		};
		A5F2C66E93DD3CAEC6F45877 /* Cell */ = {
			isa = PBXGroup;
			children = (
				9C225A3C75CAB5D48C4DAC3D /* FaqitemCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		A60D5F30D80C863A42254EAD /* Cell */ = {
			isa = PBXGroup;
			children = (
				99DA37BE9503DEA91149896E /* Rowbookingdate2Cell.swift */,
				66FDDFAEA96593E2D197A0E5 /* Rowkdcounter2Cell.swift */,
				AD2534C5A37A1FAFC7DB65F7 /* Rowtime1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		A7E01A9B83ACFD8619CEA692 /* Cell */ = {
			isa = PBXGroup;
			children = (
				BEAA60B52A9D28713EF5616C /* Appoinmentitem3Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		A81A52E3EC209BF60824B761 /* FilterView */ = {
			isa = PBXGroup;
			children = (
				598583C7C6DEF0D71B60145E /* Cell */,
				18F34D302BD7AF1A00A9DDC2 /* FiltersModel.swift */,
				E139C3DA4506C3688F40F0EC /* FilterView.swift */,
				182CE7062B87D6D900F2FEF8 /* SliderView.swift */,
				79F799C23A3762C0CB22EECA /* FilterViewModel.swift */,
				1865DB742C79C7FF00C7C9D7 /* FilterShimmerView.swift */,
			);
			path = FilterView;
			sourceTree = "<group>";
		};
		AFB3793D479FD6E6BF052431 /* RatepopupOneView */ = {
			isa = PBXGroup;
			children = (
				EC307C65E9A49A79891B5130 /* RatepopupOneView.swift */,
				4669EC9FFF3CCA7BC9B09552 /* Cell */,
				24EA8E84EDA16FD16C31EB07 /* RatepopupOneViewModel.swift */,
			);
			path = RatepopupOneView;
			sourceTree = "<group>";
		};
		B3FD4AC9ED479DAB9AB0F214 /* FabButton */ = {
			isa = PBXGroup;
			children = (
				55FEDEEE35BF9B562B7463F8 /* FabButton.swift */,
			);
			path = FabButton;
			sourceTree = "<group>";
		};
		BA10BA432C587C521C083822 /* MapviewpopupView */ = {
			isa = PBXGroup;
			children = (
				5DD0ED80654454421B558145 /* MapviewpopupView.swift */,
				C027AE8C24ECA55D8E51AF03 /* Cell */,
				0D4EF4A8DAD78C9F31471940 /* MapviewpopupViewModel.swift */,
			);
			path = MapviewpopupView;
			sourceTree = "<group>";
		};
		************************ /* AddreviewView */ = {
			isa = PBXGroup;
			children = (
				E0BD1764907B102FA4712E2B /* AddreviewView.swift */,
				E491B4BF03311FC15008DB0A /* AddreviewViewModel.swift */,
			);
			path = AddreviewView;
			sourceTree = "<group>";
		};
		C027AE8C24ECA55D8E51AF03 /* Cell */ = {
			isa = PBXGroup;
			children = (
				E50D6212B408FE71146FFE2C /* MapPin1Cell.swift */,
				572FB3411AC234B38F39C3F4 /* MapSaloonCardCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		C1EBF4EADB845235D9585B94 /* Cell */ = {
			isa = PBXGroup;
			children = (
				71D21200D0C42BDF5C591D71 /* RowbookingdateCell.swift */,
				64C93F3D6A16F6DF47423661 /* RowkdcounterCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		C4A7CA6E4DABDFADE5D1623D /* AddreviewFour1View */ = {
			isa = PBXGroup;
			children = (
				13EEE21115A6A962A27B5C58 /* AddreviewFour1View.swift */,
				F94F5577B4AEA120D05D3897 /* AddreviewFour1ViewModel.swift */,
			);
			path = AddreviewFour1View;
			sourceTree = "<group>";
		};
		C7C736EA19452876E5A70158 /* AddreviewFour2View */ = {
			isa = PBXGroup;
			children = (
				A4058DFA680D5FA211885DE9 /* AddreviewFour2View.swift */,
				A7E01A9B83ACFD8619CEA692 /* Cell */,
				C5025C6465A0ADF52FFBEBF5 /* AddreviewFour2ViewModel.swift */,
			);
			path = AddreviewFour2View;
			sourceTree = "<group>";
		};
		CCA0BB548F173187AC1F381D /* SavedView */ = {
			isa = PBXGroup;
			children = (
				18CDD1C52CA04B0D00D09B24 /* SavedModel.swift */,
				F31E1B4B13DCB0B4B14BAFA8 /* SavedView.swift */,
				DE46CFEB61E7B2682D107471 /* Cell */,
				1D5DCD7809B7E4F0692568A1 /* SavedViewModel.swift */,
			);
			path = SavedView;
			sourceTree = "<group>";
		};
		CDDDDA12B2C020A42383C925 /* Cell */ = {
			isa = PBXGroup;
			children = (
				6E3718B44F3912B8D6684EF0 /* Rowhairextension1Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		D5806F9E480C2BBDCBD49672 /* Ratepopup1View */ = {
			isa = PBXGroup;
			children = (
				426FCD196113C9BBCB75FA78 /* Ratepopup1View.swift */,
				3273704861873543B7CC6E95 /* Ratepopup1ViewModel.swift */,
			);
			path = Ratepopup1View;
			sourceTree = "<group>";
		};
		D6DF6B590A2873B037957C38 /* Onboarding */ = {
			isa = PBXGroup;
			children = (
				18CB62B02B698582009D60CE /* OnboardingModel.swift */,
				D7403D06676355688772804C /* OnboardingView.swift */,
				E11CAE3E2E04A86129449125 /* OnboardingViewModel.swift */,
			);
			path = Onboarding;
			sourceTree = "<group>";
		};
		D7C589974E21419135CDC5B9 /* Components */ = {
			isa = PBXGroup;
			children = (
				181E38442CA19B8300780921 /* Placeholders */,
				18F34D402BDAA4B000A9DDC2 /* AlertComponent */,
				18F34D3D2BDAA44400A9DDC2 /* ActivityLoader */,
				18F34D1A2BD6E15700A9DDC2 /* NetworkImage */,
				18AF03F92BBC1BAB002D374F /* RatingBar */,
				1DB4B653653B29834CA13336 /* RadioGroup */,
				B3FD4AC9ED479DAB9AB0F214 /* FabButton */,
				F1F9EF419804321039F9AFA2 /* FSPagerViewSwiftUI */,
				FB53FCC2A8E61ECB3A253D4B /* PageIndicator */,
				344B6660A71BC894F63EC287 /* BottomSheetView */,
				76A4E9085647BF6D06888E77 /* Progressbar */,
				11899230C0FC05DE6F770522 /* TabAndPager */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		D81AD3E507FB0DCD4CEAAACA /* PrivacyPolicyView */ = {
			isa = PBXGroup;
			children = (
				185A35AC2BEA21C90006CB98 /* RichTextPageModel.swift */,
				9288160833C831BD3F6D29E1 /* CustomRichTextPageView.swift */,
				185A35A72BEA1C3F0006CB98 /* CustomRichTextView.swift */,
				3EFEE4514E87D38A7E3C66C1 /* CustomRichTextPageViewModel.swift */,
			);
			path = PrivacyPolicyView;
			sourceTree = "<group>";
		};
		************************ /* RatepopupsucessmessegeView */ = {
			isa = PBXGroup;
			children = (
				C9CE21518A051F2B15CD4370 /* RatepopupsucessmessegeView.swift */,
				86E79B63F95225578A3F2B26 /* Cell */,
				D25F7B4C4A2567F36745543A /* RatepopupsucessmessegeViewModel.swift */,
			);
			path = RatepopupsucessmessegeView;
			sourceTree = "<group>";
		};
		DE46CFEB61E7B2682D107471 /* Cell */ = {
			isa = PBXGroup;
			children = (
				24A559BF6E241E117A8841C3 /* Rowrectangleten6Cell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		DF2BC6389F54F9765117BC20 /* Resources */ = {
			isa = PBXGroup;
			children = (
				18CD42752D8848D00095378F /* Swiftui Copilot Guidelines .md */,
				1847BCF62CD3F44600020F9F /* Animations */,
				270049F3DF5C3B4A1C550D67 /* Fonts */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		E72407568C22C7F068781EA5 /* ShopDetailsService */ = {
			isa = PBXGroup;
			children = (
				186CFB4D2CC77F6D00117D6C /* VendorServicesCategoryModel.swift */,
				186CFB4B2CC3D1B900117D6C /* TestView.swift */,
				186B7AAC2CABF9230002AE40 /* ShopDetailsStaffView.swift */,
				90833ED202B5286972827F0A /* ShopDetailsServiceView.swift */,
				2D915B1FE0B027A88FECE537 /* Cell */,
				186B7AAE2CABFA300002AE40 /* ShopDetailsStaffViewModel.swift */,
				68C511F3F7A60145B3648116 /* ShopDetailsServiceViewModel.swift */,
			);
			path = ShopDetailsService;
			sourceTree = "<group>";
		};
		EAB9E5136E25A5070C4327F3 /* ExploreView */ = {
			isa = PBXGroup;
			children = (
				181B7F682CB286AC006C17C6 /* FirstResponderTextField.swift */,
				AE8C756033BEE2FD998E9F6E /* ExploreView.swift */,
				65C4354761F4C347EC14963A /* Cell */,
				765657A8F64D3E438ADDD186 /* ExploreViewModel.swift */,
				185EA9572C249255008554FB /* ExploreModel.swift */,
				18AFC0C62C7F103F00DF0F7A /* ExploreShimmerView.swift */,
			);
			path = ExploreView;
			sourceTree = "<group>";
		};
		EB790E7D8F1B3339F5CDD254 /* Cell */ = {
			isa = PBXGroup;
			children = (
				D146F6AFB9B383710422FCF2 /* TitleCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		EFEDE2624399A82E1E774812 /* HelpCentercontactView */ = {
			isa = PBXGroup;
			children = (
				185A35B02BEA46170006CB98 /* ContactUsModel.swift */,
				ADF318545CEA9EDAEBA3CC85 /* ContactUsView.swift */,
				A5F2C66E93DD3CAEC6F45877 /* Cell */,
				FBFB7EF0B9853E64FFF259A8 /* ContactUsViewModel.swift */,
			);
			path = HelpCentercontactView;
			sourceTree = "<group>";
		};
		F1F9EF419804321039F9AFA2 /* FSPagerViewSwiftUI */ = {
			isa = PBXGroup;
			children = (
				FD7FF113C67CFE1174201B01 /* FSPageControlSUI.swift */,
				07DB9F877046866C0AE68342 /* FSPagerViewSUI.swift */,
				3C13A3833F24813541EBA28B /* FSPagerViewSUIOrigin.swift */,
			);
			path = FSPagerViewSwiftUI;
			sourceTree = "<group>";
		};
		F8E547F93CBAF0A983D0ED2A /* LogoutpopupView */ = {
			isa = PBXGroup;
			children = (
				1A85F2790DB4A79E0391C138 /* LogoutPopupView.swift */,
				1485A31D8CD0E3FFA8BE0CE0 /* LogoutpopupViewModel.swift */,
			);
			path = LogoutpopupView;
			sourceTree = "<group>";
		};
		FB53FCC2A8E61ECB3A253D4B /* PageIndicator */ = {
			isa = PBXGroup;
			children = (
				C8115FEAE118E245A91E09B1 /* PageIndicator.swift */,
			);
			path = PageIndicator;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		88ED74E3272FFE6E0088E3EF /* Bookme */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 88ED74F3272FFE6F0088E3EF /* Build configuration list for PBXNativeTarget "Bookme" */;
			buildPhases = (
				88ED74E0272FFE6E0088E3EF /* Sources */,
				88ED74E1272FFE6E0088E3EF /* Frameworks */,
				88ED74E2272FFE6E0088E3EF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Bookme;
			packageProductDependencies = (
				18CB629E2B680F93009D60CE /* Alamofire */,
				18CB62A12B681142009D60CE /* FSPagerView */,
				182CE7012B86896900F2FEF8 /* WrappingHStack */,
				182CE7042B87D6A400F2FEF8 /* MultiSlider */,
				18F34D092BD644FF00A9DDC2 /* SwiftyJSON */,
				18F34D262BD6E31C00A9DDC2 /* SwiftUIImageViewer */,
				18F34D2C2BD6E45900A9DDC2 /* SDWebImageSwiftUI */,
				1816CDCD2BDCFBF90058E6B9 /* SecureDefaults */,
				185A35AA2BEA1C8A0006CB98 /* RichText */,
				1865DB722C79B8C700C7C9D7 /* Shimmer */,
				18EED27D2C90687E00FFBCCA /* FirebaseAuth */,
				18EED2862C9089FD00FFBCCA /* GoogleSignIn */,
				18EED2882C9089FD00FFBCCA /* GoogleSignInSwift */,
				181B7F662CB159D5006C17C6 /* FirebaseMessaging */,
				18F7361B2CCF819400F68F62 /* CropViewController */,
				18F7361D2CCF819400F68F62 /* TOCropViewController */,
				1847BCFA2CD3F49300020F9F /* Lottie */,
				1885E6182D0D7AB600030465 /* FirebaseDynamicLinks */,
				187028112DD8BDBE009C93E6 /* FirebaseAnalytics */,
				187028132DD8BEC8009C93E6 /* FirebaseCrashlytics */,
				187028152DD8C93B009C93E6 /* FirebaseAnalyticsOnDeviceConversion */,
				187028172DD8C93B009C93E6 /* FirebaseAnalyticsWithoutAdIdSupport */,
			);
			productName = Bookme;
			productReference = 88ED74E4272FFE6E0088E3EF /* Bookme.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		88ED74DC272FFE6E0088E3EF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1250;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					88ED74E3272FFE6E0088E3EF = {
						CreatedOnToolsVersion = 12.5;
					};
				};
			};
			buildConfigurationList = 88ED74DF272FFE6E0088E3EF /* Build configuration list for PBXProject "Bookme" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ar,
			);
			mainGroup = 88ED74DB272FFE6E0088E3EF;
			packageReferences = (
				18CB629D2B680F93009D60CE /* XCRemoteSwiftPackageReference "Alamofire" */,
				18CB62A02B681142009D60CE /* XCLocalSwiftPackageReference "FSPagerView-master" */,
				182CE7002B86896900F2FEF8 /* XCLocalSwiftPackageReference "WrappingHStack-main" */,
				182CE7032B87D6A400F2FEF8 /* XCRemoteSwiftPackageReference "MultiSlider" */,
				18F34D082BD644FF00A9DDC2 /* XCRemoteSwiftPackageReference "SwiftyJSON" */,
				18F34D252BD6E31C00A9DDC2 /* XCRemoteSwiftPackageReference "swiftui-image-viewer" */,
				18F34D2B2BD6E45900A9DDC2 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
				1816CDCC2BDCFBF90058E6B9 /* XCRemoteSwiftPackageReference "SecureDefaults" */,
				185A35A92BEA1C8A0006CB98 /* XCRemoteSwiftPackageReference "RichText" */,
				1865DB712C79B8C700C7C9D7 /* XCRemoteSwiftPackageReference "SwiftUI-Shimmer" */,
				18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				18EED2852C9089FD00FFBCCA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
				18F7361A2CCF819400F68F62 /* XCRemoteSwiftPackageReference "TOCropViewController" */,
				1847BCF92CD3F49300020F9F /* XCRemoteSwiftPackageReference "lottie-ios" */,
			);
			productRefGroup = 88ED74E5272FFE6E0088E3EF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				88ED74E3272FFE6E0088E3EF /* Bookme */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		88ED74E2272FFE6E0088E3EF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1847BCF82CD3F45800020F9F /* finsh-loader.mov.lottie.json in Resources */,
				18C2A5BD2D67C2ED001AE292 /* Message.apns in Resources */,
				88ED74EF272FFE6F0088E3EF /* Preview Assets.xcassets in Resources */,
				18CD42762D8848D30095378F /* Swiftui Copilot Guidelines .md in Resources */,
				88ED74EC272FFE6F0088E3EF /* Assets.xcassets in Resources */,
				18D44EA72D16FCD100A2C54A /* apple-app-site-association.json in Resources */,
				E55D0A2C56160AAEC0EC813B /* NunitoBold.ttf in Resources */,
				210D8779D1C4DF5572C9C58C /* MontserratSemiBold.ttf in Resources */,
				1849D2282DAE90990053CB32 /* assetlinks.json in Resources */,
				4D83A38F0D3CDB679460A511 /* NunitoSemiBold.ttf in Resources */,
				188221F12CF50C30005FB2C9 /* Localizable.xcstrings in Resources */,
				187FEECD2CD60CF400EAC51C /* logo.animation.json in Resources */,
				18B0871D2D099F0F001F6DE8 /* InfoPlist.xcstrings in Resources */,
				E20448A0F6F5690DB4BDE75F /* NunitoExtraBold.ttf in Resources */,
				DF988DCC80FEC1C5328676A2 /* MontserratMedium.ttf in Resources */,
				F849F1E0D8486804CB70B257 /* NunitoMedium.ttf in Resources */,
				FA9A51A0DB62CE7D45E98F3A /* NunitoRegular.ttf in Resources */,
				189258D62D17214D00EB1D1D /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		88ED74E0272FFE6E0088E3EF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				888CB47527686A000041116C /* APIExtensions.swift in Sources */,
				8853052A27718E2D00B04E6F /* EncodableExtension.swift in Sources */,
				8853052C2771949600B04E6F /* UINavigationController.swift in Sources */,
				186CFB4C2CC3D1BE00117D6C /* TestView.swift in Sources */,
				18AFC0CD2C82FE0700DF0F7A /* NotificationShimmerView.swift in Sources */,
				185886CC2BBC0D3100650239 /* AppointmentCancelledCell.swift in Sources */,
				187FEEC92CD55C5600EAC51C /* NotificationSettingsModel.swift in Sources */,
				18F34D422BDAA4C400A9DDC2 /* AlertView.swift in Sources */,
				7F44DBA127422A5D00171570 /* ActivityLoader.swift in Sources */,
				188002E72BDEA27C007375B0 /* ImagePicker.swift in Sources */,
				8853052627718DC500B04E6F /* ViewExtensions.swift in Sources */,
				186DC60E2BE7F3EA00B24DC2 /* ColorExtension.swift in Sources */,
				88ED7500272FFECA0088E3EF /* Utilities.swift in Sources */,
				88ED7504272FFECA0088E3EF /* BookmeApp.swift in Sources */,
				C40AB9F82754D79E006BC5D7 /* AppDelegate.swift in Sources */,
				185B68092CBD8EDF00D3DC0D /* MapLocationManager.swift in Sources */,
				18F34D3F2BDAA45300A9DDC2 /* ActivityLoaderView.swift in Sources */,
				88680D1C2775C601002E964F /* ViewportHelper.swift in Sources */,
				88ED7501272FFECA0088E3EF /* AppConstants.swift in Sources */,
				185B680F2CC3935800D3DC0D /* TopStaffCell.swift in Sources */,
				1847303B2C76411C006385D2 /* ForgotPasswordViewModel.swift in Sources */,
				18BD0A2C2D7B18E800AB65A0 /* PasskeyManager.swift in Sources */,
				18EED29F2C95AED200FFBCCA /* RowellipseCell.swift in Sources */,
				18D44EA52D16F8B000A2C54A /* DeepLinkManager.swift in Sources */,
				188222072CF70BDB005FB2C9 /* UPaymentView.swift in Sources */,
				185886D32BBC11E700650239 /* WriteReviewModel.swift in Sources */,
				18DA00872B9F36DF00AAFC10 /* TestCell.swift in Sources */,
				884F3E5127B0E65A00963FC4 /* RoundedCornersView.swift in Sources */,
				CF869B57967BB927F1AD1A72 /* StringScheme.swift in Sources */,
				5E2FCB0750D9D03933041531 /* ColorConstants.swift in Sources */,
				9F6C4D86D1E16F66DB1489F2 /* FontScheme.swift in Sources */,
				1047D3638CBD7560E5C3B584 /* RatepopupOneView.swift in Sources */,
				97D0E0878C7D3E84A3750AC2 /* ArtistitemCell.swift in Sources */,
				A70E760D933EC24C4D688BA8 /* RowrectangletenCell.swift in Sources */,
				18B0871F2D09B775001F6DE8 /* LanguageView.swift in Sources */,
				5B469FF9DAC75BD2B482DC42 /* RadiogroupgreatexperiencCell.swift in Sources */,
				CAF7AB57581AE54EEFB4116E /* ExploreView.swift in Sources */,
				CD96B0AEFAAC19B3D01012ED /* Rowrectangleten1Cell.swift in Sources */,
				18F34D3C2BDAA28400A9DDC2 /* SuperView.swift in Sources */,
				126AD1DB0B3AE8B9EC93E9D3 /* AppointmentView.swift in Sources */,
				185A35B12BEA46170006CB98 /* ContactUsModel.swift in Sources */,
				8D2A36ECF704BA091E494744 /* AppointmentUpcomingCell.swift in Sources */,
				E33DD65390BFF0A8316CEE7B /* ColorpatternView.swift in Sources */,
				1847303D2C764151006385D2 /* ForgotPasswordView.swift in Sources */,
				185886CA2BBC0B2100650239 /* AppointmentCompletedCell.swift in Sources */,
				186DC6152BE80ECC00B24DC2 /* AddressViewModel.swift in Sources */,
				63325934A4D0961AF6BCC90F /* TitleCell.swift in Sources */,
				18CB62AF2B6908D3009D60CE /* MainView.swift in Sources */,
				185B68132CC3A6C700D3DC0D /* DateExtensions.swift in Sources */,
				18E7791E2D35995B000E18CB /* ReminderNotificationModel.swift in Sources */,
				185886CF2BBC117600650239 /* WriteReviewView.swift in Sources */,
				BD13B2F2A5032D3CF1D84523 /* SplashView.swift in Sources */,
				BFA10541194B92DDBF66B85A /* WelcomeView.swift in Sources */,
				1110C005B096E7091425B900 /* OnboardingView.swift in Sources */,
				BBD886C7E7CB6726AA294B19 /* SelectionView.swift in Sources */,
				186DC6062BE7C18B00B24DC2 /* NotificationView.swift in Sources */,
				9A8A6154E279E77F4DFE87F0 /* HomeView.swift in Sources */,
				9B86A8D3E1E73B0E9E532ED9 /* Artistitem1Cell.swift in Sources */,
				2FC40198A19702AD8FB56302 /* SaloonCardCell.swift in Sources */,
				84125FF1D4ED023A0F61D23F /* SaloonMapView.swift in Sources */,
				73F326246FEA8A53D638DD8C /* MapPinCell.swift in Sources */,
				3629C2EC4D952E82C35F18FA /* MapviewpopupView.swift in Sources */,
				1816CDCB2BDCFBBE0058E6B9 /* UserDefaults.swift in Sources */,
				B5363FB492F3E0041D02A637 /* MapPin1Cell.swift in Sources */,
				A2F36A5B070A05DE082F336F /* MapSaloonCardCell.swift in Sources */,
				1983ACFC4629B1241A636620 /* FilterView.swift in Sources */,
				BC9D9B3C48B9B10CC6AC680A /* RowserviceiteCell.swift in Sources */,
				185EA9562C220433008554FB /* BookingAppointmentModel.swift in Sources */,
				834FC997934F3D17346387AF /* RowtimeCell.swift in Sources */,
				CD3C71184BD9E9B63F433714 /* ShopDetailsServiceView.swift in Sources */,
				7502F468DC080EF3B16F990B /* RowhairextensionCell.swift in Sources */,
				159035D8E33D4DEC1AED3AE0 /* Shopdetails1View.swift in Sources */,
				18EED2A12C95B87000FFBCCA /* PaymentCardView.swift in Sources */,
				2D8D750A363486202CE37482 /* Rowhairextension1Cell.swift in Sources */,
				18EED2842C90899500FFBCCA /* AuthService.swift in Sources */,
				18CB62B12B698582009D60CE /* OnboardingModel.swift in Sources */,
				A8971A7BE2045C78072665B6 /* ShopDetailsView.swift in Sources */,
				116BD8B070E76CF500A762A2 /* ShopdetailsabouutView.swift in Sources */,
				9A9D4A55AD18BF379E015198 /* ShopdetailsReviewView.swift in Sources */,
				18CB62B32B699E00009D60CE /* CalendarView.swift in Sources */,
				64FC034D0CD430DB61CD8C42 /* RateiteCell.swift in Sources */,
				D8A6C4DB8BCBDEDA4443390A /* ColumnellipseCell.swift in Sources */,
				B74A17608C377E375600B188 /* ShopdetailsPortfolioView.swift in Sources */,
				FAF5353167C17087434E39FE /* Stackrectangle4024oneCell.swift in Sources */,
				187FEEE22CE66AD900EAC51C /* StringExtension.swift in Sources */,
				18931CFF2CA4253000E258DA /* DocumentPicker.swift in Sources */,
				185EA9582C249255008554FB /* ExploreModel.swift in Sources */,
				186DC60C2BE7F31900B24DC2 /* NotificationCellView.swift in Sources */,
				E7F6276FFFD38433D4F9E413 /* BookAppointmentView.swift in Sources */,
				18EED29B2C959E9800FFBCCA /* AddCardViewModel.swift in Sources */,
				187FEECB2CD57B7900EAC51C /* RegistrationOtpView.swift in Sources */,
				1816CDC52BDBC2830058E6B9 /* TabViewDynamicHeight.swift in Sources */,
				CBAB2C092762A53A174661E4 /* RowdateitemCell.swift in Sources */,
				6EAD04CDBFE4511BA7D375DB /* SignInView.swift in Sources */,
				181B7F692CB286B2006C17C6 /* FirstResponderTextField.swift in Sources */,
				C0111C49BB6C5D1E396772DC /* MyAccountView.swift in Sources */,
				00C10FA1E67623F5088681F9 /* CancelModifyView.swift in Sources */,
				41528372BDB9C4E72DAA411D /* RowbookingdateCell.swift in Sources */,
				2D1962DD7D125090A07B0D0E /* RowkdcounterCell.swift in Sources */,
				2EA45C1F6BC4AD1357AC8AA8 /* DateandtimeView.swift in Sources */,
				98EF95080962DE2F9C75ED13 /* Rowbookingdate1Cell.swift in Sources */,
				186DC6112BE8062000B24DC2 /* AddressListingView.swift in Sources */,
				A017D07D16BBDB5DE2B3B5A6 /* Rowkdcounter1Cell.swift in Sources */,
				C6D943573292DC0E9C311CA9 /* Rowdateitem1Cell.swift in Sources */,
				1815DE542BC166D600FEEF15 /* FAQSampleModel.swift in Sources */,
				187FEEE02CE6625200EAC51C /* PaymentGatewayViewModel.swift in Sources */,
				186BC7842C99DEBE00DA5131 /* BookingSuccessView.swift in Sources */,
				186DC6082BE7C19A00B24DC2 /* NotificationModel.swift in Sources */,
				07D829807E13C6C293612640 /* TimeView.swift in Sources */,
				18E7791A2D358B8B000E18CB /* NotificationReminderView.swift in Sources */,
				B2606D7786DECBBBB62960F2 /* Rowbookingdate2Cell.swift in Sources */,
				5616DFBEA02EA7205826E97D /* Rowkdcounter2Cell.swift in Sources */,
				4C48B701B41E0DDEF1F43D40 /* Rowtime1Cell.swift in Sources */,
				185A35A12BE8BE720006CB98 /* AddAddressModel.swift in Sources */,
				186DC6192BE817A400B24DC2 /* AddAddressViewModel.swift in Sources */,
				43182E504FDC1CFB41B45ED6 /* AppoinmentcompleteView.swift in Sources */,
				186DC6132BE8076700B24DC2 /* AddressCellView.swift in Sources */,
				186B7AAD2CABF9310002AE40 /* ShopDetailsStaffView.swift in Sources */,
				5298DF0A2850C4B0ECCD2613 /* Appoinmentitem1Cell.swift in Sources */,
				A1A26475F6AAC296AB0D3308 /* AppoinmentcancelledView.swift in Sources */,
				187901F22C04532C00355246 /* MonthPopUpView.swift in Sources */,
				18F34D172BD64D3E00A9DDC2 /* SuperViewModel.swift in Sources */,
				0F458805D385474EE6665122 /* Appoinmentitem2Cell.swift in Sources */,
				8FEAD052ECCFC9B6FA6101B2 /* AddreviewView.swift in Sources */,
				185886D12BBC11C700650239 /* WriteReviewViewModel.swift in Sources */,
				E0B7432D7FA4DF7B6A51D947 /* AddreviewTwoView.swift in Sources */,
				5B1BA54538DACD9A566B3154 /* Radiogroupgreatexperienc1Cell.swift in Sources */,
				4A8276BAEBBDEFAD3F1C13EB /* AddreviewThreeView.swift in Sources */,
				E53A1FB1A878F1245726A862 /* AddreviewFourOneView.swift in Sources */,
				0AD9EFE7B6017F20C8D80A32 /* AddreviewFour1View.swift in Sources */,
				A2C0A6C738507ABB6FB3B136 /* AddreviewFour2View.swift in Sources */,
				9E5D416BED9E84A4C31289DC /* Appoinmentitem3Cell.swift in Sources */,
				18CB62A92B690499009D60CE /* AppState.swift in Sources */,
				88713F7EDC52F7B4529FF12A /* EReceiptView.swift in Sources */,
				1865DB752C79C7FF00C7C9D7 /* FilterShimmerView.swift in Sources */,
				185A35B72BEB829B0006CB98 /* BookAppoinmentModel.swift in Sources */,
				2B70C300B1694B9D70AAEBA8 /* Ratepopup1View.swift in Sources */,
				31B33BB528A4F575CF94C495 /* RatepopupTwoView.swift in Sources */,
				18AF03FB2BBC1BBA002D374F /* RatingBarView.swift in Sources */,
				18F736202CCF959300F68F62 /* UserNotificationSettingsView.swift in Sources */,
				9FAA0717444F72BA60E99CDA /* Artistitem2Cell.swift in Sources */,
				132A95182FEBB004EB399E19 /* Rowrectangleten3Cell.swift in Sources */,
				186DC60A2BE7C1A600B24DC2 /* NotificationViewModel.swift in Sources */,
				8C66FFAEF7E090B4400F7BF3 /* RatepopupThreeView.swift in Sources */,
				032E34BFA02E32CC4B138E00 /* Artistitem3Cell.swift in Sources */,
				185A35AD2BEA21C90006CB98 /* RichTextPageModel.swift in Sources */,
				83D1D9F878928A0985393DB1 /* Rowrectangleten4Cell.swift in Sources */,
				8054DCD3CF06C9D47E75F5F7 /* RatepopupsucessmessegeView.swift in Sources */,
				8CA9E7457E6431D886A8F908 /* Artistitem4Cell.swift in Sources */,
				185B680B2CBD8FAF00D3DC0D /* LocationDeniedView.swift in Sources */,
				1865DB6A2C76478200C7C9D7 /* ForgotPasswordOtpView.swift in Sources */,
				18CDD1C62CA04B1100D09B24 /* SavedModel.swift in Sources */,
				836A6B5E3DD11543BCDF6848 /* Rowrectangleten5Cell.swift in Sources */,
				54308AC9E019614EE86FB929 /* ProfileView.swift in Sources */,
				187FEEDC2CE654AF00EAC51C /* WebView.swift in Sources */,
				BAF5D10F666BB956E9AF0033 /* AddAddressView.swift in Sources */,
				43E617696CBB9DCC47E490F6 /* PaymentMethodView.swift in Sources */,
				8C2A13387EE5A6F281CF10B4 /* InputCell.swift in Sources */,
				186CFB4E2CC77F7B00117D6C /* VendorServicesCategoryModel.swift in Sources */,
				306EC6088B14C238881EADE3 /* SavedView.swift in Sources */,
				18F34D0E2BD645F100A9DDC2 /* RepositoriesNetworking.swift in Sources */,
				1865DB6E2C76511A00C7C9D7 /* ChangePasswordView.swift in Sources */,
				185B68112CC3A68600D3DC0D /* CustomBottomNavigationButton.swift in Sources */,
				9F22231DE803B097647C2970 /* Rowrectangleten6Cell.swift in Sources */,
				E8059A0999F8BD8A2AB38901 /* SettingsView.swift in Sources */,
				01F6BD2791E89A48E4B41288 /* TransactionView.swift in Sources */,
				18F34D312BD7AF1A00A9DDC2 /* FiltersModel.swift in Sources */,
				18F022612B97209200A35420 /* MainScrollBody.swift in Sources */,
				49BFF16875A4A65573F8AFB1 /* TransactionareCell.swift in Sources */,
				36254C7F306BA2E481E47416 /* Transactionare1Cell.swift in Sources */,
				8E54D7F251791AC34495E880 /* FAQView.swift in Sources */,
				DD19CEC00D196DB9FB9DF541 /* ContactUsView.swift in Sources */,
				18CD42742D88401C0095378F /* ShopDetailsShimmerView.swift in Sources */,
				186094122B6BE01900B3C95F /* DashboardViewModel.swift in Sources */,
				0207F0FEE04772A23C9F1DFD /* FaqitemCell.swift in Sources */,
				A36A9F47075E5075E600524E /* HelpCenterView.swift in Sources */,
				18AFC0C52C7CE35500DF0F7A /* SideMenu.swift in Sources */,
				5C1E33A320A528192141730C /* CustomRichTextPageView.swift in Sources */,
				36F5DD0723D3C85A550E89CD /* PasswordManagerView.swift in Sources */,
				C5EAEC1D503F0B156EEB2341 /* LogoutPopupView.swift in Sources */,
				595BFB266B1BFC108022CBA9 /* RatepopupOneContainerView.swift in Sources */,
				18EED2982C959E3B00FFBCCA /* AddCardView.swift in Sources */,
				F729FC0835F88CE2665A5D1F /* RadioGroupButton.swift in Sources */,
				D5A965CC8BF283643E6D921F /* FabButton.swift in Sources */,
				187FEEDE2CE6611C00EAC51C /* PaymentGatewayView.swift in Sources */,
				558C7FAB6E469F3921D173A4 /* FSPageControlSUI.swift in Sources */,
				18CB62BA2B6BD9B4009D60CE /* DashboardView.swift in Sources */,
				75ECD3F08DF4A284FC85A777 /* FSPagerViewSUI.swift in Sources */,
				3B1C9B6716D81781AC3ED0FB /* FSPagerViewSUIOrigin.swift in Sources */,
				187FEEE62CF1ED9500EAC51C /* PaymentCardViewModel.swift in Sources */,
				182FCE5E2BDFA5F7000C2717 /* SignUpView.swift in Sources */,
				18AFC0D12C831E6500DF0F7A /* AppointmentShimmerView.swift in Sources */,
				44D12CFA90F7EFFE9393B9C7 /* PageIndicator.swift in Sources */,
				53A68D0B2B59F4277070669A /* BottomSheetView.swift in Sources */,
				18F34D072BD644B900A9DDC2 /* BaseAPI.swift in Sources */,
				18E779202D35A7E6000E18CB /* ServiceForView.swift in Sources */,
				D026A679491032BDB0F41742 /* LinearProgress.swift in Sources */,
				18F736192CCF74BC00F68F62 /* UIApplicationExtension.swift in Sources */,
				49F533124CD42879758E4EB9 /* CircularProgress.swift in Sources */,
				21DD9798F6B1ECA6843618BE /* TabsView.swift in Sources */,
				18AFC0CF2C8300E700DF0F7A /* AddressListingShimmerView.swift in Sources */,
				185A35A82BEA1C3F0006CB98 /* CustomRichTextView.swift in Sources */,
				D7E6B96481AAC855955916BB /* PagerView.swift in Sources */,
				2CED9F0E3879CB4D10917B3D /* Validation.swift in Sources */,
				188002E42BDE661B007375B0 /* ProfileModel.swift in Sources */,
				B9D54E170D550A3430B2EAB4 /* OnboardingViewModel.swift in Sources */,
				176E2A9811A78DD27601E5EE /* ContactUsViewModel.swift in Sources */,
				1865DB702C76544400C7C9D7 /* ChangePasswordSuccessView.swift in Sources */,
				BD8AB948A908E98B9F95EA1A /* RatepopupsucessmessegeViewModel.swift in Sources */,
				182CE7072B87D6D900F2FEF8 /* SliderView.swift in Sources */,
				1E64A774ECF735645D8E7B66 /* AddreviewViewModel.swift in Sources */,
				0C4D2F22C87D67EBAA5FF9FE /* HomeViewModel.swift in Sources */,
				18F34D102BD6464500A9DDC2 /* RepositoriesAPI.swift in Sources */,
				185A35AF2BEA29BC0006CB98 /* MyAccountModel.swift in Sources */,
				50ABDBA88D796907E8E6CF53 /* WelcomeViewModel.swift in Sources */,
				18AFC0C72C7F103F00DF0F7A /* ExploreShimmerView.swift in Sources */,
				0E110BEE52D72B975F71A79A /* TimeViewModel.swift in Sources */,
				************************ /* ProfileViewModel.swift in Sources */,
				186CFB532CC8365C00117D6C /* DebounceHelper.swift in Sources */,
				9A7F2979A10EBD8288FB250E /* PaymentmethodeViewModel.swift in Sources */,
				124A56BED185E6CA3A3C38CE /* Shopdetails1ViewModel.swift in Sources */,
				181E38492CA19BB000780921 /* CustomPlaceholder.swift in Sources */,
				18EED29D2C95AE7300FFBCCA /* UserPaymentCardView.swift in Sources */,
				586179A486CFC7CB7F7132A9 /* SavedViewModel.swift in Sources */,
				1FB4284313F4B2856F39B01D /* RatepopupTwoViewModel.swift in Sources */,
				185A35B32BEA514E0006CB98 /* TransactionHistoryModel.swift in Sources */,
				906B3B37ECF189D144E069A4 /* ShopdetailsReviewViewModel.swift in Sources */,
				181E384D2CA1E99100780921 /* CustomDatePicker.swift in Sources */,
				188222052CF70BB8005FB2C9 /* UPaymentCardView.swift in Sources */,
				E9829341F59C89448D066B96 /* FAQViewModel.swift in Sources */,
				2FBC1041B6E3C46E5171978C /* SaloonMapViewModel.swift in Sources */,
				0415DA7236FBE0A59DA9AC3C /* EReceptViewModel.swift in Sources */,
				14A6D8B2B9B51F4B66EB7649 /* DateandtimeViewModel.swift in Sources */,
				186DC6172BE8132600B24DC2 /* AddressModel.swift in Sources */,
				18F34D192BD6D8A200A9DDC2 /* HomeModel.swift in Sources */,
				82047A302ACF6F11D6303A5F /* AuthViewModel.swift in Sources */,
				3A066F7A85F7E353A90A78EF /* AppoinmentcancelledViewModel.swift in Sources */,
				18F34D0C2BD6456A00A9DDC2 /* APIEndPoints.swift in Sources */,
				185B680D2CC1A82D00D3DC0D /* WrappingHStack.swift in Sources */,
				18E7791C2D358CE4000E18CB /* NotificationReminderViewModel.swift in Sources */,
				68C4C027A49F504336839ACD /* CancelmodifyViewModel.swift in Sources */,
				18CD427C2D9125850095378F /* NetworkMonitor.swift in Sources */,
				186094142B6BE15100B3C95F /* Route.swift in Sources */,
				************************ /* ShopdetailsPortfolioViewModel.swift in Sources */,
				1816CDD02BDCFC6D0058E6B9 /* UserModel.swift in Sources */,
				186094102B6BDB9800B3C95F /* TabModel.swift in Sources */,
				C71E1440D4BAE5D375687046 /* SelectionViewModel.swift in Sources */,
				************************ /* MyAccountViewModel.swift in Sources */,
				18F34D052BD6432500A9DDC2 /* TargetType.swift in Sources */,
				E4548BCE4B3FAA7EB93BEAB5 /* AddreviewFour2ViewModel.swift in Sources */,
				950B32A222CD48173D082E95 /* AddreviewTwoViewModel.swift in Sources */,
				18F34D2F2BD6E62A00A9DDC2 /* LocationManager.swift in Sources */,
				19A9C77DDDE3E67513E3D5B9 /* FilterViewModel.swift in Sources */,
				188222092CF70C1A005FB2C9 /* LoadingView.swift in Sources */,
				89A2D0763C65B5068535D176 /* SettingsViewModel.swift in Sources */,
				185A35B52BEAA6BC0006CB98 /* ShopDetailsModel.swift in Sources */,
				8B8DD373F4144012D02DA4EE /* AppointmentViewModel.swift in Sources */,
				187FEEDA2CE3C24700EAC51C /* CheckoutView.swift in Sources */,
				9133420B65690330F64696CF /* LogoutpopupViewModel.swift in Sources */,
				79DDAA66F77B110B8DD16A20 /* AddreviewFourOneViewModel.swift in Sources */,
				185A35A62BE933900006CB98 /* SaloonMapModel.swift in Sources */,
				18AFC0C92C804FC300DF0F7A /* SaloonMapShimmerView.swift in Sources */,
				B593F9CC5D7330BA623FC257 /* BookAppointmentViewModel.swift in Sources */,
				18F34D152BD646FF00A9DDC2 /* SuperModel.swift in Sources */,
				36C5CA1D7CC14CB621CF4FB5 /* MapviewpopupViewModel.swift in Sources */,
				833E7A68A14C4EAD3CF2DA19 /* ShopDetailsServiceViewModel.swift in Sources */,
				18F736222CD2425B00F68F62 /* AppStorageKey.swift in Sources */,
				BD89359BFC7DA8C6F95E2CC2 /* TransactionViewModel.swift in Sources */,
				1865DB6C2C7648FA00C7C9D7 /* OtpFormFieldView.swift in Sources */,
				************************ /* AppoinmentcompleteViewModel.swift in Sources */,
				18AFC0CB2C823AA600DF0F7A /* ForgotPasswordModel.swift in Sources */,
				8FD97F2F06E662F4E51C7880 /* RatepopupOneContainerViewModel.swift in Sources */,
				187FEEE42CF1E9AA00EAC51C /* PaymentCardModel.swift in Sources */,
				A0FB68FEA62396F462B81807 /* ColorpatternViewModel.swift in Sources */,
				090E1B178583CD80B995DD38 /* AddreviewThreeViewModel.swift in Sources */,
				699C73A5AC26A403D37F9890 /* HelpCenterViewModel.swift in Sources */,
				FC1E09A290A75356B659D1F9 /* ExploreViewModel.swift in Sources */,
				2C0EE0C358BEC50F25C3FCAE /* PasswordManagerViewModel.swift in Sources */,
				186CFB502CC7C6D700117D6C /* CartItemCellView.swift in Sources */,
				18F735D22CCBD2D600F68F62 /* MainCategoryView.swift in Sources */,
				68679CE88ACED714D2CE6615 /* ShopDetailsViewModel.swift in Sources */,
				18F34D1C2BD6E16400A9DDC2 /* NetworkImageView.swift in Sources */,
				18F34D332BD7C41F00A9DDC2 /* VendorDetailsModel.swift in Sources */,
				A1F9666C5F318DF07449BDFF /* AddreviewFour1ViewModel.swift in Sources */,
				18566E9B2BBAB0600050A98B /* TabExtension.swift in Sources */,
				7F4D9C5690C2869AA238DECE /* CustomRichTextPageViewModel.swift in Sources */,
				189A966C2DB66D5100BF6663 /* LottieView.swift in Sources */,
				796D769D4EC556D2FF247E48 /* SplashViewModel.swift in Sources */,
				187ACFBB610D147221EB1F53 /* ShopdetailsabouutViewModel.swift in Sources */,
				4F7F1C28A86E08BB8AD17D60 /* ProfileOneViewModel.swift in Sources */,
				27B56B35101E9B8E8D1537E9 /* Ratepopup1ViewModel.swift in Sources */,
				18CB62AC2B690800009D60CE /* RouterManager.swift in Sources */,
				186B7AAF2CABFA320002AE40 /* ShopDetailsStaffViewModel.swift in Sources */,
				FE6188DF61A5ABD8226FCAB7 /* RatepopupOneViewModel.swift in Sources */,
				4ED125557E92791F9B12ABED /* RatepopupThreeViewModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		88ED74F1272FFE6F0088E3EF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		88ED74F2272FFE6F0088E3EF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		88ED74F4272FFE6F0088E3EF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Bookme/Bookme.entitlements;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "\"Bookme/Preview Content\"";
				DEVELOPMENT_TEAM = J3YSJK895Z;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Bookme/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = BookMe;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.book.me.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		88ED74F5272FFE6F0088E3EF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Bookme/Bookme.entitlements;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "\"Bookme/Preview Content\"";
				DEVELOPMENT_TEAM = J3YSJK895Z;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Bookme/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = BookMe;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.book.me.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		88ED74DF272FFE6E0088E3EF /* Build configuration list for PBXProject "Bookme" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88ED74F1272FFE6F0088E3EF /* Debug */,
				88ED74F2272FFE6F0088E3EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		88ED74F3272FFE6F0088E3EF /* Build configuration list for PBXNativeTarget "Bookme" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				88ED74F4272FFE6F0088E3EF /* Debug */,
				88ED74F5272FFE6F0088E3EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		182CE7002B86896900F2FEF8 /* XCLocalSwiftPackageReference "WrappingHStack-main" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "WrappingHStack-main";
		};
		18CB62A02B681142009D60CE /* XCLocalSwiftPackageReference "FSPagerView-master" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "FSPagerView-master";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		1816CDCC2BDCFBF90058E6B9 /* XCRemoteSwiftPackageReference "SecureDefaults" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/vpeschenkov/SecureDefaults.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.2;
			};
		};
		182CE7032B87D6A400F2FEF8 /* XCRemoteSwiftPackageReference "MultiSlider" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/yonat/MultiSlider.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.1.2;
			};
		};
		1847BCF92CD3F49300020F9F /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.5.0;
			};
		};
		185A35A92BEA1C8A0006CB98 /* XCRemoteSwiftPackageReference "RichText" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/NuPlay/RichText.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
		1865DB712C79B8C700C7C9D7 /* XCRemoteSwiftPackageReference "SwiftUI-Shimmer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/markiv/SwiftUI-Shimmer.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.5.1;
			};
		};
		18CB629D2B680F93009D60CE /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.8.1;
			};
		};
		18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.1.0;
			};
		};
		18EED2852C9089FD00FFBCCA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		18F34D082BD644FF00A9DDC2 /* XCRemoteSwiftPackageReference "SwiftyJSON" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftyJSON/SwiftyJSON.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.2;
			};
		};
		18F34D252BD6E31C00A9DDC2 /* XCRemoteSwiftPackageReference "swiftui-image-viewer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/fuzzzlove/swiftui-image-viewer.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.0.0;
			};
		};
		18F34D2B2BD6E45900A9DDC2 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.2.6;
			};
		};
		18F7361A2CCF819400F68F62 /* XCRemoteSwiftPackageReference "TOCropViewController" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/TimOliver/TOCropViewController.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.7.4;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		1816CDCD2BDCFBF90058E6B9 /* SecureDefaults */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1816CDCC2BDCFBF90058E6B9 /* XCRemoteSwiftPackageReference "SecureDefaults" */;
			productName = SecureDefaults;
		};
		181B7F662CB159D5006C17C6 /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		182CE7012B86896900F2FEF8 /* WrappingHStack */ = {
			isa = XCSwiftPackageProductDependency;
			productName = WrappingHStack;
		};
		182CE7042B87D6A400F2FEF8 /* MultiSlider */ = {
			isa = XCSwiftPackageProductDependency;
			package = 182CE7032B87D6A400F2FEF8 /* XCRemoteSwiftPackageReference "MultiSlider" */;
			productName = MultiSlider;
		};
		1847BCFA2CD3F49300020F9F /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1847BCF92CD3F49300020F9F /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};
		185A35AA2BEA1C8A0006CB98 /* RichText */ = {
			isa = XCSwiftPackageProductDependency;
			package = 185A35A92BEA1C8A0006CB98 /* XCRemoteSwiftPackageReference "RichText" */;
			productName = RichText;
		};
		1865DB722C79B8C700C7C9D7 /* Shimmer */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1865DB712C79B8C700C7C9D7 /* XCRemoteSwiftPackageReference "SwiftUI-Shimmer" */;
			productName = Shimmer;
		};
		187028112DD8BDBE009C93E6 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		187028132DD8BEC8009C93E6 /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCrashlytics;
		};
		187028152DD8C93B009C93E6 /* FirebaseAnalyticsOnDeviceConversion */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsOnDeviceConversion;
		};
		187028172DD8C93B009C93E6 /* FirebaseAnalyticsWithoutAdIdSupport */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsWithoutAdIdSupport;
		};
		1885E6182D0D7AB600030465 /* FirebaseDynamicLinks */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseDynamicLinks;
		};
		18CB629E2B680F93009D60CE /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18CB629D2B680F93009D60CE /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		18CB62A12B681142009D60CE /* FSPagerView */ = {
			isa = XCSwiftPackageProductDependency;
			productName = FSPagerView;
		};
		18EED27D2C90687E00FFBCCA /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED27C2C90687E00FFBCCA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		18EED2862C9089FD00FFBCCA /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED2852C9089FD00FFBCCA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		18EED2882C9089FD00FFBCCA /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18EED2852C9089FD00FFBCCA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		18F34D092BD644FF00A9DDC2 /* SwiftyJSON */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F34D082BD644FF00A9DDC2 /* XCRemoteSwiftPackageReference "SwiftyJSON" */;
			productName = SwiftyJSON;
		};
		18F34D262BD6E31C00A9DDC2 /* SwiftUIImageViewer */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F34D252BD6E31C00A9DDC2 /* XCRemoteSwiftPackageReference "swiftui-image-viewer" */;
			productName = SwiftUIImageViewer;
		};
		18F34D2C2BD6E45900A9DDC2 /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F34D2B2BD6E45900A9DDC2 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
		18F7361B2CCF819400F68F62 /* CropViewController */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F7361A2CCF819400F68F62 /* XCRemoteSwiftPackageReference "TOCropViewController" */;
			productName = CropViewController;
		};
		18F7361D2CCF819400F68F62 /* TOCropViewController */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18F7361A2CCF819400F68F62 /* XCRemoteSwiftPackageReference "TOCropViewController" */;
			productName = TOCropViewController;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 88ED74DC272FFE6E0088E3EF /* Project object */;
}
