# Custom Instructions for Copilot 

## 🔹 Core Principles
- **Single File Modifications** 📝
  - All changes must remain within the original file
  - No external file creation or dependencies
  - Preserve all existing functionality and data
  - Maintain original business logic integrity

## 🔹 Code Organization
- **View Structure** 📐
  ```swift
  struct MainView: View {
      // 1. Properties
      @State private var viewState
      @EnvironmentObject private var viewModel
      
      // 2. Computed Properties
      private var derivedData
      
      // 3. Body Implementation
      var body: some View {
          content
      }
      
      // 4. Helper Views (within same file)
      private struct SubView: View
      
      // 5. Helper Methods
      private func helperMethod()
  }
  ```

- **Component Hierarchy** 🎯
  - Break large views into:
    1. Main container view
    2. Feature-specific subviews
    3. Reusable UI components
    4. Helper methods and extensions

## 🔹 SwiftUI Best Practices
- **State Management** 💾
  ```swift
  // Prefer
  @State private var localState
  @StateObject private var viewModel
  @EnvironmentObject private var appState
  
  // Avoid
  @ObservedObject in view creation
  var mutableState
  ```

- **Performance Optimizations** ⚡️
  - Use `@ViewBuilder` for conditional views
  - Implement `Equatable` for custom views
  - Leverage `LazyVStack` and `LazyHGrid`
  - Apply `task` modifier for async operations

## 🔹 View Modifiers
- **Custom Modifiers** 🎨
  ```swift
  extension View {
      func customModifier() -> some View {
          self.modifier(CustomViewModifier())
      }
  }
  ```

## 🔹 Error Handling
- **Structured Error Flow** 🚨
  ```swift
  enum ViewError: LocalizedError {
      case dataFailure
      case networkError
      
      var errorDescription: String? {
          switch self {
              case .dataFailure: "Data loading failed"
              case .networkError: "Network unavailable"
          }
      }
  }
  ```

## 🔹 Testing Considerations
- **ViewModifier Testing** 🧪
  ```swift
  // Make views testable without external dependencies
  struct ContentView: View {
      let dataProvider: DataProviding // Protocol
      
      var body: some View {
          // Implementation
      }
  }
  ```

## 🔹 Accessibility
- **Essential Practices** 🌐
  ```swift
  Button(action: handleTap) {
      Text("Action")
  }
  .accessibilityLabel("Descriptive label")
  .accessibilityHint("Explains the action")
  .accessibilityAddTraits(.isButton)
  ```

---
Remember: Code quality and maintainability are paramount. Always optimize for readability and future maintenance within the single-file constraint. 🚀
