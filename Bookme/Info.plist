<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>BookMe</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.963139583184-og4jdsf6m4uhstb32e3v4cbvvmg6jib6</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.book.me.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>bookme</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>GIDClientID</key>
	<string>963139583184-og4jdsf6m4uhstb32e3v4cbvvmg6jib6.apps.googleusercontent.com</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>comgooglemaps</string>
		<string>maps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCalendarsUsageDescription</key>
	<string>This app requires access to your calendar to schedule appointments.</string>
	<key>NSCameraUsageDescription</key>
	<string>This app need to access your Camera</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We will need to make use of your location to present relevant information about offers around you.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We will need to make use of your location to present relevant information about offers around you.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We will need to make use of your location to present relevant information about offers around you.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app need to access your Photo Library</string>
	<key>UIAppFonts</key>
	<array>
		<string>NunitoBold.ttf</string>
		<string>MontserratSemiBold.ttf</string>
		<string>NunitoSemiBold.ttf</string>
		<string>NunitoExtraBold.ttf</string>
		<string>MontserratMedium.ttf</string>
		<string>NunitoMedium.ttf</string>
		<string>NunitoRegular.ttf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<true/>
	</dict>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchScreen</key>
	<dict/>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>NSFaceIDUsageDescription</key>
	<string>Allow Face ID authentication</string>
</dict>
</plist>
