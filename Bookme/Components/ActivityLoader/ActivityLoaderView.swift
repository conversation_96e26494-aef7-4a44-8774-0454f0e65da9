//
//  ActivityLoaderView.swift
//  Bookme
//
//  Created by Apple on 25/04/2024.
//


import SwiftUI


struct ActivityLoaderView: View {
    @State var isAnimating = false // <1>
    var body: some View {
        LottieView(name: "logo.animation")
            .frame(width: 54.relativeWidth, height: 74.relativeHeight)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(red: 0.73, green: 0.75, blue: 0.75).opacity(0.8))
    }
}




