//
//  LoadingView.swift
//  Bookme
//
//  Created by Apple on 27/11/2024.
//

import SwiftUI


struct LoadingView<Content>: View where Content: View {
    @Binding var isShowing: Bool
    var content: () -> Content

    var body: some View {
        GeometryReader { _ in
            ZStack(alignment: .center) {
                self.content()
//                    .disabled(self.isShowing)
//                    .blur(radius: self.isShowing ? 3 : 0)

//                VStack {
//                    if self.isShowing {
//                        ProgressView("Loading...")
//                    }
//                }
            }
        }
    }
}
