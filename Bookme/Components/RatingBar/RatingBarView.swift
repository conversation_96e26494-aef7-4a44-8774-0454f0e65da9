//
//  RatingBarView.swift
//  Bookme
//
//  Created by Apple on 02/04/2024.
//


import SwiftUI

/**
  RatingBarView is lightweight & easy-to-use rating bar view to get and set ratings in SwiftUI.

  # Input Variable: #

 - `selected`: parameter wrapped with "@Binding" helps to show the selected ratings of a ratingbarview.

  - `heightWeightImage`: variable to change height of ratingbar view.

  # Example #
 ```
  RatingBarView(selected: .constant(5), heightWeightImage: 30)

 ```
 */

struct RatingBarView: View {
    @Binding var selected: Double
    var heightWeightImage: CGFloat = 30
    var spaceBetween: Double = 5
    var totalStars: Int = 5 // Total number of stars
    
    var body: some View {
        HStack(spacing: spaceBetween) {
            ForEach(1...totalStars, id: \.self) { index in
                ZStack {
                    // Background for empty star
                    Image("img_star1_amber_600")
                        .resizable()
                        .scaledToFit()
                        .frame(width: heightWeightImage, height: heightWeightImage)
                    
                    // Foreground for filled star
                    Image("img_star1")
                        .resizable()
                        .scaledToFit()
                        .frame(width: heightWeightImage, height: heightWeightImage)
                        .mask(
                            Rectangle()
                                .size(
                                    width: calculateFillWidth(for: index) * heightWeightImage,
                                    height: heightWeightImage
                                )
                        )
                }
                .onTapGesture {
                    self.selected = Double(index)
                }
            }
        }
        .background(Color.clear)
    }
    
    // Calculate the fill percentage for the current star
    private func calculateFillWidth(for index: Int) -> CGFloat {
        let threshold = Double(index)
        if selected >= threshold {
            return 1.0 // Fully filled
        } else if selected > threshold - 1 {
            return CGFloat(selected - (threshold - 1)) // Partially filled
        } else {
            return 0.0 // Not filled
        }
    }
}

struct RatingBarView_Previews: PreviewProvider {
    static var previews: some View {
        RatingBarView(selected: .constant(1.5), heightWeightImage: 40)
            .previewLayout(.sizeThatFits)
    }
}

