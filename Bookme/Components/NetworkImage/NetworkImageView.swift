//
//  NetworkImageView.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//

import SDWebImageSwiftUI
import SwiftUI

struct NetworkImageView: View {
    let originalColor: Bool
    let path: String?
    let contentMode: ContentMode
    let onFailure: ((Error) -> Void)?
    let onSuccess : ((PlatformImage, Data?, SDImageCacheType) -> Void)?
    let imageView: Bool
    init(path: String?, contentMode: ContentMode = .fit, originalColor: Bool = true, imageView: Bool = false, onSuccess : ((PlatformImage, Data?, SDImageCacheType) -> Void)? = nil , onFailure: ((Error) -> Void)? = nil) {
        self.originalColor = originalColor
        self.path = path
        self.imageView = imageView
        self.onSuccess = onSuccess
        self.onFailure = onFailure
        self.contentMode = contentMode
    }
    var body: some View {
        if let path = path, !path.isEmpty {
            if path.starts(with: "http") {
                WebImage(url: URL(string: path))
                    .renderingMode(self.originalColor ? .original : .template)
                    .resizable()
                    .placeholder { Image(systemName: "photo").foregroundColor(.white) }
                    .onSuccess(perform: onSuccess)
//                    .onSuccess(perform: { image, _, _ in
//                        self.convertImage(value: image, path: path)
//                    })
                    .onFailure(perform: self.onFailure)
                    .indicator(.activity) // Activity Indicator
                    .transition(.fade(duration: 0.5)) // Fade Transition with duration
                    .aspectRatio(contentMode: self.contentMode) 
            } else {
                Image(path)
                    .renderingMode(self.originalColor ? .original : .template)
                    .resizable()
                    .scaledToFit()
            }
            
        } else {
            Image(systemName: "photo")
                .resizable()
                .scaledToFit()
                .foregroundStyle(.gray.opacity(0.6))
        }
    }
}
struct StretchableHeader: View
{
    var image: Image
    var initialHeaderHeight: CGFloat = UIScreen.main.bounds.height * 0.25
    
    var body: some View
    {
        GeometryReader(content: { geometry in

            let minY = geometry.frame(in: .global).minY

            image
                .resizable()
                .offset(y: minY > 0 ? -minY : 0)
                .frame(height: minY > 0 ? initialHeaderHeight + minY : initialHeaderHeight)
                .aspectRatio(2, contentMode: .fill)
        })
        .frame(height: initialHeaderHeight)
    }
}
