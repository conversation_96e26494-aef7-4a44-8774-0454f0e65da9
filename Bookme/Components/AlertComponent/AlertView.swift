//
//  AlertView.swift
//  Bookme
//
//  Created by Apple on 25/04/2024.
//

import SwiftUI

struct AlertView: View {
    @Binding var pageState: PageState
    let config: AlertConfig
    var body: some View {
        switch config.alertType {
        case .alert:
            ZStack {
                VStack(alignment: .center) {
                    Text(LocalizedStringKey(config.title))
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(18.0)).weight(.medium))
                        .multilineTextAlignment(.center)
                        .foregroundColor(.black)

                    Text(LocalizedStringKey(config.text))
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)).weight(.medium))
                        .multilineTextAlignment(.center)
                        .foregroundColor(Color(red: 0.47, green: 0.47, blue: 0.47))
                        .padding(.vertical, 8)

                    HStack {
                        Button {
                            pageState = .stable
                            config.onOk?()
                        } label: {
                            VStack {
                                Text("Ok")
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(15.0)).bold())
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                            .background(ColorConstants.Cyan800)
                            
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.4, green: 0.7, blue: 0.27), lineWidth: 0.50)
                            )
                            .frame(maxWidth: .infinity)
                        }
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(.white)
                .cornerRadius(6)
                .padding()
                
            }
        case .choiceAlert:
            ZStack {
                VStack(alignment: .center) {
                    Text(LocalizedStringKey(config.title))
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(18.0)).weight(.medium))
                        .multilineTextAlignment(.center)
                        .foregroundColor(.black)

                    Text(LocalizedStringKey(config.text))
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)).weight(.medium))
                        .multilineTextAlignment(.center)
                        .foregroundColor(Color(red: 0.47, green: 0.47, blue: 0.47))
                        .padding(.vertical, 8)

                    HStack {
                        Button {
                            pageState = .stable
                            config.onCancel?()

                        } label: {
                            VStack {
                                Text(LocalizedStringKey(config.cancelButtonText))
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)).bold())
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(ColorConstants.Cyan800)
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.99, green: 0.7, blue: 0.16), lineWidth: 0.50)
                            )
                        }

                        Button {
                            pageState = .stable
                            config.onOk?()
                        } label: {
                            VStack {
                                Text(LocalizedStringKey(config.okButtonText))
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)).bold())
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(ColorConstants.Cyan800)
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.4, green: 0.7, blue: 0.27), lineWidth: 0.50)
                            )
                        }
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(.white)
                .cornerRadius(6)
                .padding()
            }
        }
    }
}

#Preview {
    VStack {
        
        AlertView(pageState: .constant(.message(config: AlertConfig(title: "Hello", text: "Hi",onCancel: {}, onOk: {}))), config: AlertConfig(title: "Hello", text: "Hi",alertType: .choiceAlert, onCancel: {},onOk: {}))
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(.gray)
}
