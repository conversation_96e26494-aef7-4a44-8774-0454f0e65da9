//
//  CustomPlaceholder.swift
//  Bookme
//
//  Created by Apple on 23/09/2024.
//

import SwiftUI

enum PlaceholderType {
    case noData, noAppointments, noService, clientSideError, serverSideError, networkError, unknownError, emptyTimeSlot
}

struct CustomPlaceholder: View {
    let placeholderType: PlaceholderType
    var title: LocalizedStringKey? = "No Data Found"
    var subTitle: LocalizedStringKey? = "The content is not available"
    var image: ImageResource?
    var size: CGSize?
    var titleColor: Color?
    var retryAction: (() -> Void)?
    var body: some View {
        switch placeholderType {
        case .noData:
            NoDataSection
        case .noAppointments:
            NoAppointmentSection
        case .noService:
            NoServiceSection
        case .networkError:
            NetworkErrorSection
        case .clientSideError:
            ClientSideErrorSection
        case .serverSideError:
            ServerSideErrorSection
        case .unknownError:
            UnknownErrorSection
        case .emptyTimeSlot:
            EmptyTimeSlotSection
        }
    }

    var NoDataSection: some View {
        VStack {
            Image(image ?? .noDataPlaceholder)
                .resizable()
                .scaledToFit()
                .frame(width: size?.width ?? 310.relativeWidth, height: size?.height ?? 309.relativeHeight, alignment: .center)
                .padding(.bottom, 16)
            VStack(spacing: 16.relativeHeight) {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(titleColor ?? ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Cyan800.opacity(0.6))
                        .multilineTextAlignment(.leading)
                }
            }
        }
    }

    var NoAppointmentSection: some View {
        VStack {
            Image(image ?? .appointmentPlaceholder)
                .resizable()
                .scaledToFit()
                .frame(width: 116.relativeWidth, height: 116.relativeWidth, alignment: .center)

            VStack(spacing: 8.relativeHeight) {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: 20.relativeFontSize))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.center)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: 14.relativeFontSize))
                        .fontWeight(.regular)
                        .foregroundColor(.black.opacity(0.69))
                        .multilineTextAlignment(.center)
                }
            }
        }.padding(.top, 64.relativeHeight)
    }

    var NoServiceSection: some View {
        VStack {
            Image(image ?? .noService)
                .resizable()
                .scaledToFit()
                .padding(-8)
                .frame(width: 120.08.relativeWidth, height: 120.relativeHeight, alignment: .center)

            VStack {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: 16.0.relativeFontSize))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.center)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: 12.0.relativeFontSize))
                        .fontWeight(.regular)
                        .foregroundColor(.black.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
            }
        }
    }

    var NetworkErrorSection: some View {
        VStack {
            Image(image ?? .networkErrorPlaceholder)
                .resizable()
                .scaledToFit()
                .frame(width: 269.relativeWidth, height: 269.relativeHeight, alignment: .center)

            VStack {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }
            }
        }
    }

    var ClientSideErrorSection: some View {
        VStack {
            Image(image ?? .noDataPlaceholder)
                .resizable()
                .scaledToFit()
                .frame(width: 310.relativeWidth, height: 309.relativeHeight, alignment: .center)

            VStack {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }
            }
        }
    }

    var ServerSideErrorSection: some View {
        VStack {
            Image(image ?? .noDataPlaceholder)
                .resizable()
                .scaledToFit()
                .frame(width: 310.relativeWidth, height: 309.relativeHeight, alignment: .center)

            VStack {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }
            }
        }
    }

    var UnknownErrorSection: some View {
        VStack {
            Image(image ?? .noDataPlaceholder)
                .resizable()
                .scaledToFit()
                .frame(width: 310.relativeWidth, height: 309.relativeHeight, alignment: .center)

            VStack {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                }
            }
        }
    }

    var EmptyTimeSlotSection: some View {
        VStack {
            Image(image ?? .noTimeslot)
                .resizable()
                .scaledToFit()
                .foregroundColor(.black.opacity(0.6))
                .frame(width: 30.relativeWidth, height: 30.relativeHeight, alignment: .center)

            VStack {
                if let title = title {
                    Text(title)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Black900)
                        .multilineTextAlignment(.leading)
                }

                if let subTitle = subTitle {
                    Text(subTitle)
                        .font(FontScheme
                            .kNunitoSemiBold(size: getRelativeHeight(11.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(.black.opacity(0.6))
                        .multilineTextAlignment(.leading)
                }
            }
        }
    }
}

#Preview {
    CustomPlaceholder(placeholderType: .noData)
}
