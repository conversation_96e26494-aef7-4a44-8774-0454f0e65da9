//
//  PageIndicator.swift
//
//  Created By Dhiwise
//
//

import SwiftUI

/**
 PageIndicator provide a indicators duraing page update.

 # Input Variable: #

 - `numPages`: count of total indicators

 - `currentPage`: variable wrapped with @Binding. update currunt page.

 - `selectedColor`: currunt page indicator color.

 - `unSelectedColor`: remaining indicators color.

 # Example #
 ```
    @State private var curruntPage: Int = 0

    PageIndicator(numPages: 5,
                  currentPage: $curruntPage,
                  selectedColor: Color.green,
                  unSelectedColor: Color.gray,
                  spacing: 8.0)
 ```
 */

struct PageIndicator: View {
    // Constants
    private var spacing: CGFloat = 2
    private let isCircular: Bool
    private let diameter: CGFloat
    private var selectedColor: Color = .green
    private var unSelectedColor: Color = .gray

    // Settings
    let numPages: Int
    @Binding var selectedIndex: Int
    @Namespace private var animation
    init(numPages: Int, currentPage: Binding<Int>, selectedColor: Color, unSelectedColor: Color,
         spacing: CGFloat, diameter: CGFloat = 12, isCircular: Bool = true)
    {
        self.numPages = numPages
        _selectedIndex = currentPage
        self.spacing = spacing
        self.diameter = diameter
        self.isCircular = isCircular
        self.selectedColor = selectedColor
        self.unSelectedColor = unSelectedColor
    }

    var body: some View {
        VStack {
            HStack(alignment: .center, spacing: spacing) {
                ForEach(0 ..< numPages, id: \.self) { index in
                    DotIndicator(pageIndex: index,
                                 selectedColor: selectedColor,
                                 unSelectedColor: unSelectedColor, isCircular: isCircular,
                                 selectedPage: self.$selectedIndex)
                    .frame(width: (index == selectedIndex && !isCircular) ? self.diameter * 3 : self.diameter, height: (index == selectedIndex && !isCircular) ? self.diameter * 0.7 :    self.diameter)
                        .if(index == selectedIndex, transform: {
                            $0.matchedGeometryEffect(id: "dot.indicator", in: animation)
                        })
                }
            }
        }
    }
}

struct DotIndicator: View {
    let minScale: CGFloat = 1
    let maxScale: CGFloat = 1.1
    let minOpacity: Double = 0.6

    let pageIndex: Int
    let selectedColor: Color
    let unSelectedColor: Color
    let isCircular: Bool
    @Binding var selectedPage: Int

    var body: some View {
        Button(action: {
            withAnimation(.bouncy) {
                self.selectedPage = self.pageIndex
            }
        }) {
            if isCircular {
                Circle()
                    .scaleEffect(selectedPage == pageIndex
                        ? maxScale
                        : minScale)
                    .animation(.spring(), value: selectedPage)
                    .foregroundColor(selectedPage == pageIndex
                        ? selectedColor
                        : unSelectedColor.opacity(minOpacity))
            } else {
                Capsule()
                    .scaleEffect(selectedPage == pageIndex
                        ? maxScale
                        : minScale)
                    .animation(.spring(), value: selectedPage)
                    .foregroundColor(selectedPage == pageIndex
                        ? selectedColor
                        : unSelectedColor.opacity(minOpacity))
            }
        }
    }
}

struct PageIndicator_Previews: PreviewProvider {
    static var previews: some View {
        PageIndicator(numPages: 5, currentPage: .constant(2), selectedColor: .red,
                      unSelectedColor: .green, spacing: 10, isCircular: false)
            .previewDisplayName("Regular")
            .previewLayout(PreviewLayout.sizeThatFits)
            .padding()
    }
}



struct PageIndicatorOptional: View {
    // Constants
    private var spacing: CGFloat = 2
    private let isCircular: Bool
    private let diameter: CGFloat
    private var selectedColor: Color = .green
    private var unSelectedColor: Color = .gray

    // Settings
    let numPages: Int
    @Binding var selectedIndex: Int?
    @Namespace private var animation
    init(numPages: Int, currentPage: Binding<Int?>, selectedColor: Color, unSelectedColor: Color,
         spacing: CGFloat, diameter: CGFloat = 12, isCircular: Bool = true)
    {
        self.numPages = numPages
        _selectedIndex = currentPage
        self.spacing = spacing
        self.diameter = diameter
        self.isCircular = isCircular
        self.selectedColor = selectedColor
        self.unSelectedColor = unSelectedColor
    }

    var body: some View {
        VStack {
            HStack(alignment: .center, spacing: spacing) {
                ForEach(0 ..< numPages, id: \.self) { index in
                    DotIndicatorOptional(pageIndex: index,
                                 selectedColor: selectedColor,
                                 unSelectedColor: unSelectedColor, isCircular: isCircular,
                                 selectedPage: self.$selectedIndex)
                    .frame(width: (index == selectedIndex && !isCircular) ? self.diameter * 3 : self.diameter, height: (index == selectedIndex && !isCircular) ? self.diameter * 0.7 :    self.diameter)
                        .if(index == selectedIndex, transform: {
                            $0.matchedGeometryEffect(id: "dot.indicator", in: animation)
                        })
                }
            }
        }
    }
}

struct DotIndicatorOptional: View {
    let minScale: CGFloat = 1
    let maxScale: CGFloat = 1.1
    let minOpacity: Double = 0.6

    let pageIndex: Int
    let selectedColor: Color
    let unSelectedColor: Color
    let isCircular: Bool
    @Binding var selectedPage: Int?

    var body: some View {
        Button(action: {
            withAnimation(.bouncy) {
                self.selectedPage = self.pageIndex
            }
        }) {
            if isCircular {
                Circle()
                    .scaleEffect(selectedPage == pageIndex
                        ? maxScale
                        : minScale)
                    .animation(.spring(), value: selectedPage)
                    .foregroundColor(selectedPage == pageIndex
                        ? selectedColor
                        : unSelectedColor.opacity(minOpacity))
            } else {
                Capsule()
                    .scaleEffect(selectedPage == pageIndex
                        ? maxScale
                        : minScale)
                    .animation(.spring(), value: selectedPage)
                    .foregroundColor(selectedPage == pageIndex
                        ? selectedColor
                        : unSelectedColor.opacity(minOpacity))
            }
        }
    }
}
