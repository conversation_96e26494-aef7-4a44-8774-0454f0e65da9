<svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_392_352)">
<circle cx="13.5" cy="12.5" r="10.5" fill="#008F96"/>
</g>
<defs>
<filter id="filter0_d_392_352" x="0" y="0" width="27" height="27" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.49 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_392_352"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_392_352" result="shape"/>
</filter>
</defs>
</svg>
