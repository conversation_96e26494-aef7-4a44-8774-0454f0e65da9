//
//  UIApplicationExtension.swift
//  Bookme
//
//  Created by Apple on 28/10/2024.
//


import UIKit
extension UIApplication {
    func activeRootViewController() -> UIViewController? {
        // Iterate over all connected scenes to find the active one
        connectedScenes
            .compactMap { $0 as? UIWindowScene }
            .first(where: { $0.activationState == .foregroundActive })?
            .windows
            .first(where: \.isKeyWindow)?
            .rootViewController
    }
}
