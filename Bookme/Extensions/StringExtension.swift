//
//  StringExtension.swift
//  Bookme
//
//  Created by Apple on 14/11/2024.
//

import Foundation

extension String {
    func responseDecodable<T: Decodable>(of type: T.Type) -> T? {
        let jsonData = Data(utf8)
        let decoder = JSONDecoder()

        do {
            let data = try decoder.decode(type.self, from: jsonData)
            print("data loaded")
            return data
        } catch {
            print(error)
            print("data failed")
            return nil
        }
    }
}
