//
//  ViewExtension.swift
//  Bookme
//

import Foundation
import Shimmer
import SwiftUI
import UIKit

// Hide default navigation bar from Navigation link screen.
extension View {
    func hideNavigationBar(_ hide: Bool = true) -> some View {
        toolbar(hide ? .hidden : .visible, for: .tabBar)
            .toolbar(hide ? .hidden : .visible, for: .navigationBar)
            .navigationBarTitle("", displayMode: .inline)
            .navigationBarBackButtonHidden()
    }

    func hideNavigationBar() -> some View {
        navigationBarTitle("", displayMode: .inline)
            .navigationBarHidden(true)
            .navigationViewStyle(StackNavigationViewStyle())
    }

    @ViewBuilder func visibility(_ visibility: ViewVisibility) -> some View {
        if visibility != .gone {
            if visibility == .visible {
                self
            } else {
                hidden()
            }
        }
    }

    @ViewBuilder func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }

    func disableWithOpacity(_ disable: Bo<PERSON>, opacity: Double = 0.4) -> some View {
        modifier(DisableWithOpacityModifier(disable: disable, opacity: opacity))
    }
}

struct DisableWithOpacityModifier: ViewModifier {
    let disable: Bool
    let opacity: Double
    func body(content: Content) -> some View {
        ZStack(alignment: .bottomTrailing) {
            content
                .disabled(disable)
                .opacity(disable ? opacity : 1.0)
        }
    }
}

enum ViewVisibility: CaseIterable {
    case visible, // view is fully visible
         invisible, // view is hidden but takes up space
         gone // view is fully removed from the view hierarchy
}

extension View {
    func sync<T: Equatable>(_ published: Binding<T>, with binding: Binding<T>) -> some View {
        onChange(of: published.wrappedValue) { _, published in
            binding.wrappedValue = published
        }
        .onChange(of: binding.wrappedValue) { _, binding in
            published.wrappedValue = binding
        }
    }
}

struct ViewDidLoadModifier: ViewModifier {
    @State private var didLoad = false
    private let action: (() -> Void)?

    init(perform action: (() -> Void)? = nil) {
        self.action = action
    }

    func body(content: Content) -> some View {
        content.onAppear {
            if self.didLoad == false {
                self.didLoad = true
                self.action?()
            }
        }
    }
}

extension View {
    func onLoad(perform action: (() -> Void)? = nil) -> some View {
        modifier(ViewDidLoadModifier(perform: action))
    }
}

extension View {
    func endTextEditing() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder),
                                        to: nil, from: nil, for: nil)
    }
}

extension View {
    func onSlide(editAction: (() -> Void)? = nil, deleteAction: (() -> Void)? = nil) -> some View {
        modifier(SlideActions(deleteAction: deleteAction, editAction: editAction))
    }

    func cornerRadius(radius: CGFloat, corners: UIRectCorner) -> some View {
        ModifiedContent(content: self, modifier: CornerRadiusStyle(radius: radius, corners: corners))
    }
}

struct CornerRadiusShape: Shape {
    var radius = CGFloat.infinity
    var corners = UIRectCorner.allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

struct ShimmerizeViewModifier: ViewModifier {
    let shimmering: Bool
    func body(content: Content) -> some View {
        if shimmering {
            content
                .redacted(reason: .placeholder)
                .shimmering()
                .disabled(true)
        }else {
            content
        }
       
    }
}

extension View {
    func shimmerize(_ shimmering: Bool = true) -> some View {
        modifier(ShimmerizeViewModifier(shimmering: shimmering))
    }
}

struct CornerRadiusStyle: ViewModifier {
    var radius: CGFloat
    var corners: UIRectCorner

    func body(content: Content) -> some View {
        content
            .clipShape(CornerRadiusShape(radius: radius, corners: corners))
    }
}

struct SlideActions: ViewModifier {
    let deleteAction: (() -> Void)?
    let editAction: (() -> Void)?

    @State var offset: CGSize = .zero
    @State var initialOffset: CGSize = .zero
    @State var contentWidth: CGFloat = 0.0
    @State var willDeleteIfReleased = false

    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    ZStack {
                        Rectangle()
                            .cornerRadius(radius: 7, corners: [.topRight, .bottomRight])
                            .foregroundColor(.clear)

                        VStack(alignment: .center, spacing: 16.0.relativeHeight) {
                            if let _ = editAction {
                                Button(action: {
                                    edit()
                                }, label: {
                                    Image("img_basileditoutl")
                                        .frame(width: getRelativeWidth(40.0),
                                               height: getRelativeWidth(40.0), alignment: .center)
                                        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0,
                                                                bottomLeft: 20.0, bottomRight: 20.0)
                                                .stroke(ColorConstants.Cyan800,
                                                        lineWidth: 2))
                                        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0,
                                                                   bottomLeft: 20.0, bottomRight: 20.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Cyan80019, radius: 30, x: 0, y: 12)
                                }).layoutPriority(-1)
                            }

                            if let _ = deleteAction {
                                Button(action: {
                                    delete()
                                }, label: {
                                    Image("img_vector_white_a700_40x40")
                                        .frame(width: getRelativeWidth(40.0),
                                               height: getRelativeWidth(40.0),
                                               alignment: .center)
                                        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0,
                                                                bottomLeft: 20.0,
                                                                bottomRight: 20.0)
                                                .stroke(.red,
                                                        lineWidth: 2))
                                        .background(RoundedCorners(topLeft: 20.0,
                                                                   topRight: 20.0,
                                                                   bottomLeft: 20.0,
                                                                   bottomRight: 20.0)
                                                .fill(.red))
                                        .shadow(color: ColorConstants.Cyan80019, radius: 30,
                                                x: 0, y: 12)

                                }).layoutPriority(-1)
                            }
                        }

                        .padding(.top, 8.0.relativeHeight)
                    }
                    .frame(width: -offset.width)
                    .clipShape(Rectangle())
                    .offset(x: geometry.size.width - 16.0.relativeWidth)
                    .onAppear {
                        contentWidth = geometry.size.width
                    }
//                    .gesture(
//                        TapGesture()
//                            .onEnded {
//                                delete()
//                            }
//                    )
                }
            )

            .offset(x: offset.width, y: 0)
            .gesture(
                DragGesture()
                    .onChanged { gesture in
                        if gesture.translation.width + initialOffset.width <= 0 {
                            self.offset.width = gesture.translation.width + initialOffset.width
                        }
                        if self.offset.width < -deletionDistance, !willDeleteIfReleased {
                            hapticFeedback()
                            willDeleteIfReleased.toggle()
                        } else if offset.width > -deletionDistance, willDeleteIfReleased {
                            hapticFeedback()
                            willDeleteIfReleased.toggle()
                        }
                    }
                    .onEnded { _ in
                        if offset.width < -deletionDistance {
                            delete()
                        } else if offset.width < -halfDeletionDistance {
                            offset.width = -tappableDeletionWidth
                            initialOffset.width = -tappableDeletionWidth
                        } else {
                            offset = .zero
                            initialOffset = .zero
                        }
                    }
            )
            .animation(.interactiveSpring(), value: offset)
    }

    private func edit() {
        offset = .zero
        initialOffset = .zero
        editAction?()
    }

    private func delete() {
        // offset.width = -contentWidth

        offset = .zero
        initialOffset = .zero
        deleteAction?()
    }

    private func hapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
    }

    // MARK: Constants

    let deletionDistance = CGFloat(200)
    let halfDeletionDistance = CGFloat(50)
    let tappableDeletionWidth = CGFloat(100)
}
