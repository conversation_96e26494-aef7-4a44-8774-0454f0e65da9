import SwiftUI

struct RatepopupOneContainerView: View {
    @StateObject var ratepopupOneContainerViewModel = RatepopupOneContainerViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text("Content")
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(786.0))
            Text("tabbar")
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(60.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan800)
                .shadow(color: ColorConstants.Black90019, radius: 5, x: 0, y: -4)
        }
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(LinearGradient(gradient: Gradient(colors: [ColorConstants.Cyan900,
                                                               ColorConstants.Black901]),
            startPoint: .topLeading, endPoint: .bottomTrailing))
        .hideNavigationBar()
        .onAppear {}
    }
}

struct RatepopupOneContainerView_Previews: PreviewProvider {
    static var previews: some View {
        RatepopupOneContainerView()
    }
}
