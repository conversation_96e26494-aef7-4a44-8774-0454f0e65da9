import Foundation
import SwiftUI

class SavedViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var selectedHeader: String?
    @Published var vendorsList: [WishlistModel] = []
    let headersList: [String] = ["All", "Hair Salon", "barbershop", "Nail salon", "Skin Care", "Brows & Lashes"]
    
    override init() {
        selectedHeader = headersList.first
        
        super.init()
    }
    
    func checkHeader(_ value: String) -> Bool { value == selectedHeader }
    
    func updateHeaderList(_ value: String) { withAnimation(.bouncy) { selectedHeader = value }}
    
    func getSavedVendorList() {
        guard
            let coordination = AppState.userLocation,
            let userID: Int = AppState.userModel?.user.id
        else { return }
        
        let parameters: [String: Any] = ["user_id": userID, "lat": coordination.latitude, "lng": coordination.longitude]
        self.vendorsList.removeAll()
        onApiCall(api.viewWishlists, parameters: parameters) {
            self.vendorsList = $0.data ?? []
        }
    }
    func removeFromWishlist(vendor: WishlistModel?) {
        guard
            let userID: Int = AppState.userModel?.user.id,
            let vendorID = vendor?.vendorID
        else { return }
        
        let parameters: [String: Any] = ["user_id": userID, "Vendor_ID": vendorID]
        onApiCall(api.addWishlist, parameters: parameters) { response in
            if response.success {
                self.getSavedVendorList()
            }
        }
    }
}
