import SwiftUI

struct SavedView: View {
    @StateObject var viewModel = SavedViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Namespace private var animation: Namespace.ID
    var body: some View {
        MainScrollBody(invertColor: true, backButtonWithTitle: StringConstants.kLblSaved.localize) {
            SuperView(pageState: $viewModel.pageState) {
                ExploreShimmerView()
                    .background(.white)
            } content: {
               
                VStack {
                        if viewModel.vendorsList.isEmpty {
                            CustomPlaceholder(placeholderType: .noData,title: "No favorite list", subTitle: "", image: .noFavorite, size: .init(width: 78.relativeFontSize, height: 78.relativeFontSize), titleColor: .black)
                                .padding(.top, 32.relativeHeight)
                        } else {
                            ForEach(viewModel.vendorsList) { item in
                                VStack {
                                    let model: NearestVendorModel = .init(vendorName: item.vendorName, vendorID: item.vendorID, vendorEmail: item.vendorEmail, vendorPhone: item.vendorPhone, vendorLogo: item.vendorLogo, address: item.address, lat: item.lat, lng: item.lng, openingTime: item.openingTime, closingTime: item.closingTime, type: item.type, distance: item.distance.toString, rating: item.rating)
                                    ZStack(alignment: .topTrailing) {
                                        SaloonCardCell(model: model, routesType: .myAccountRoute)

                                        Button {
                                            viewModel.removeFromWishlist(vendor: item)
                                        } label: {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundStyle(ColorConstants.Cyan900)
                                        }
                                        .padding(.vertical)
                                    }
                                    if item != viewModel.vendorsList.last {
                                        Divider()
                                            .padding(.horizontal)
                                    }
                                }
                            }
                        }
                    }
                .padding(.vertical)
                
            }
        }
        .toolbarBackground(.ultraThinMaterial, for: .navigationBar)
        .toolbarBackground(.automatic, for: .navigationBar)
        .background(ColorConstants.WhiteA700)
        .onAppear {
            viewModel.getSavedVendorList()
        }
    }
}

struct SavedView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            SavedView().attachAllEnvironmentObjects()
                .preferredColorScheme(.dark)
        }
    }
}
