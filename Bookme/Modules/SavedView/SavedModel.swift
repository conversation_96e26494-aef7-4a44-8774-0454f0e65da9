//
//  SavedModel.swift
//  Bookme
//
//  Created by Apple on 22/09/2024.
//

import Foundation



struct WishlistModel: Codable, Identifiable, Equatable, Hashable {
    let id: UUID = .init()
    let vendorName: String
    let vendorID: Int
    let vendorEmail, vendorPhone, vendorLogo, address: String
    let lat, lng, openingTime, closingTime: String
    let type: Int
    var  rating: String
    var distance: Int
    
    
    

    var vendorLogoUrl: String { AppConstants.Server.baseURL + vendorLogo }

    enum CodingKeys: String, CodingKey {
        case vendorName = "vendor_name"
        case vendorID = "vendor_id"
        case vendorEmail = "vendor_email"
        case vendorPhone = "vendor_phone"
        case vendorLogo = "vendor_logo"
        case address, lat, lng
        case openingTime = "opening_time"
        case closingTime = "closing_time"
        case type, distance, rating
    }

    init(from decoder: any Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.vendorName = try container.decode(String.self, forKey: .vendorName)
        self.vendorID = try container.decode(Int.self, forKey: .vendorID)
        self.vendorEmail = try container.decode(String.self, forKey: .vendorEmail)
        self.vendorPhone = try container.decode(String.self, forKey: .vendorPhone)
        self.vendorLogo = try container.decode(String.self, forKey: .vendorLogo)
        self.address = try container.decode(String.self, forKey: .address)
        self.lat = try container.decode(String.self, forKey: .lat)
        self.lng = try container.decode(String.self, forKey: .lng)
        self.openingTime = try container.decode(String.self, forKey: .openingTime)
        self.closingTime = try container.decode(String.self, forKey: .closingTime)
        self.type = try container.decode(Int.self, forKey: .type)
        self.distance = try container.decode(Int.self, forKey: .distance)
        do {
            
            let ratingValue = try container.decode(Int.self, forKey: .rating)
            self.rating = String(ratingValue)
           
//            let distanceValue = try container.decode(String.self, forKey: .distance)
//            self.distance = String(distanceValue)

        } catch DecodingError.typeMismatch {
            self.rating = try container.decode(String.self, forKey: .rating)
//            self.distance = try container.decode(String.self, forKey: .distance)
            
        }
    }
}
