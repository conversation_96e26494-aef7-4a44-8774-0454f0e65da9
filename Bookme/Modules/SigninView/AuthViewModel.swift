import AuthenticationServices
import Foundation
import GoogleSignIn
import SwiftUI

enum SocialLoginType: String {
    case apple, google
}

class AuthViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var nameText: String = .init()
    @Published var emailText: String = .init()
    @Published var passwordText: String = .init()
//    @Published var phoneText: String = .init()
    @Published var confirmPasswordText: String = .init()
    @Published var selectedGenderType: GenderType = .male
    @Published var selectedDate = Date()
//    var selectedDOBDate:Date?
    @Published var isValidEmailText: Bool = true
    
    @Published var nameErrorText: String?
//    @Published var phoneErrorText: String?
    @Published var emailErrorText: String?
    @Published var dobErrorText: String?
    @Published var passwordErrorText: String?
    @Published var confirmPasswordErrorText: String?
    @Published var showPopUpType: popUpType = .hide
    
    @Published var timerExpired = false
    @Published var timeStr = ""
    @Published var timeRemaining = AppConstants.COUNTDOWN_TIMER_LENGTH
    var timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    @Published var isTermsAccepted: Bool = false
    
    @Published var otpErrorMessage: String?
    
    @Published var otp: String = ""
    let numberOfFieldsInOTP = 4
    
    var isVerifyDisabled: Bool { otp.count < numberOfFieldsInOTP }
    
//    override init() {
//        super.init()
//        emailText = "<EMAIL>"
//        passwordText = "ntfser47"
//    }
    
    func onChangeDate(value: Date) {
//        dobText = value.toString(outputFormate: dateOutputFormate)
//        selectedDOBDate = value
    }
    
    
    func onDisappear(){
        self.emailText = ""
        self.passwordText = ""
    }
    
    func handleSuccessfulGooleLogin(with socialUserModel: SocialUserModel?, completion: @escaping (UserModel) -> Void) {
        if let socialUserModel = socialUserModel {
//            // Create an account in your system.
            let userIdentifier = socialUserModel.id ?? ""
            let fullName = socialUserModel.fullName ?? ""
            let firstName = socialUserModel.firstName ?? ""
            let lastName = socialUserModel.lastName ?? ""
            let email = socialUserModel.email ?? ""
            let photo = socialUserModel.photo ?? ""
            
            print(
                "userIdentifier: \(userIdentifier)", "fullName: \(fullName)", "firstName: \(firstName)", "lastName: \(lastName)", "email: \(email)", "photo: \(photo)"
            )
            
            socialLogin(user: socialUserModel, completion: completion)
        }
    }
    
    func handleSuccessfulAppleLogin(with socialUserModel: SocialUserModel?, completion: @escaping (UserModel) -> Void) {
        if let socialUserModel = socialUserModel {
//            // Create an account in your system.
            let userIdentifier = socialUserModel.id ?? ""
            let fullName = socialUserModel.fullName ?? ""
            let firstName = socialUserModel.firstName ?? ""
            let lastName = socialUserModel.lastName ?? ""
            let email = socialUserModel.email ?? ""
            let photo = socialUserModel.photo ?? ""
            
            print(
                "userIdentifier: \(userIdentifier)", "fullName: \(fullName)", "firstName: \(firstName)", "lastName: \(lastName)", "email: \(email)", "photo: \(photo)"
            )
            
            socialLogin(user: socialUserModel, completion: completion)
        }
    }
    
    func socialLogin(user: SocialUserModel, completion: @escaping (UserModel) -> Void) {
        var parameters = ["device_id": "\(AppState.fcmToken)"]
        parameters.updateValue(user.type.rawValue, forKey: "type")
        if let userIdentifier = user.id { parameters.updateValue(userIdentifier, forKey: "apple_id") }
        if let name = user.fullName { parameters.updateValue(name, forKey: "name") }
        if let email = user.email { parameters.updateValue(email, forKey: "user_email") }
        
        onApiCall(api.socialLogin, parameters: parameters) {
            if let user = $0.data {
                completion(user)
            }
        }
    }
    
    func updateOtpErrorMessage(_ value: String?) { otpErrorMessage = value }
    
    func onVerifyCode(userID: Int, completion: @escaping (UserModel) -> Void) {
        let parameters: [String: Any] = ["user_id": "\(userID)", "otp": "\(otp)"]
        updateOtpErrorMessage(nil)
        
        onApiCall(api.verifyOtpRegistration, parameters: parameters, hideClientSideError: true) {
            self.otpErrorMessage = $0.error ? $0.message : nil
            if let data = $0.data {
                self.registerWithPasskey(email: self.emailText, password: self.passwordText)
                completion(data)
            }
        } onFailure: { error in
            self.updatePageState(.failure(error: error))
            self.updateOtpErrorMessage(error)
        }
    }
    
    func onResendCode(userID: Int, completion: @escaping (Bool) -> Void) {
        let parameters: [String: Any] = ["user_id": "\(userID)"]

        onApiCall(api.resendOtpRegistration, parameters: parameters) {
            completion($0.success)
            if $0.success { self.startTimer() }
        }
    }
    
    func countDownString() {
        guard timeRemaining > 0 else {
            timer.upstream.connect().cancel()
            timerExpired = true
            timeStr = String(format: "%02d:%02d", 00, 00)
            return
        }
            
        timeRemaining -= 1
        timeStr = String(format: "%02d:%02d", 00, timeRemaining)
    }
    
    func startTimer() {
        timerExpired = false
        timeRemaining = AppConstants.COUNTDOWN_TIMER_LENGTH
        timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    }
        
    func stopTimer() {
        timerExpired = true
        timer.upstream.connect().cancel()
    }
    
    var isSignUpDisabled: Bool { (nameText.isEmptyOrWhitespace || emailText.isEmptyOrWhitespace || passwordText.isEmptyOrWhitespace) || emailErrorText != nil || passwordText.isEmptyOrWhitespace || confirmPasswordText.isEmptyOrWhitespace || passwordText != confirmPasswordText || !isTermsAccepted }
    var isSignInDisabled: Bool { (emailText.isEmptyOrWhitespace || passwordText.isEmptyOrWhitespace) || emailErrorText != nil }

    func onLogin(completion: @escaping (UserModel) -> Void) {
        let parameters: [String: String] = ["user_email": "\(emailText)", "password": "\(passwordText)", "device_id": "\(AppState.fcmToken)"]
        onApiCall(api.loginWithEmail, parameters: parameters) {
            if let user = $0.data {
                completion(user)
              
                self.updateLanguage(user.user.id)
            }
        }
    }
    
    func updateLanguage(_ userID: Int) {
        let parameters: [String: String] = ["user_id": "\(userID)", "language": "\(AppState.language)"]
        onApiCall(api.updateLanguage, parameters: parameters, withStateChange: false, withLoadingIndicator: false) { _ in }
    }
    
    func onRegister(completion: @escaping (User) -> Void) {
        let dob = selectedDate.toString(outputFormate: "yyyy-MM-dd")
        let gender = selectedGenderType.rawValue.capitalized
        let parameters: [String: String] = [
            "name": "\(nameText)",
//            "user_phone": "\(phoneText)",
            "email": "\(emailText)",
            "date_of_birth": "\(dob)",
            "Gender": "\(gender)",
            "password": "\(passwordText)",
        ]
        onApiCall(api.registerUser, parameters: parameters, hideClientSideError: true) {
            if let user = $0.data {
                completion(user)
                self.updateLanguage(user.id)
            }
        } onFailure: { errorString in
            self.updatePageState(.failure(title: "Oops", error: errorString))
        }
    }

    func retrieveUserCredentialsAndAutofill() {
        PasskeyManager.shared.requestCredential { email, password in
            if let email = email {
                self.emailText = email
            }
            if let password = password {
                self.passwordText = password
            }
        }
    }
    
    func registerWithPasskey(email: String, password: String) {
        PasskeyManager.shared.saveCredential(email: email, password: password)
    }
    
    // Validation logic for email and phone number
    func validateInput(_ value: String) {
        if isValidEmail(value) {
            emailErrorText = nil
        } else if isValidKuwaitPhoneNumber(value) {
            emailErrorText = nil
        }
        else if value.isEmpty {
//            emailErrorText = "Field is required."
            emailErrorText = nil
        }
        
        else if isNumeric(value) {
            emailErrorText = "Invalid Kuwaiti phone number. It should be 8 digits and start with 5, 6, or 9."
        } else if !isValidEmail(value) {
            emailErrorText = "Invalid email format."
        }
    }
}

// Regex for email validation
func isValidEmail(_ value: String) -> Bool {
    let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    let emailTest = NSPredicate(format: "SELF MATCHES %@", emailRegEx)
    return emailTest.evaluate(with: value)
}
  
func isValidKuwaitPhoneNumber(_ input: String) -> Bool {
    // Phone numbers in Kuwait are typically 8 digits long and start with a 5, 6, or 9
    let phoneRegex = "^[569][0-9]{7}$"
    return NSPredicate(format: "SELF MATCHES %@", phoneRegex).evaluate(with: input)
}

func isNumeric(_ input: String) -> Bool {
    // Checks if the input consists of numbers only
    return CharacterSet.decimalDigits.isSuperset(of: CharacterSet(charactersIn: input))
}

extension AuthViewModel: RoutableProtocol {
    nonisolated static func == (lhs: AuthViewModel, rhs: AuthViewModel) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    nonisolated func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }
}

