import SwiftUI

struct SignInView: View {
    let routesType: RoutesType
    let getBack: Bool
    var onGetBack: (() -> Void)?
    @StateObject private var authService: AuthService = .init()
   
    @StateObject var viewModel = AuthViewModel()
    @Environment(\.dismiss) var dismiss
    @Environment(RouterManager.self) var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    
    // Add focus state variables for keyboard navigation
    @State private var focusedFieldIndex: Int?
    @FocusState private var focusedField: TextFieldFocus?
    
    // Track if user has passkeys for this app
    @State private var hasPasskeys: Bool = false
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(invertColor: false, backButtonWithTitle: "") {
                VStack {
                    VStack {
                        VStack {
                            Text("Sign In")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(height: getRelativeHeight(33.0),
                                       alignment: .topLeading)
                                .frame(maxWidth: .infinity)
                                .padding(.horizontal, getRelativeWidth(48.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(115.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                            
                        let emailIndex: Int = 0
                        CustomTextField(title: "Email or Mobile Number", placeholder: "Email or Mobile Number", text: self.$viewModel.emailText, invalidText: self.viewModel.emailErrorText)
                            .keyboardType(.emailAddress)
                            .autocapitalization(.none)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.viewModel.emailText) { _, newValue in
                                withAnimation(.bouncy) {
                                    viewModel.validateInput(newValue)
                                }
                            }
                           
                            .focused($focusedField, equals: .field(emailIndex))
                            .focusable(fieldIndex: emailIndex, focusedFieldIndex: $focusedFieldIndex)
                           
                        let passwordIndex: Int = 1
                        CustomTextField(title: "Password", placeholder: StringConstants.kMsg, text: self.$viewModel.passwordText, invalidText: self.viewModel.passwordErrorText, isSecure: true)
                            .keyboardType(.default)
                            .textContentType(.oneTimeCode)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
//                            .onChange(of: self.viewModel.passwordText) { _, newValue in
//                                withAnimation(.bouncy) {
//                                    self.viewModel.passwordErrorText = newValue.isEmptyOrWhitespace
//                                        ? "Password field can't be empty"
//                                        : nil
//                                }
//                            }
                            .onChange(of: focusedFieldIndex) { _, newValue in
                                if newValue != nil {
                                    viewModel.retrieveUserCredentialsAndAutofill()
                                }
                            }
                           
                            .focused($focusedField, equals: .field(passwordIndex))
                            .focusable(fieldIndex: passwordIndex, focusedFieldIndex: $focusedFieldIndex)
                            
                        VStack(alignment: .trailing, spacing: 0) {
                            Button {
                                routerManager.push(to: .forgotPassword(type: routesType), where: routesType)
                            } label: {
                                Text("Forgot Password?")
                                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(114.0), height: getRelativeHeight(18.0),
                                           alignment: .topLeading)
                                    .padding(.leading)
                                    .padding(.leading)
                                    .padding(.trailing, getRelativeWidth(5.0))
                            }
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(18.0),
                               alignment: .trailing)
                                
                        .padding(.horizontal, getRelativeWidth(22.0))
                        .padding(.top, getRelativeHeight(8.0))

                        VStack {
                            Button(action: {
                                viewModel.onLogin(completion: afterSuccessLogin)
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("Sign in")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .multilineTextAlignment(.center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            })
                            .disableWithOpacity(viewModel.isSignInDisabled)
                            
                            VStack {
                                HStack {
                                    Divider()
                                        .frame(width: getRelativeWidth(115.0),
                                               height: getRelativeHeight(1.0), alignment: .bottom)
                                        .background(ColorConstants.Cyan80087)
                                        .padding(.top, getRelativeHeight(9.0))
                                        .padding(.bottom, getRelativeHeight(6.0))
                                    Spacer()
                                    Text("or sign with")
                                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                        .fontWeight(.regular)
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .fixedSize()
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(17.0), alignment: .topLeading)
                                    Spacer()
                                    Divider()
                                        .frame(width: getRelativeWidth(113.0),
                                               height: getRelativeHeight(1.0), alignment: .bottom)
                                        .background(ColorConstants.Cyan80087)
                                        .padding(.top, getRelativeHeight(9.0))
                                        .padding(.bottom, getRelativeHeight(6.0))
                                }
                                .frame(height: getRelativeHeight(17.0),
                                       alignment: .leading)
                                 
                                HStack {
                                    Button(action: {
                                        authService.appleSignIn()
                                            
                                    }, label: {
                                        Image("img_itemsign")
                                    })
                                    .frame(width: getRelativeWidth(65.0), height: getRelativeWidth(65.0),
                                           alignment: .center)
                                    .overlay(Circle()
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                    .background(Circle()
                                        .fill(ColorConstants.Cyan8007f))
                                    Button(action: {
                                               authService.googleSignIn()
                                           },
                                           label: {
                                               Image("img_itemsign_white_a700")
                                           })
                                           .frame(width: getRelativeWidth(65.0), height: getRelativeWidth(65.0),
                                                  alignment: .center)
                                           .overlay(Circle()
                                               .stroke(ColorConstants.Cyan800,
                                                       lineWidth: 1))
                                           .background(Circle()
                                               .fill(ColorConstants.Cyan8007f))
                                           .padding(.leading, getRelativeWidth(15.0))
                                }
                                .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(65.0),
                                       alignment: .center)
                                .padding(.top, getRelativeHeight(29.0))
                                .padding(.horizontal, getRelativeWidth(46.0))
                            }
                            .frame(width: getRelativeWidth(315.0), height: getRelativeHeight(111.0),
                                   alignment: .center)
                            .padding(.top, getRelativeHeight(31.0))
                            .padding(.leading, getRelativeWidth(10.0))
                            .padding(.trailing, getRelativeWidth(5.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(193.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        VStack(alignment: .leading, spacing: 0) {
                            HStack {
                                Text("Don’t have an account ?")
                                Button {
                                    
                                    viewModel.onDisappear()
                                    
                                    routerManager.push(to: .signUp, where: routesType)
                                } label: {
                                    Text("Sign up")
                                        .foregroundColor(ColorConstants.Cyan800)
                                }
                            }
                            
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(186.0), height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.horizontal, getRelativeWidth(72.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(18.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(53.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
                .padding(.bottom, 32.0.relativeHeight)
            }
            .background(ColorConstants.bgGradient)
        }
        // Add keyboard navigation modifier and focus coordination
        .coordinateFocus(focusedField: _focusedField, focusedFieldIndex: $focusedFieldIndex)
        .addKeyboardNavigation(focusedFieldIndex: $focusedFieldIndex, totalFields: 2, onDismiss: dismissKeyboard)
        .onChange(of: authService.googleUser) { viewModel.handleSuccessfulGooleLogin(with: $1, completion: afterSuccessLogin) }
        .onChange(of: authService.appleUser) { viewModel.handleSuccessfulAppleLogin(with: $1, completion: afterSuccessLogin) }
        
    }
    
    // Add helper method to dismiss keyboard
    private func dismissKeyboard() {
        focusedField = nil
        focusedFieldIndex = nil
    }
    
   
    
    func afterSuccessLogin(_ user: UserModel) {
        appState.onAuthentication(user)
        
        if getBack {
            onGetBack?()
            dismiss()
            
        } else {
            routerManager.popToAllRoot()
        }
    }
}

struct SigninView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            SignInView(routesType: .myAccountRoute, getBack: false).attachAllEnvironmentObjects()
        }
    }
}
