import Security
import SwiftUI
import AuthenticationServices

class PasskeyManager: NSObject {
    static let shared = PasskeyManager()
    
    private var credentialDelegate: ASAuthorizationControllerDelegate?

    func saveCredential(email: String, password: String) {
        SecAddSharedWebCredential("projects.crisance.com" as CFString,
                                  email as CF<PERSON><PERSON>,
                                  password as CFString) { error in
            if let error = error {
                print("❌ Failed to save credential: \(error.localizedDescription)")
            } else {
                print("✅ Credential saved successfully to iCloud Keychain")
            }
        }
    }

    func requestCredential(completion: @escaping (_ email: String?, _ password: String?) -> Void) {
        let passwordProvider = ASAuthorizationPasswordProvider()
        let request = passwordProvider.createRequest()
        let controller = ASAuthorizationController(authorizationRequests: [request])
        
        let delegate = CredentialRequestDelegate(completion: completion)
        controller.delegate = delegate
        controller.presentationContextProvider = delegate
        
        // Keep a strong reference
        self.credentialDelegate = delegate
        
        controller.performRequests()
    }

    private class CredentialRequestDelegate: NSObject, ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
        private let completion: (_ email: String?, _ password: String?) -> Void

        init(completion: @escaping (_ email: String?, _ password: String?) -> Void) {
            self.completion = completion
        }

        func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
            if let credential = authorization.credential as? ASPasswordCredential {
                completion(credential.user, credential.password)
            } else {
                completion(nil, nil)
            }
        }

        func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
            print("❌ Error retrieving credential: \(error.localizedDescription)")
            completion(nil, nil)
        }

        func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
            return UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
                .first { $0.isKeyWindow } ?? UIWindow()
        }
    }
}
