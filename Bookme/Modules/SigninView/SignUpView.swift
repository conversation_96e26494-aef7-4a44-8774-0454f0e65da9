//
//  SignUpView.swift
//  Bookme
//
//  Created by Apple on 29/04/2024.
//

import SwiftUI

struct SignUpView: View {
    @StateObject private var authService: AuthService = .init()
    @StateObject private var viewModel = AuthViewModel()
    @Environment(\.dismiss) var dismiss
    @Environment(RouterManager.self) var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    // Add a state variable to track the currently focused field
    @State private var focusedFieldIndex: Int?
    @FocusState private var focusedField: TextFieldFocus?
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(invertColor: false, backButtonWithTitle: "") {
                VStack {
                    VStack {
                        VStack {
                            Text("Sign Up")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(83.0), height: getRelativeHeight(33.0),
                                       alignment: .topLeading)
                                .padding(.horizontal, getRelativeWidth(48.0))
                            Text("Fill your information below or register with your social account.")
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Gray400)
                                .multilineTextAlignment(.center)
                                .padding(.top, getRelativeHeight(7.0))
                                .padding(.horizontal, getRelativeWidth(32.0))
                        }
                        .padding(.top, getRelativeHeight(32))
                        let nameIndex: Int = 0
                        CustomTextField(title: "Name", placeholder: "Enter your name", text: self.$viewModel.nameText, invalidText: self.viewModel.nameErrorText)
                            .keyboardType(.default)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.viewModel.nameText) { _, newValue in
                                withAnimation(.bouncy) {
                                    self.viewModel.nameErrorText = newValue.isEmptyOrWhitespace
                                        ? "Name field can't be empty"
                                        : nil
                                }
                            }
                            .focused($focusedField, equals: .field(nameIndex))
                            .focusable(fieldIndex: nameIndex, focusedFieldIndex: $focusedFieldIndex)
                    
                        let emailIndex: Int = 1
                        CustomTextField(title: "Email or Mobile Number", placeholder: "Email or Mobile Number", text: self.$viewModel.emailText, invalidText: self.viewModel.emailErrorText)
                            .keyboardType(.emailAddress)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.viewModel.emailText) { _, newValue in
                                withAnimation(.bouncy) {
                                    viewModel.validateInput(newValue)
                                }
                            }
                            .focused($focusedField, equals: .field(emailIndex))
                            .focusable(fieldIndex: emailIndex, focusedFieldIndex: $focusedFieldIndex)
                        
                        CustomTextField(title: "DOB", placeholder: "", text: .constant(viewModel.selectedDate.toString(outputFormate: "dd/MM/yyyy")), invalidText: nil)
                            .keyboardType(.emailAddress)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .disabled(true)
                            .onTapGesture {
                                self.dismissKeyboard()
                                self.viewModel.showPopUpType = .date
                            }
                    
                        VStack(alignment: .leading) {
                            HStack(spacing: 4) {
                                Text("Gender")
                                
                                Text("*")
                                    .foregroundColor(.red)
                            }
                           
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .padding(.horizontal, getRelativeWidth(24.0))
                            .padding(.top, getRelativeHeight(19.0))
                        
                            HStack {
                                Menu {
                                    ForEach(GenderType.allCases) { type in
                                        Button {
                                            withAnimation(.bouncy) {
                                                //                                                selectedPlace = place
                                                self.viewModel.selectedGenderType = type
                                                self.dismissKeyboard()
                                            }
                                        
                                        } label: {
                                            Text(type.title)
                                        }
                                    }
                                
                                } label: {
                                    HStack {
                                        //                                        if let selectedGenderType = viewModel.selectedGenderType {
                                        HStack {
                                            Text(LocalizedStringKey(self.viewModel.selectedGenderType.rawValue.capitalized))
                                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                                .fontWeight(.medium)
                                                .foregroundColor(ColorConstants.WhiteA700)
                                                .fixedSize()
                                                .multilineTextAlignment(.leading)
                                                .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(18.0),
                                                       alignment: .topLeading)
                                                .padding(.top, getRelativeHeight(15.0))
                                                .padding(.bottom, getRelativeHeight(16.0))
                                                .padding(.leading, getRelativeWidth(20.0))
                                        }
                                        //                                        } else {
                                        //                                            Text(StringConstants.kLblSelect)
                                        //                                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                        //                                                .fontWeight(.medium)
                                        //                                                .foregroundColor(ColorConstants.Cyan800.opacity(0.77))
                                        //                                                .fixedSize()
                                        //                                                .multilineTextAlignment(.leading)
                                        //                                                .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(18.0),
                                        //                                                       alignment: .topLeading)
                                        //                                                .padding(.top, getRelativeHeight(15.0))
                                        //                                                .padding(.bottom, getRelativeHeight(16.0))
                                        //                                                .padding(.leading, getRelativeWidth(20.0))
                                        //                                        }
                                        Spacer()
                                        Image("img_arrowup")
                                            .resizable()
                                            .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(6.0),
                                                   alignment: .center)
                                            .scaledToFit()
                                            .clipped()
                                            .padding(.vertical, getRelativeHeight(22.0))
                                            .padding(.trailing, getRelativeWidth(24.0))
                                    }
                                    //                    .bold()
                                    .foregroundColor(.blue)
                                    .padding(.vertical, 12)
                                }
                            }
                            .frame(width: getRelativeWidth(321.0), height: getRelativeHeight(50.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                    bottomRight: 13.0)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                       bottomRight: 13.0)
                                    .fill(ColorConstants.Cyan8003f1))
                            .padding(.leading, getRelativeWidth(13.0))
                        }
                    
                        .padding(.top, getRelativeHeight(7.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        let passwordIndex: Int = 2
                        CustomTextField(title: "Password", placeholder: StringConstants.kMsg, text: self.$viewModel.passwordText, invalidText: self.viewModel.passwordErrorText, isSecure: true)
                            .keyboardType(.default)
                            .textContentType(.oneTimeCode)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.viewModel.passwordText) { _, newValue in
                                withAnimation(.bouncy) {
                                    self.viewModel.passwordErrorText = newValue.isEmptyOrWhitespace
                                        ? "Password field can't be empty"
                                        : nil
                                
//                                    self.viewModel.confirmPasswordErrorText = newValue != self.viewModel.confirmPasswordText ? "password mismatch with confirm password field" : nil
                                }
                            }
                            .focused($focusedField, equals: .field(passwordIndex))
                            .focusable(fieldIndex: passwordIndex, focusedFieldIndex: $focusedFieldIndex)
                    
                        let confirmPasswordIndex: Int = 3
                        CustomTextField(title: "Confirm Password", placeholder: StringConstants.kMsg, text: self.$viewModel.confirmPasswordText, invalidText: self.viewModel.confirmPasswordErrorText, isSecure: true)
                            .keyboardType(.default)
                            .textContentType(.oneTimeCode)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.viewModel.confirmPasswordText) { _, newValue in
                                withAnimation(.bouncy) {
                                    self.viewModel.confirmPasswordErrorText = newValue.isEmptyOrWhitespace
                                        ? "Confirm Password field can't be empty"
                                        : newValue != self.viewModel.passwordText ? "password mismatch with password field" : nil
                                
//                                    self.viewModel.passwordErrorText = newValue != self.viewModel.passwordText ? "password mismatch with confirm password field" : nil
                                }
                            }
                            .focused($focusedField, equals: .field(confirmPasswordIndex))
                            .focusable(fieldIndex: confirmPasswordIndex, focusedFieldIndex: $focusedFieldIndex)
                        
                        HStack {
                            CheckBoxView(checked: $viewModel.isTermsAccepted)
                              
                            Button {
                                let routesType: RoutesType = routerManager.mapRouterWithTab(appState: appState)
                                routerManager.push(to: .customRichTextPage(type: .terms), where: routesType)
                            } label: {
                                Text("Agree with \(Text("Terms and Conditions").underline())")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .multilineTextAlignment(.leading)
                            }
                        }
                        
                        .padding(.top, getRelativeHeight(16))
                        .padding(.horizontal, getRelativeWidth(12))
                        .frame(width: getRelativeWidth(321.0), alignment: .leading)
                       
                        VStack {
                            Button(action: {
                                self.viewModel.onRegister { user in
//                                    self.dismiss()
                                    let routeType = routerManager.mapRouterWithTab(appState: appState)
                                    routerManager.push(to: .signUpOtp(userID: user.id, isEmail: viewModel.emailText.isEmail, viewModel: viewModel), where: routeType)
                                }
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("Sign Up")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
//                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
//                                        .frame(width: getRelativeWidth(320.0),
//                                               height: getRelativeHeight(50.0), alignment: .center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            }).disableWithOpacity(self.viewModel.isSignUpDisabled)
                        
                            VStack {
                                HStack {
                                    Divider()
                                        .frame(width: getRelativeWidth(115.0),
                                               height: getRelativeHeight(1.0), alignment: .bottom)
                                        .background(ColorConstants.Cyan80087)
                                        .padding(.top, getRelativeHeight(9.0))
                                        .padding(.bottom, getRelativeHeight(6.0))
                                    Spacer()
                                    Text("or sign with")
                                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                        .fontWeight(.regular)
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .fixedSize()
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(17.0), alignment: .topLeading)
                                    Spacer()
                                    Divider()
                                        .frame(width: getRelativeWidth(113.0),
                                               height: getRelativeHeight(1.0), alignment: .bottom)
                                        .background(ColorConstants.Cyan80087)
                                        .padding(.top, getRelativeHeight(9.0))
                                        .padding(.bottom, getRelativeHeight(6.0))
                                }
                                .frame(height: getRelativeHeight(17.0),
                                       alignment: .leading)
                                HStack {
                                    Button(action: {
                                        authService.appleSignIn()
                                    }, label: {
                                        Image("img_itemsign")
                                    })
                                    .frame(width: getRelativeWidth(65.0), height: getRelativeWidth(65.0),
                                           alignment: .center)
                                    .overlay(Circle()
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                    .background(Circle()
                                        .fill(ColorConstants.Cyan8007f))
                                    Button(action: {
                                        authService.googleSignIn()
                                    }, label: {
                                        Image("img_itemsign_white_a700")
                                    })
                                    .frame(width: getRelativeWidth(65.0), height: getRelativeWidth(65.0),
                                           alignment: .center)
                                    .overlay(Circle()
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                    .background(Circle()
                                        .fill(ColorConstants.Cyan8007f))
                                    .padding(.leading, getRelativeWidth(15.0))
//                                    Button(action: {}, label: {
//                                        Image("img_itemsign_white_a700_65x65")
//                                    })
//                                    .frame(width: getRelativeWidth(65.0), height: getRelativeWidth(65.0),
//                                           alignment: .center)
//                                    .overlay(RoundedCorners(topLeft: 32.5, topRight: 32.5, bottomLeft: 32.5,
//                                                            bottomRight: 32.5)
//                                            .stroke(ColorConstants.Cyan800,
//                                                    lineWidth: 1))
//                                    .background(RoundedCorners(topLeft: 32.5, topRight: 32.5,
//                                                               bottomLeft: 32.5, bottomRight: 32.5)
//                                            .fill(ColorConstants.Cyan8007f))
//                                    .padding(.leading, getRelativeWidth(15.0))
                                }
                                .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(65.0),
                                       alignment: .center)
                                .padding(.top, getRelativeHeight(29.0))
                                .padding(.horizontal, getRelativeWidth(46.0))
                            }
                            .frame(width: getRelativeWidth(315.0), height: getRelativeHeight(111.0),
                                   alignment: .center)
                            .padding(.top, getRelativeHeight(31.0))
                            .padding(.leading, getRelativeWidth(10.0))
                            .padding(.trailing, getRelativeWidth(5.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(193.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                    
                        VStack(alignment: .leading, spacing: 0) {
                            HStack {
                                Text("Don’t have an account ?")
                                Button {
                                    self.dismiss()
                                } label: {
                                    Text("Sign In")
                                        .foregroundColor(ColorConstants.Cyan800)
                                }
                            }
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(186.0), height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.horizontal, getRelativeWidth(72.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(18.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(53.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
               
                .padding(.bottom, 32.0.relativeHeight)
            }
            .background(ColorConstants.bgGradient)
        }
        
        .coordinateFocus(focusedField: _focusedField, focusedFieldIndex: $focusedFieldIndex)
        .addKeyboardNavigation(focusedFieldIndex: $focusedFieldIndex, totalFields: 4, onDismiss: dismissKeyboard)
        .onChange(of: authService.googleUser) { viewModel.handleSuccessfulGooleLogin(with: $1, completion: afterSuccessLogin) }
        .onChange(of: authService.appleUser) { viewModel.handleSuccessfulAppleLogin(with: $1, completion: afterSuccessLogin) }
        .overlay {
            VStack {
                if viewModel.showPopUpType != .hide {
                    CustomDatePicker(date: $viewModel.selectedDate, range: .to(Date()), isShowCalendar: $viewModel.showPopUpType, changed: viewModel.onChangeDate)
                        .transition(.scale.combined(with: .opacity))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(.black.opacity(0.01))
                }
            }.animation(.easeInOut, value: viewModel.showPopUpType)
        }
    }
    
    private func dismissKeyboard() {
        focusedField = nil
        focusedFieldIndex = nil
    }
    
    func afterSuccessLogin(_ user: UserModel) {
        appState.onAuthentication(user)
        routerManager.popToAllRoot()
    }
}

struct SignUpView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            SignUpView().attachAllEnvironmentObjects()
        }
    }
}

struct CustomTextField: View {
    let title, placeholder: LocalizedStringKey
    @Binding var text: String
    var invalidText: String?
    var isSecure: Bool = false
    var isPhone: Bool = false
    var isOptional: Bool = false
    @State private var hidePassword: Bool = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(spacing: 4) {
                Text(self.title)
                
                if !self.isOptional {
                    Text("*")
                        .foregroundColor(.red)
                }
            }
            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
            .fontWeight(.medium)
            .foregroundColor(ColorConstants.WhiteA700)
            .multilineTextAlignment(.leading)
            .frame(height: getRelativeHeight(18.0),
                   alignment: .topLeading)
            .padding(.horizontal, getRelativeWidth(12.0))
            Group {
                HStack {
//                    if self.isPhone {
//                        TextField(StringConstants.kLblEstherHoward,
//                                  text: .constant("+965"), prompt: Text("Enter Your Name").foregroundColor(ColorConstants.Cyan800.opacity(0.77)))
//                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
//                            .padding(8.0.relativeFontSize)
                    ////                            .fixedSize()
//                            .disabled(true)
//                            .frame(width: getRelativeWidth(60.0), height: getRelativeHeight(50.0),
//                                   alignment: .center)
//                            .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                                    bottomRight: 13.0)
//                                    .stroke(ColorConstants.Cyan800,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                                       bottomRight: 13.0)
//                                    .fill(ColorConstants.Cyan8003f1))
//                            .padding(.top, getRelativeHeight(7.0))
//                    }
                    
                    HStack {
                        if self.isSecure && self.hidePassword {
                            SecureField(self.placeholder, text: self.$text, prompt: Text(self.placeholder).foregroundColor(ColorConstants.Cyan800.opacity(0.77)))
                                .textContentType(nil)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                .padding()
                            
                        } else {
                            TextField(self.placeholder, text: self.$text, prompt: Text(self.placeholder).foregroundColor(ColorConstants.Cyan800.opacity(0.77)))
                                .textInputAutocapitalization(.sentences)
                                .autocorrectionDisabled(true) // Optional: enables autocorrect if needed
                                .keyboardType(.default)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                .padding()
                        }
                    }
                    .if(self.isSecure, transform: {
                        $0.overlay(alignment: .trailing) {
                            Button(action: { self.hidePassword.toggle() }, label: {
                                Image(systemName: !self.hidePassword ? "eye.fill" : "eye.slash.fill")
                                    .foregroundStyle(ColorConstants.Cyan800)
                                    .padding()
                                   
                            })
                        }
                    })
                    
                    .frame(height: getRelativeHeight(50.0),
                           alignment: .leading)
                    .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                            bottomRight: 13.0)
                            .stroke(ColorConstants.Cyan800,
                                    lineWidth: 1))
                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0,
                                               bottomLeft: 13.0, bottomRight: 13.0)
                            .fill(ColorConstants.Cyan8003f1))
                    .padding(.top, getRelativeHeight(7.0))
                }
                .foregroundStyle(ColorConstants.WhiteA700)
                
                if let invalidText = invalidText, !invalidText.isEmptyOrWhitespace {
                    Text(LocalizedStringKey(invalidText))
                        .foregroundColor(Color.red)
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                        .padding(.top, 8)
                        .padding(.leading)
                }
            }
        }
        .tint(ColorConstants.WhiteA700)
    }
}

// struct CustomDatePicker: View {
//    @Binding var date: Date
//
//    @State private var showPicker: Bool = false
//    @State private var selectedDateText: String = "Date"
//
//    private var selectedDate: Binding<Date> {
//        Binding<Date>(get: { self.date }, set: {
//            self.date = $0
//            self.setDateString()
//        })
//    } // This private var I found… somewhere. I wish I could remember where
//
//    // To take the selected date and store it as a string for the text field
//    private func setDateString() {
//        let formatter = DateFormatter()
//        formatter.dateFormat = "MMMM dd, yyyy"
//
//        self.selectedDateText = formatter.string(from: self.date)
//    }
//
//    var body: some View {
//        VStack {
//            HStack {
//                Text("Date:")
//                    .frame(alignment: .leading)
//
//                TextField("", text: self.$selectedDateText)
//                    .onAppear {
//                        self.setDateString()
//                    }
//                    .disabled(true)
//                    .onTapGesture {
//                        self.showPicker.toggle()
//                    }
//                    .multilineTextAlignment(.trailing)
//            }
//
//            if self.showPicker {
//                DatePicker("", selection: self.selectedDate,
//                           displayedComponents: .date)
//                    .datePickerStyle(WheelDatePickerStyle())
//                    .labelsHidden()
//            }
//        }
//    }
// }

/// An enum to identify each text field uniquely
enum TextFieldFocus: Hashable {
    case field(Int)
    case none
}

// MARK: - Keyboard Navigation Extensions

struct KeyboardNavigationToolbar: ViewModifier {
    @Binding var focusedFieldIndex: Int?
    let totalFields: Int
    let onDismiss: () -> Void
    
    func body(content: Content) -> some View {
        content
            .toolbar {
                ToolbarItemGroup(placement: .keyboard) {
                    Button(action: {
                        moveToPreviousField()
                    }) {
                        Image(systemName: "chevron.up")
                    }
                    .tint(Color(UIColor.systemBlue))
                    .disabled(focusedFieldIndex == nil || focusedFieldIndex == 0)
                    
                    Button(action: {
                        moveToNextField()
                    }) {
                        Image(systemName: "chevron.down")
                    }
                    .tint(Color(UIColor.systemBlue))
                    .disabled(focusedFieldIndex == nil || focusedFieldIndex == totalFields - 1)
                    
                    Spacer()
                    
                    Button("Done") {
                        onDismiss()
                    }
                    .tint(Color(UIColor.systemBlue))
                }
            }
    }
    
    private func moveToNextField() {
        if let current = focusedFieldIndex, current < totalFields - 1 {
            focusedFieldIndex = current + 1
        }
    }
    
    private func moveToPreviousField() {
        if let current = focusedFieldIndex, current > 0 {
            focusedFieldIndex = current - 1
        }
    }
}

struct FocusStateCoordinator: ViewModifier {
    @FocusState var focusedField: TextFieldFocus?
    @Binding var focusedFieldIndex: Int?
    
    func body(content: Content) -> some View {
        content
            .onChange(of: focusedFieldIndex) { _, newValue in
                if let newIndex = newValue {
                    focusedField = .field(newIndex)
                } else {
                    focusedField = nil
                }
            }
            .onChange(of: focusedField) { _, newValue in
                if case .field(let index) = newValue {
                    focusedFieldIndex = index
                } else {
                    focusedFieldIndex = nil
                }
            }
    }
}

extension View {
    /// Adds keyboard navigation with arrow buttons and a done button to the view
    func addKeyboardNavigation(focusedFieldIndex: Binding<Int?>, totalFields: Int, onDismiss: @escaping () -> Void = {}) -> some View {
        modifier(KeyboardNavigationToolbar(focusedFieldIndex: focusedFieldIndex, totalFields: totalFields, onDismiss: onDismiss))
    }
    
    /// Coordinates focus state with the field index binding
    func coordinateFocus(focusedField: FocusState<TextFieldFocus?>, focusedFieldIndex: Binding<Int?>) -> some View {
        modifier(FocusStateCoordinator(focusedField: focusedField, focusedFieldIndex: focusedFieldIndex))
    }
    
    /// Makes a field focusable by index
    func focusable(fieldIndex: Int, focusedFieldIndex: Binding<Int?>) -> some View {
        onTapGesture {
            focusedFieldIndex.wrappedValue = fieldIndex
        }
    }
}
