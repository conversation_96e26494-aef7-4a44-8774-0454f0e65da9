//
//  AuthService.swift
//  Bookme
//
//  Created by Apple on 10/09/2024.
//

import AuthenticationServices
import GoogleSignIn
import CryptoKit

class AuthService: NSObject, ObservableObject {
    
    @Published var googleUser: SocialUserModel?
    @Published var appleUser: SocialUserModel?
    
    fileprivate var currentNonce: String?
    
    
    public func appleSignIn() {
        let nonce = randomNonceString()
        currentNonce = nonce
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        let request = appleIDProvider.createRequest()
        request.requestedScopes = [.email, .fullName]
        request.nonce = sha256(nonce)
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.performRequests()
    }

    // Apple sign in...
    public func googleSignIn() {
        // As you’re not using view controllers to retrieve the presentingViewController, access it through
        // the shared instance of the UIApplication
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else { return }
        guard let rootViewController = windowScene.windows.first?.rootViewController else { return }

        // Start the sign in flow!
        GIDSignIn.sharedInstance.signIn(withPresenting: rootViewController) { signInResult, error in
            if let error = error {print("Error doing Google Sign-In, \(error)");return}
            guard let signInResult = signInResult
            else {
                print("Error during Google Sign-In authentication, \(error?.localizedDescription ?? "")")
                return
            }
            let user = signInResult.user
            let photo = user.profile?.imageURL(withDimension: 100)
            let socialUserModel: SocialUserModel = .init(id: user.userID, fullName: user.profile?.name ,firstName: user.profile?.givenName, lastName: user.profile?.familyName, email: user.profile?.email, photo: photo?.absoluteString, type: .google)
           
            DispatchQueue.main.async {
                self.googleUser = socialUserModel
            }
            
 
        }
    }
    
    // Sign out if used Single-sign-on with Google
    public func googleSignOut() {
        GIDSignIn.sharedInstance.signOut()
        print("Google sign out")
    }
}

extension AuthService: ASAuthorizationControllerDelegate {
    
    public func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIdCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            guard let nonce = currentNonce else { return }
            guard let appleIdToken = appleIdCredential.identityToken else { return }
            guard let idTokenString = String(data: appleIdToken, encoding: .utf8) else { return }
            
            let user: ASAuthorizationAppleIDCredential = appleIdCredential
            let fullName = user.fullName
            let socialUserModel: SocialUserModel = .init(id: user.user, fullName: "\(fullName?.givenName ?? "") \(fullName?.familyName ?? "")" ,firstName: user.fullName?.givenName, lastName: user.fullName?.familyName, email: user.email, photo: nil, type: .apple)
            
            DispatchQueue.main.async {
                self.appleUser = socialUserModel
            }
        }
    }
    
    
    public func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: any Error) {
        print("Authorization Error: \(error.localizedDescription)")
    }
}



extension AuthService {
    
    private func randomNonceString(length: Int = 32) -> String {
        precondition(length > 0)
        var randomBytes = [UInt8](repeating: 0, count: length)
        let errorCode = SecRandomCopyBytes(kSecRandomDefault, randomBytes.count, &randomBytes)
        if errorCode != errSecSuccess {
            fatalError(
                "Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)"
            )
        }

        let charset: [Character] =
            Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")

        let nonce = randomBytes.map { byte in
            // Pick a random character from the set, wrapping around if needed.
            charset[Int(byte) % charset.count]
        }

        return String(nonce)
    }
    
    private func sha256(_ input: String) -> String {
        let inputData = Data(input.utf8)
        let hashedData = SHA256.hash(data: inputData)
        let hashString = hashedData.compactMap {
            String(format: "%02x", $0)
        }.joined()

        return hashString
    }
}
