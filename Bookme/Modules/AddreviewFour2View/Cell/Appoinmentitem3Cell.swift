import SwiftUI

struct Appoinmentitem3Cell: View {
    @State var tabGroup226List: [String] = ["ReBook", "Receipt"]
    @State var selectedGroup226Index: Int = 0
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(StringConstants.kMsgNov222023)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(121.0), height: getRelativeHeight(17.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(15.0))
                .padding(.horizontal, getRelativeWidth(17.0))
            Divider()
                .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, getRelativeHeight(10.0))
                .padding(.horizontal, getRelativeWidth(14.0))
            ZStack(alignment: .topTrailing) {
                Image("img_rectangle10")
                    .resizable()
                    .frame(width: getRelativeWidth(72.0), height: getRelativeWidth(74.0),
                           alignment: .leading)
                    .scaledToFit()
                    .clipShape(Circle())
                    .clipShape(Circle())
                    .padding(.bottom, getRelativeHeight(15.35))
                    .padding(.trailing, getRelativeWidth(256.0))
                VStack(alignment: .leading, spacing: 0) {
                    Text(StringConstants.kMsgBroadwayBarber)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(174.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                        .padding(.trailing)
                    Text(StringConstants.kMsg4thFloorAlZ)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900B2)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(33.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(56.0),
                       alignment: .leading)
                .padding(.bottom, getRelativeHeight(38.0))
                .padding(.leading, getRelativeWidth(89.87))
                VStack {
                    Text(StringConstants.kLblAddReview)
                        .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(65.0), height: getRelativeHeight(17.0),
                               alignment: .leading)
                        .padding(.bottom, getRelativeHeight(4.0))
                        .padding(.horizontal, getRelativeWidth(10.0))
                }
                .frame(width: getRelativeWidth(85.0), height: getRelativeHeight(25.0),
                       alignment: .leading)
                .overlay(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                        bottomRight: 12.5)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                           bottomRight: 12.5)
                        .fill(ColorConstants.Cyan8003f))
                .padding(.top, getRelativeHeight(61.65))
                .padding(.leading, getRelativeWidth(243.0))
                Text(StringConstants.kLbl6Km)
                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                    .fontWeight(.semibold)
                    .foregroundColor(ColorConstants.Black900B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(17.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(61.92))
                    .padding(.trailing, getRelativeWidth(195.5))
                Image("img_map_cyan_800")
                    .resizable()
                    .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(30.0),
                           alignment: .leading)
                    .scaledToFit()
                    .padding(.top, getRelativeHeight(64.04))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(94.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(13.0))
            .padding(.horizontal, getRelativeWidth(14.0))
//            TabsView(tabs: $appoinmentitem3CellModel.tabGroup226List,
//                     selectedTabIndex: $appoinmentitem3CellModel.selectedGroup226Index,
//                     selectedTab: appoinmentitem3CellModel.tabGroup226List[0], fontSize: 14.0,
//                     fontName: FontScheme.kNunitoBold(size: getRelativeHeight(14.0)),
//                     selectedFontColor: ColorConstants.WhiteA700,
//                     unSelectedFontColor: ColorConstants.WhiteA700,
//                     selectedBackColor: ColorConstants.Cyan800, cornerRadius: 17.5,
//                     type: .customBackground)
//                .frame(height: getRelativeHeight(35.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0, bottomRight: 20.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                   bottomRight: 20.0)
                .fill(ColorConstants.WhiteA700))
        .hideNavigationBar()
    }
}

/* struct Appoinmentitem3Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Appoinmentitem3Cell()
 }
 } */
