import SwiftUI

struct AddreviewFour2View: View {
    @StateObject var addreviewFour2ViewModel = AddreviewFour2ViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        ZStack(alignment: .leading) {
            HStack {
                HStack {
                    HStack {
                        Image("img_arrowleft_white_a700")
                            .resizable()
                            .frame(width: getRelativeWidth(16.0), height: getRelativeHeight(12.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                            .onTapGesture {
                                self.presentationMode.wrappedValue.dismiss()
                            }
                        Text(StringConstants.kLblAppointments)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(103.0), height: getRelativeHeight(22.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(20.0))
                    }
                    .frame(width: getRelativeWidth(139.0), height: getRelativeHeight(22.0),
                           alignment: .center)
                    Spacer()
                    Image("img_notification")
                        .resizable()
                        .frame(width: getRelativeWidth(20.0), height: getRelativeHeight(19.0),
                               alignment: .center)
                        .scaledToFit()
                        .clipped()
                }
                .frame(width: getRelativeWidth(346.0), height: getRelativeHeight(22.0),
                       alignment: .leading)
            }
            .frame(width: getRelativeWidth(346.0), height: getRelativeHeight(22.0),
                   alignment: .leading)
            .padding(.bottom, getRelativeHeight(881.29))
            .padding(.horizontal, getRelativeWidth(22.0))
            ScrollView(.vertical, showsIndicators: false) {
                ZStack(alignment: .topLeading) {
                    VStack {
                        VStack {
                            Text(StringConstants.kMsgUpcoming)
                                .font(FontScheme.kNeusaNextStdMedium(size: getRelativeHeight(14.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(351.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                                .padding(.top, getRelativeHeight(6.0))
                                .padding(.horizontal, getRelativeWidth(19.0))
                        }
                        .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(28.0),
                               alignment: .leading)
                        .overlay(RoundedCorners().stroke(ColorConstants.Cyan800, lineWidth: 1))
                        .background(RoundedCorners().fill(Color.clear.opacity(0.7)))
                        Divider()
                            .frame(width: getRelativeWidth(68.0), height: getRelativeHeight(4.0),
                                   alignment: .center)
                            .background(ColorConstants.Cyan800)
                            .padding(.horizontal, getRelativeWidth(159.0))
                    }
                    .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(32.0),
                           alignment: .topLeading)
                    .padding(.bottom, getRelativeHeight(825.78))
                    VStack {
                        VStack(spacing: 0) {
                            ScrollView(.vertical, showsIndicators: false) {
                                LazyVStack {
                                    ForEach(0 ... 0, id: \.self) { index in
                                        Appoinmentitem3Cell()
                                    }
                                }
                            }
                        }
                        .frame(width: getRelativeWidth(360.0), alignment: .center)
                        .padding(.vertical, getRelativeHeight(20.0))
                        .padding(.horizontal, getRelativeWidth(15.0))
                    }
                    .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(357.0),
                           alignment: .topLeading)
                    .background(ColorConstants.WhiteA700)
                    .padding(.bottom, getRelativeHeight(472.78))
                    PagerView(content: {}, tabIndex: $addreviewFour2ViewModel.tabIndex,
                              pagers: $addreviewFour2ViewModel.pagers)
                        .frame(height: getRelativeHeight(570.0), alignment: .bottomLeading)
                    ZStack {}
                        .hideNavigationBar()
                        .frame(width: UIScreen.main.bounds.width,
                               height: UIScreen.main.bounds.height,
                               alignment: .topLeading)
                        .background(ColorConstants.Black90059)
                }
                .hideNavigationBar()
                .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                       alignment: .topLeading)
            }
            Text("tabbar")
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(60.0),
                       alignment: .bottomLeading)
                .background(ColorConstants.Cyan800)
                .shadow(color: ColorConstants.Black90019, radius: 5, x: 0, y: -4)
                .padding(.top, getRelativeHeight(863.22))
        }
        .hideNavigationBar()
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(LinearGradient(gradient: Gradient(colors: [ColorConstants.Cyan900,
                                                               ColorConstants.Black901]),
            startPoint: .topLeading, endPoint: .bottomTrailing))
    }
}

struct AddreviewFour2View_Previews: PreviewProvider {
    static var previews: some View {
        AddreviewFour2View()
    }
}
