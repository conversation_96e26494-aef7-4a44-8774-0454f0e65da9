import SwiftUI

struct ShopdetailsReviewView: View {
    @StateObject var shopdetailsReviewViewModel = ShopdetailsReviewViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var shopDetailsViewModel: ShopDetailsViewModel
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    @Binding var selectedReview: VendorDetailsModel.Review?
    @State var selectedLikeReview: VendorDetailsModel.Review?
    var body: some View {
//            ScrollView(.vertical, showsIndicators: false) {
        if let vendorDetailsModel = shopDetailsViewModel.vendorDetailsModel {
            let ratingComponents: [String] = vendorDetailsModel.rating.components(separatedBy: "/")
            let firstComponent = ratingComponents.first ?? "0"
            let ratingValue = String(format: "%.1f", Double(firstComponent) ?? "0")
            VStack {
                VStack(alignment: .trailing, spacing: 0) {
                    HStack {
                        Spacer()
                        VStack {
                            Text("\(ratingValue)\(Text("/5").font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0))))")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(20.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .multilineTextAlignment(.leading)
                                .frame(
                                    height: getRelativeHeight(28.0), alignment: .topLeading)
                                .padding(.horizontal, getRelativeWidth(40.0))
                                       
                            RatingBarView(selected: .constant( Double(firstComponent) ?? 0), heightWeightImage: 18.0.relativeWidth, spaceBetween: 12.0.relativeWidth)
                                .frame(
                                    height: getRelativeHeight(13.0), alignment: .leading)
                                .padding(.top, getRelativeHeight(15.0))
                                .padding(.horizontal, getRelativeWidth(4.0))
                                       
                            Text("Based On \(vendorDetailsModel.reviewcount) Reviews")
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Black900B2)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(136.0),
                                       height: getRelativeHeight(17.0), alignment: .center)
                                .padding(.top, getRelativeHeight(13.0))
                        }
                        .frame(width: getRelativeWidth(136.0), height: getRelativeHeight(86.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(43.0))
                        .padding(.bottom, getRelativeHeight(45.0))
                        Spacer()
                        Divider()
                            .frame(width: getRelativeWidth(1.0),
                                   height: getRelativeHeight(139.0), alignment: .center)
                            .background(ColorConstants.Cyan800)
                            .padding(.top, getRelativeHeight(17.0))
                            .padding(.bottom, getRelativeHeight(20.0))
                        Spacer()
                        VStack(alignment: .leading, spacing: 0) {
                            VStack(spacing: 0) {
                                //                                    ScrollView(.vertical, showsIndicators: false) {
                                LazyVStack(alignment: .leading) {
                                    ForEach(1 ... 5, id: \.self) { index in
                                        
                                        let value = switch index {
                                        case 1: vendorDetailsModel.ratingonestarcount
                                        case 2: vendorDetailsModel.ratingtwostarcount
                                        case 3: vendorDetailsModel.ratingthreestarcount
                                        case 4: vendorDetailsModel.ratingfourstarcount
                                        case 5: vendorDetailsModel.ratingfivestarcount
                                        default:
                                            0
                                        }
                                        
                                        RateiteCell(iteration: index, percentage: CGFloat(value))
                                    }
                                }
                                //                                    }
                            }
                        }
                                    
                        .padding(.top, getRelativeHeight(20.0))
                        .padding(.bottom, getRelativeHeight(21.0))
                        Spacer()
                    }
                    .frame(width: getRelativeWidth(341.0), height: getRelativeHeight(176.0),
                           alignment: .center)
                    .overlay(RoundedCorners(topLeft: 16.0, topRight: 16.0, bottomLeft: 16.0,
                                            bottomRight: 16.0)
                            .stroke(ColorConstants.Cyan800,
                                    lineWidth: 1))
                    .background(RoundedCorners(topLeft: 16.0, topRight: 16.0, bottomLeft: 16.0,
                                               bottomRight: 16.0)
                            .fill(ColorConstants.WhiteA700))
                    Text("Reviews")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(66.0), height: getRelativeHeight(22.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(20.0))
                        .padding(.leading, getRelativeWidth(5.0))
                        .padding(.trailing, getRelativeWidth(271.0))
                    Text( "Book Me App Guarantees that reviews with the “Verified Book Me User” tag have been added by registered Book Me Useres who have had anappoinment with the provider. A registered Book Me user has the opportunity to add a review only after the service has been provides to them")
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900B2)
                        //                            .minimumScaleFactor(0.5)
                        .lineSpacing(3)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(333.0), height: getRelativeHeight(120.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(13.0))
                        .padding(.leading, getRelativeWidth(6.0))
                   
                    LazyVStack(spacing: 22.0.relativeHeight) {
                        ForEach(vendorDetailsModel.reviews) { model in
                            VStack(spacing: 20.0.relativeHeight) {
                                let routesType = routerManager.mapRouterWithTab(appState: appState)
                                ColumnellipseCell(model: model, isLikeLoading: model.id == selectedLikeReview?.id ? $shopDetailsViewModel.isLikeLoading : .constant(false), isReportLoading: model.id == selectedLikeReview?.id ? $shopDetailsViewModel.isReportLoading : .constant(false)) { review, onSuccess in
                                    selectedLikeReview = review
                                    shopDetailsViewModel.onLikeReview(model: review) {
                                        onSuccess()
                                    } onAuthFail: {
                                        routerManager.push(to: .signIn(type: routesType, getBack: true, onGetBack: .init(onBack: {
                                            shopDetailsViewModel.getVendorDetails(id: shopDetailsViewModel.vendorID)
                                        })), where: routesType)
                                    }

                                } onReport: { review, _ in
                                    if AppState.isLoggedIn {
                                        selectedReview = review
                                    } else {
                                        routerManager.push(to: .signIn(type: routesType, getBack: true, onGetBack: .init(onBack: {
                                            shopDetailsViewModel.getVendorDetails(id: shopDetailsViewModel.vendorID)
                                        })), where: routesType)
                                    }
                                }

                                Divider()
                                    .frame(width: getRelativeWidth(335.0), height: getRelativeHeight(1.0),
                                           alignment: .center)
                                    .background(ColorConstants.Cyan80082)
                                    .padding(.horizontal, getRelativeWidth(7.0))
                            }
                        }
                    }
                    
                    .frame(width: getRelativeWidth(328.0), alignment: .center)
                    .padding(.top, getRelativeHeight(11.0))
                    .padding(.leading, getRelativeWidth(9.0))
                    .padding(.trailing, getRelativeWidth(5.0))
                }
                .frame(width: getRelativeWidth(342.0),
                       alignment: .topLeading)
                .padding(.horizontal, getRelativeWidth(21.0))
            }
          
            .padding(.bottom, 64.0.relativeHeight)
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        }
    }
}

// struct ShopdetailsReviewView_Previews: PreviewProvider {
//    static var previews: some View {
//        ShopdetailsReviewView()
//    }
// }
