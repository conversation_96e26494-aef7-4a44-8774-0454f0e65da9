import SwiftUI

struct ColumnellipseCell: View {
    @State var model: VendorDetailsModel.Review
    @Binding var isLikeLoading: Bool
    @Binding var isReportLoading: Bool 
    let onLike: (VendorDetailsModel.Review, _ onSuccess: @escaping () -> Void) -> Void
    let onReport: (VendorDetailsModel.Review, _ onSuccess: @escaping () -> Void) -> Void
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                NetworkImageView(path: model.imageUrl)
                    .frame(width: getRelativeWidth(49.0), height: getRelativeWidth(51.0),
                           alignment: .leading)
                    .scaledToFit()
                    .clipShape(Circle())
                    .clipShape(Circle())
                    .onAppear {
                        print("model.imageUrl", model.imageUrl)
                    }
                Spacer()
                VStack(alignment: .leading, spacing: 6.0.relativeHeight) {
                    HStack {
                        Text(model.name)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .multilineTextAlignment(.leading)
                            .frame(height: getRelativeHeight(18.0),
                                   alignment: .leading)
                        Spacer()
                        HStack {
                            Image("img_star1_yellow_a700")
                                .resizable()
                                .frame(width: getRelativeWidth(13.0),
                                       height: getRelativeHeight(14.0), alignment: .leading)
                                .scaledToFit()
                            Text(String(format: "%.1f", Double(model.rating)))
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(18.0),
                                       height: getRelativeHeight(18.0), alignment: .leading)
                                .padding(.leading, getRelativeWidth(6.0))
                        }
                        .frame(width: getRelativeWidth(46.0), height: getRelativeHeight(18.0),
                               alignment: .leading)
                    }
                    .frame(width: getRelativeWidth(257.0), height: getRelativeHeight(18.0),
                           alignment: .leading)
                    HStack {
//                        Text(StringConstants.kLblKuwait)
//                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
//                            .fontWeight(.regular)
//                            .foregroundColor(ColorConstants.Cyan800B2)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(39.0), height: getRelativeHeight(17.0),
//                                   alignment: .leading)
//                            .padding(.bottom, getRelativeHeight(7.0))
                        Spacer()
                        Text(model.ratedDate ?? "")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Black900)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(height: getRelativeHeight(17.0),
                                   alignment: .trailing)
                            .padding(.top, getRelativeHeight(7.0))
                    }
                    .frame(height: getRelativeHeight(24.0),
                           alignment: .leading)
                }
                .frame(width: getRelativeWidth(257.0), height: getRelativeHeight(45.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(7.0))
            }
            .frame(width: getRelativeWidth(320.0), height: getRelativeHeight(52.0),
                   alignment: .leading)
            .padding(.trailing)
            VStack(alignment: .leading, spacing: 16) {
                
                if let quickReview = model.quickReview {
                    Text(quickReview)
                }
                
                if let comments = model.comments {
                    Text(comments)
                }
               
                
               
            }
            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
            .fontWeight(.regular)
            .foregroundColor(ColorConstants.Black900B2)
            .multilineTextAlignment(.leading)
            .frame(width: getRelativeWidth(294.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(22.0))
            .padding(.leading, getRelativeWidth(6.0))
            .padding(.trailing, getRelativeWidth(10.0))
            HStack {
                
               
                HStack(spacing: 8.0.relativeWidth) {
                    let reviewLiked: Bool = model.isLiked
                    Button(action: {
                        onLike(model, {
                            Utilities.enQueue(after: .now() + 0.2) {
                                model.isLiked.toggle()
                                if model.isLiked {
                                    model.likes += 1
                                }else{
                                    if model.likes > 0 {
                                        model.likes -= 1
                                    }
                                    
                                }
                               
                            }
                          
                        })
                    }, label: {
                        HStack(spacing: 16.0.relativeWidth) {
                            
                            if isLikeLoading {
                                ProgressView()
                            }else{
                                Text("\(model.likes)")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(reviewLiked ? ColorConstants.WhiteA700 : ColorConstants.Black900)
//                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(height: getRelativeHeight(17.0),
                                           alignment: .leading)
                                    
                                    .contentTransition(.numericText())
                                
                                Image("img_thumbup")
                                    .resizable()
                                    .renderingMode(.template)
                                    .foregroundStyle(reviewLiked ?  ColorConstants.WhiteA700 : ColorConstants.Cyan800)
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(20.0), height: getRelativeWidth(20.0),
                                           alignment: .leading)
                                //                            .padding(.trailing, getRelativeWidth(18.0))
                            }
                            
                          
                        }
                        .padding(.horizontal, getRelativeWidth(12.0))
                        .frame( height: getRelativeHeight(30.0),
                               alignment: .center)
                        .overlay(Capsule()
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(Capsule()
                                .fill(reviewLiked ? ColorConstants.Cyan800 : ColorConstants.WhiteA700))
                        .animation(.bouncy, value: isLikeLoading)
                        .animation(.bouncy, value: model.likes)
                    })
                    .disabled(isLikeLoading)
                   
//                    Button(action: {
//
//                    }, label: {
//                        HStack(spacing:16.0.relativeWidth) {
//                            Text("\(model.dislikes)")
//                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
//                                .fontWeight(.regular)
//                                .foregroundColor(ColorConstants.Black900)
//                                .minimumScaleFactor(0.5)
//                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(6.0), height: getRelativeHeight(17.0),
//                                       alignment: .leading)
//                                .padding(.leading, getRelativeWidth(13.0))
//                            Image("img_thumbup_cyan_800")
//                                .resizable()
//                                .scaledToFit()
//                                .frame(width: getRelativeWidth(20.0), height: getRelativeWidth(20.0),
//                                       alignment: .leading)
//
//    //                            .padding(.horizontal, getRelativeWidth(19.0))
//                        }
//                        .frame(width: getRelativeWidth(71.0), height: getRelativeHeight(30.0),
//                               alignment: .leading)
//                        .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
//                                                bottomRight: 15.0)
//                                .stroke(ColorConstants.Cyan800,
//                                        lineWidth: 1))
//                        .background(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
//                                                   bottomRight: 15.0)
//                                .fill(ColorConstants.WhiteA700))
//                    })
                }
                .frame(width: getRelativeWidth(151.0), height: getRelativeHeight(30.0),
                       alignment: .leading)
                Spacer()
                
                let reviewReported = model.isReported
                Button(action: {
                    onReport(model, {
                       
                        model.isReported = true
                    })
                }, label: {
                    HStack(spacing: 4) {
                        
                        if isReportLoading {
                            ProgressView()
                        }else{
                            
                            Text("Report")
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                .fontWeight(.regular)
                                .foregroundColor(reviewReported ? ColorConstants.WhiteA700 : ColorConstants.Black900)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(38.0), height: getRelativeHeight(17.0),
                                       alignment: .center)
                                
                            Image("img_rectangle1")
                                .resizable()
                                .renderingMode(.template)
                                .foregroundStyle(reviewReported ?  ColorConstants.WhiteA700 : ColorConstants.Cyan800)
                                .scaledToFit()
                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                       alignment: .leading)
                        }
                        
                       
                    }
                    .padding(.horizontal, getRelativeWidth(8.0))
                    .frame( height: getRelativeHeight(30.0),
                           alignment: .center)
                    .overlay(Capsule()
                            .stroke(ColorConstants.Cyan800,
                                    lineWidth: 1))
                    .background(Capsule()
                            .fill(reviewReported ? ColorConstants.Cyan800 : ColorConstants.WhiteA700))
                    .animation(.bouncy, value: isReportLoading)
                })
                .disabled(isReportLoading || reviewReported)
            }
            .frame(width: getRelativeWidth(322.0), height: getRelativeHeight(30.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(11.0))
            .padding(.leading, getRelativeWidth(4.0))
        }
        .frame(width: getRelativeWidth(326.0), alignment: .leading)
        .hideNavigationBar()
    }
}

// struct ColumnellipseCell_Previews: PreviewProvider {
//    static var previews: some View {
//        ColumnellipseCell()
//    }
// }
