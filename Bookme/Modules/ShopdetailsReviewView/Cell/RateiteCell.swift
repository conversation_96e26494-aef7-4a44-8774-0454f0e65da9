import SwiftUI

struct RateiteCell: View {
    let iteration: Int
    let percentage: CGFloat
    @State var percentageState: CGFloat = 0
    var body: some View {
        HStack {
            Text("\(iteration)")
                .font(FontScheme
                    .kNunitoRegular(size: getRelativeHeight(14.0)))
                .fontWeight(.regular)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(9.0),
                       height: getRelativeHeight(20.0),
                       alignment: .topLeading)
            LinearProgress(percentage: CGFloat(Double(percentageState) * 0.2),
                           backgroundColor: ColorConstants.Cyan8003f,
                           foregroundColor: LinearGradient(gradient: Gradient(colors: [ColorConstants
                                   .Amber600]),
                           startPoint: .leading,
                           endPoint: .trailing))
           
                .animation(.bouncy, value: percentageState)
                .onAppear {
                    let copy = percentage
                    let iterateTime = (Double(iteration) / 10.0) 
                    Utilities.enQueue(after: .now() + iterateTime) {
                        percentageState = copy
                    }
                }
               
                .frame(width: getRelativeWidth(104.0),
                       height: getRelativeHeight(5.0), alignment: .bottom)
                
                .padding(.vertical, getRelativeHeight(7.0))
                .padding(.leading, getRelativeWidth(9.0))
            Text("\(Int(percentage))")
                .font(FontScheme
                    .kNunitoRegular(size: getRelativeHeight(14.0)))
                .fontWeight(.regular)
                .foregroundColor(ColorConstants.Cyan800)
                .fixedSize()
                .multilineTextAlignment(.leading)
                .frame(
                    height: getRelativeHeight(20.0),
                    alignment: .topLeading)
                .padding(.leading, getRelativeWidth(6.0))
            Spacer()
        }
       
        .padding(.horizontal, getRelativeWidth(6.0))
        .clipShape(Capsule())
        .hideNavigationBar()
    }
}

struct RateiteCell_Previews: PreviewProvider {
    static var previews: some View {
        RateiteCell(iteration: 1, percentage: 5)
    }
}
