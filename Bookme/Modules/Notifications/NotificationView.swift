//
//  NotificationView.swift
//  Bookme
//
//  Created by Apple on 05/05/2024.
//

import SwiftUI

struct NotificationView: View {
    @StateObject private var viewModel: NotificationViewModel = .init()
    @Environment(\.dismiss) private var dismiss: DismissAction

    @EnvironmentObject var appState: AppState

    var body: some View {
        SuperView(pageState: self.$viewModel.pageState, loadingView: {
            NotificationShimmerView()
                .background(ColorConstants.bgGradient)
        }, content: {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Notification") {
                Group {
                    if self.viewModel.notificationArray.isEmpty {
                        CustomPlaceholder(placeholderType: .noData, title: "No notification", subTitle: "", image: .noNotification, size: .init(width: 78.relativeFontSize, height: 78.relativeFontSize), titleColor: .white)
                            .padding(.top, 32.relativeHeight)
                    } else {
                        VStack(alignment: .leading, spacing: 16) {
                            ForEach(self.viewModel.sectionHeaderArray.reversed(), id: \.self) { item in

                                Section {
                                    let dataArray = self.viewModel.notificationArray.filter { $0.createdAt.relativeDateString(using: NotificationModel.dateFormatString) == item }

                                    ForEach(dataArray.reversed()) { model in
                                        Button {
                                            self.onNotificationSelected(model: model)

                                        } label: {
                                            VStack(spacing: 12.0.relativeWidth) {
                                                NotificationCellView(model: model)
                                                    .padding(.horizontal, 13)
                                                if model != self.viewModel.notificationArray.last {
                                                    Divider().background(Color(hex: "#008F96"))
                                                }
                                            }
                                        }
                                    }
                                } header: {
                                    Text(item)
                                        .font(FontScheme.kNunitoMedium(size: 16.0.relativeFontSize))
                                        .fontWeight(.bold)
                                        .foregroundColor(Color(hex: "#008F96"))
                                        .multilineTextAlignment(.leading)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .clipped()
                                        .padding(.top)
                                        .padding(.horizontal)
                                }
                            }
                        }
                    }
                }

                .padding(.top)
            }
        }).background(ColorConstants.bgGradient)
    }

    func onNotificationSelected(model: NotificationModel) {
        if let type = model.type {
            switch type {
            case "order":
                guard let orderID = model.orderID
                else { print("Invalid or missing productID"); return }
                // Handle product navigation logic
                print("Navigate to product with ID: \(orderID)")
                self.appState.updateSelectedTab(.appointment)
            default:
                print("Unhandled notification type: \(type)")
            }
        }
    }
}

#Preview {
    NavigationStack {
        NotificationView().attachAllEnvironmentObjects()
    }
}

extension String {
    /// Converts a date string to a relative date string like "Today", "Yesterday", or a formatted date.
    /// - Parameters:
    ///   - format: The date format used in the input date string.
    /// - Returns: A relative date string ("Today", "Yesterday", or formatted date).
    func relativeDateString(using format: String) -> String? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format

        // Convert the string to a Date object
        guard let date = dateFormatter.date(from: self) else {
            return nil // Return nil if the date string is invalid
        }

        let calendar = Calendar.current

        // Check if the date is today
        if calendar.isDateInToday(date) {
            return "Today"
        }

        // Check if the date is yesterday
        if calendar.isDateInYesterday(date) {
            return "Yesterday"
        }

        // Otherwise, return a formatted date string
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        return dateFormatter.string(from: date)
    }
}
