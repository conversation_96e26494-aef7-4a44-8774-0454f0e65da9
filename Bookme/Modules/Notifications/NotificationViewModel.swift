//
//  NotificationViewModel.swift
//  Bookme
//
//  Created by Apple on 05/05/2024.
//

import Foundation

class NotificationViewModel: SuperViewModel {
    @Published var notificationArray: [NotificationModel] = []
    @Published var sectionHeaderArray: [String] = []

    override init() {
        super.init()
        getNotifications()
    }

    func getNotifications() {
        guard let userID = AppState.userModel?.user.id else { return }
        let params: [String: Any] = ["user_id": userID]
        onApiCall(api.allNotifications, parameters: params) {
            self.notificationArray = $0.data ?? []
            self.sectionHeaderArray = self.notificationArray.map { $0.createdAt.relativeDateString(using: NotificationModel.dateFormatString) ?? "" }
                .uniqued()
                .sorted { date1, date2 in
                    guard let d1 = NotificationModel.dateFormatter.date(from: date1),
                          let d2 = NotificationModel.dateFormatter.date(from: date2)
                    else {
                        return false
                    }
                    return d1 > d2 // Descending order
                }
        }
    }
}
