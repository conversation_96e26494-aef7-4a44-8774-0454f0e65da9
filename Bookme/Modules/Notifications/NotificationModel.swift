//
//  NotificationModel.swift
//  Bookme
//
//  Created by Apple on 05/05/2024.
//

import Foundation

// MARK: - NotificationModel

struct NotificationModel: Codable, Identifiable, Equatable {
   
    let notiID, userID: Int
    let notiTitle, notiMessage: String
    let readByUser: Int
    let createdAt: String
    let image: String
    let type, orderID: String?
    
    var id: Int { notiID }

    enum CodingKeys: String, CodingKey {
        case notiID = "noti_id"
        case userID = "user_id"
        case notiTitle = "noti_title"
        case notiMessage = "noti_message"
        case readByUser = "read_by_user"
        case createdAt = "created_at"
        case image, type
        case orderID = "order_id"
    }

//    var id: Int { notiID }

    static let dateFormatString: String = "yyyy-MM-dd HH:mm:ss"
    static let dateOnlyFormatString: String = "yyyy-MM-dd"
    
    static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd" // Match your date format
        return formatter
    }()
}
