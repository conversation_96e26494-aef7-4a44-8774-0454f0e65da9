//
//  NotificationShimmerView.swift
//  Bookme
//
//  Created by Apple on 31/08/2024.
//

import SwiftUI

struct NotificationShimmerView: View {
    

    let list:[String] = ["Today", "Yesterday", "16 April 2024 "]
    var body: some View {
        
        ScrollView {
            
            VStack(spacing: 16){
                
                ForEach(list, id:\.self){ item in
                
                    Section {
                        ForEach(0...3, id:\.self){ index in
                            Button {
                                
                            } label: {
                                VStack(spacing:12.0.relativeWidth){
                                    NotificationShimmerCellView()
                                        .padding(.horizontal)
                                        Divider().background(Color(hex: "#008F96"))
                                    }
                            }

                        }
                    } header: {
                        Text(item)
                            .font(FontScheme.kNunitoMedium(size: 16.0.relativeFontSize))
                            .fontWeight(.bold)
                            .foregroundColor(Color(hex:"#008F96"))
                            .multilineTextAlignment(.leading)
                            .frame(maxWidth: .infinity, alignment: .leading)
//                            .frame(height: 20)
//                            .background(.white)
                            .clipped()
                            .padding(.top)
                            .padding(.horizontal)
                    }

                }
                
                
                
            }
           
            .padding(.top)
            
        }
        
        .shimmerize()
     
       

    }
}

#Preview {
    NavigationStack{
        NotificationShimmerView().attachAllEnvironmentObjects()
    }
}


struct NotificationShimmerCellView: View {
   
    var body: some View {
        VStack{
            HStack(spacing:12.0.relativeWidth){
                
                
                Image("img_notification")
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(20.0),
                           height: getRelativeHeight(20.0), alignment: .center)
                    .foregroundColor(Color(hex: "#008F96"))
                    .clipped()
                   
                    .frame(width: 50.0.relativeFontSize, height: 50.0.relativeFontSize)
                    .background(Color(hex: "#008F96").opacity(0.23))
                    .clipShape(.circle)
                
                VStack{
                    Text(String(repeating: "congratulations, your booking for a relaxing spa session has been confirmed.", count: 2))
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.leading)
                }
            }
            
            
        }
       
    }
}
