//
//  NotificationCellView.swift
//  Bookme
//
//  Created by Apple on 05/05/2024.
//

import SwiftUI

struct NotificationCellView: View {
    let model: NotificationModel
    var body: some View {
        VStack(alignment: .leading) {
            HStack(spacing: 12.0.relativeWidth) {
                Image("img_notification")
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(20.0),
                           height: getRelativeHeight(20.0), alignment: .center)
                    .foregroundColor(Color(hex: "#008F96"))
                    .clipped()
                    .frame(width: 50.0.relativeFontSize, height: 50.0.relativeFontSize)
                    .background(Color(hex: "#008F96").opacity(0.23))
                    .clipShape(.circle)
                   
                VStack(alignment: .leading) {
                    Text(String(repeating: model.notiTitle, count: 1))
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.leading)
                        
                    Text(String(repeating: model.notiMessage, count: 1))
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(10.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.WhiteA700.opacity(0.6))
                        .multilineTextAlignment(.leading)
                }
            }
            
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

// #Preview {
//    NotificationCellView()
//        .padding()
//        .frame(maxWidth: .infinity, maxHeight: .infinity)
//        .background(ColorConstants.bgGradient)
// }
