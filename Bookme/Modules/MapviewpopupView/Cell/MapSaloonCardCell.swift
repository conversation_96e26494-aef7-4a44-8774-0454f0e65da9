import SwiftUI

struct MapSaloonCardCell: View {
    let model:SaloonMapModel.VendorModel
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            ZStack(alignment: .topTrailing) {
                NetworkImageView(path: model.vendorLogoUrl,contentMode: .fill)
                   
                    .frame(width: getRelativeWidth(186.0), height: getRelativeHeight(103.0),
                           alignment: .leading)
                    .background(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                               bottomRight: 3.0))
                    .clipped()
                    .onAppear{ print(model.vendorLogoUrl) }
                VStack(spacing:4) {
                    Image("img_vector_yellow_a700")
                        .resizable()
                        .scaledToFit()
                        .frame(width: getRelativeWidth(9.0), height: getRelativeWidth(9.0),
                               alignment: .leading)
                       
//                        .padding(.top, getRelativeHeight(5.0))
                        .padding(.horizontal, getRelativeWidth(13.0))
                    Text(model.rating ?? "0")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(16.0), height: getRelativeHeight(14.0),
                               alignment: .center)
                        .padding(.horizontal, getRelativeWidth(9.0))
                }
                .frame(width: getRelativeWidth(33.0), height: getRelativeWidth(35.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                           bottomRight: 17.5)
                        .fill(ColorConstants.Cyan800))
                .padding([.top,.trailing], 6)
            }
            .frame(width: getRelativeWidth(186.0), height: getRelativeHeight(103.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(10.0))
            .padding(.horizontal, getRelativeWidth(12.0))
            Text(model.vendorName)
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(14.0)))
                .fontWeight(.semibold)
                .foregroundColor(ColorConstants.Cyan800)
                
                .multilineTextAlignment(.leading)
                .frame(height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(10.0))
                .padding(.horizontal, getRelativeWidth(13.0))
            Text(model.address)
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .foregroundColor(ColorConstants.Gray800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(169.0), height: getRelativeHeight(36.0),
                       alignment: .leading)
                .padding(.horizontal, getRelativeWidth(13.0))
            HStack {
                Image("location_pin")
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(11.0), height: getRelativeHeight(11.0),
                           alignment: .leading)
                let formatedString = String(format:"%.2f KM",Float(model.distance.toDouble / 1000.0))
                Text("\(formatedString)")
                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                    .fontWeight(.semibold)
                    .foregroundColor(ColorConstants.Black900B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(26.0), height: getRelativeHeight(17.0),
                           alignment: .leading)
                    
            }
            .frame(width: getRelativeWidth(43.0), height: getRelativeHeight(17.0),
                   alignment: .leading)
            .padding(.vertical, getRelativeHeight(3.0))
            .padding(.horizontal, getRelativeWidth(13.0))
            .padding(.bottom,8)
        }
        .frame(width: getRelativeWidth(210.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 4.0, topRight: 4.0, bottomLeft: 4.0, bottomRight: 4.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 4.0, topRight: 4.0, bottomLeft: 4.0, bottomRight: 4.0)
            .fill(ColorConstants.WhiteA700))
        
    }
}
//
//struct Columnrectangle4218oneCell_Previews: PreviewProvider {
//    static var previews: some View {
//        MapSaloonCardCell()
//    }
//}
