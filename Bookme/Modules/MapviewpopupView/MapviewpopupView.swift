import SwiftUI

struct MapviewpopupView: View {
    @StateObject var mapviewpopupViewModel = MapviewpopupViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            ZStack(alignment: .leading) {
//                Image("img_basemapimage_820x390")
//                    .resizable()
//                    .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(820.0),
//                           alignment: .center)
//                    .scaledToFit()
//                    .clipped()
                VStack {
//                    HStack {
//                        HStack {
//                            Button(action: {
//                                self.presentationMode.wrappedValue.dismiss()
//                            }, label: {
//                                Image("img_arrowleft")
//                            })
//                            .frame(width: getRelativeWidth(49.0), height: getRelativeWidth(49.0),
//                                   alignment: .center)
//                            .background(RoundedCorners(topLeft: 24.5, topRight: 24.5,
//                                                       bottomLeft: 24.5, bottomRight: 24.5)
//                                    .fill(ColorConstants.WhiteA700))
//                            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
//                            Spacer()
//                            Button(action: {}, label: {
//                                Image("img_gps")
//                            })
//                            .frame(width: getRelativeWidth(49.0), height: getRelativeWidth(49.0),
//                                   alignment: .center)
//                            .background(RoundedCorners(topLeft: 24.5, topRight: 24.5,
//                                                       bottomLeft: 24.5, bottomRight: 24.5)
//                                    .fill(ColorConstants.WhiteA700))
//                            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
//                        }
//                        .frame(width: getRelativeWidth(361.0), height: getRelativeHeight(49.0),
//                               alignment: .leading)
//                    }
//                    .frame(width: getRelativeWidth(361.0), height: getRelativeHeight(49.0),
//                           alignment: .leading)
//                    .padding(.horizontal, getRelativeWidth(14.0))
                    ZStack(alignment: .bottomLeading) {
                        VStack {
                            ZStack(alignment: .center) {
                                Image("img_vector_cyan_800")
                                    .resizable()
                                    .frame(width: getRelativeWidth(42.0),
                                           height: getRelativeHeight(54.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                Button(action: {}, label: {
                                    Image("img_group29")
                                })
                                .frame(width: getRelativeWidth(22.0),
                                       height: getRelativeWidth(22.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 11.44, topRight: 11.44,
                                                           bottomLeft: 11.44, bottomRight: 11.44)
                                        .fill(ColorConstants.Cyan800))
                                .padding(.bottom, getRelativeHeight(21.11))
                                .padding(.horizontal, getRelativeWidth(10.24))
                            }
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(42.0), height: getRelativeHeight(54.0),
                                   alignment: .trailing)
                            .padding(.leading, getRelativeWidth(203.0))
                            .padding(.trailing, getRelativeWidth(8.0))
                            ZStack(alignment: .center) {
                                Image("img_vector_cyan_800")
                                    .resizable()
                                    .frame(width: getRelativeWidth(42.0),
                                           height: getRelativeHeight(54.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                Button(action: {}, label: {
                                    Image("img_group29")
                                })
                                .frame(width: getRelativeWidth(22.0),
                                       height: getRelativeWidth(22.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 11.44, topRight: 11.44,
                                                           bottomLeft: 11.44, bottomRight: 11.44)
                                        .fill(ColorConstants.Cyan800))
                                .padding(.bottom, getRelativeHeight(21.11))
                                .padding(.horizontal, getRelativeWidth(10.24))
                            }
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(42.0), height: getRelativeHeight(54.0),
                                   alignment: .trailing)
                            .padding(.top, getRelativeHeight(30.0))
                            .padding(.horizontal, getRelativeWidth(100.0))
                            ScrollView(.vertical, showsIndicators: false) {
                                VStack {
                                    LazyVGrid(columns: [SwiftUI.GridItem(), SwiftUI.GridItem()],
                                              spacing: 168.38) {
                                        ForEach(0 ... 2, id: \.self) { index in
                                            MapPin1Cell()
                                        }
                                    }
                                }
                            }
                            .padding(.top, getRelativeHeight(5.0))
                            .fixedSize(horizontal: false, vertical: false)
                        }
                        .frame(width: getRelativeWidth(253.0), height: getRelativeHeight(329.0),
                               alignment: .topLeading)
                        .padding(.bottom, getRelativeHeight(390.0))
                        .padding(.trailing, getRelativeWidth(104.31))
                       
                        
                        ZStack(alignment: .center) {
                            Image("img_vector_cyan_800")
                                .resizable()
                                .frame(width: getRelativeWidth(42.0),
                                       height: getRelativeHeight(54.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                            Button(action: {}, label: {
                                Image("img_group29")
                            })
                            .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(22.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 11.44, topRight: 11.44,
                                                       bottomLeft: 11.44, bottomRight: 11.44)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.bottom, getRelativeHeight(21.11))
                            .padding(.horizontal, getRelativeWidth(10.24))
                        }
                        .hideNavigationBar()
                        .frame(width: getRelativeWidth(42.0), height: getRelativeHeight(54.0),
                               alignment: .bottomLeading)
                        .padding(.top, getRelativeHeight(373.0))
                        .padding(.trailing, getRelativeWidth(307.31))
                       
                        
                        ZStack(alignment: .center) {
                            Image("img_vector_cyan_800")
                                .resizable()
                                .frame(width: getRelativeWidth(42.0),
                                       height: getRelativeHeight(54.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                            Button(action: {}, label: {
                                Image("img_group29")
                            })
                            .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(22.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 11.44, topRight: 11.44,
                                                       bottomLeft: 11.44, bottomRight: 11.44)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.bottom, getRelativeHeight(21.11))
                            .padding(.horizontal, getRelativeWidth(10.24))
                        }
                        .hideNavigationBar()
                        .frame(width: getRelativeWidth(42.0), height: getRelativeHeight(54.0),
                               alignment: .bottomLeading)
                        .padding(.top, getRelativeHeight(420.0))
                        .padding(.trailing, getRelativeWidth(224.31))
                        ZStack(alignment: .center) {
                            Image("img_vector_cyan_800")
                                .resizable()
                                .frame(width: getRelativeWidth(42.0),
                                       height: getRelativeHeight(54.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                            Button(action: {}, label: {
                                Image("img_group29")
                            })
                            .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(22.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 11.44, topRight: 11.44,
                                                       bottomLeft: 11.44, bottomRight: 11.44)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.bottom, getRelativeHeight(21.11))
                            .padding(.horizontal, getRelativeWidth(10.24))
                        }
                        .hideNavigationBar()
                        .frame(width: getRelativeWidth(42.0), height: getRelativeHeight(54.0),
                               alignment: .bottomTrailing)
                        .padding(.top, getRelativeHeight(521.0))
                        .padding(.leading, getRelativeWidth(340.69))
                        HStack {
                            HStack {
                                Image("img_map")
                                    .resizable()
                                    .frame(width: getRelativeWidth(9.0),
                                           height: getRelativeHeight(11.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                Text(StringConstants.kLbl6Km)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.Black900B2)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(28.0),
                                           height: getRelativeHeight(18.0), alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(7.0))
                            }
                            .frame(width: getRelativeWidth(45.0), height: getRelativeHeight(18.0),
                                   alignment: .bottom)
                            HStack {
                                ScrollView(.horizontal, showsIndicators: false) {
                                    ZStack(alignment: .topLeading) {
                                        Image("img_rectangle4217")
                                            .resizable()
                                            .frame(width: getRelativeWidth(212.0),
                                                   height: getRelativeHeight(220.0),
                                                   alignment: .center)
                                            .scaledToFit()
                                            .clipped()
                                        VStack(alignment: .leading, spacing: 0) {
                                            ZStack(alignment: .topTrailing) {
                                                Image("img_rectangle4218")
                                                    .resizable()
                                                    .frame(width: getRelativeWidth(188.0),
                                                           height: getRelativeHeight(103.0),
                                                           alignment: .center)
                                                    .scaledToFit()
                                                    .clipped()
                                                    .background(RoundedCorners(topLeft: 3.0,
                                                                               topRight: 3.0,
                                                                               bottomLeft: 3.0,
                                                                               bottomRight: 3.0))
                                                VStack {
                                                    Image("img_vector_yellow_a700")
                                                        .resizable()
                                                        .frame(width: getRelativeWidth(6.0),
                                                               height: getRelativeWidth(6.0),
                                                               alignment: .center)
                                                        .scaledToFit()
                                                        .clipped()
                                                        .padding(.horizontal, getRelativeWidth(9.0))
                                                    Text(StringConstants.kLbl43)
                                                        .font(FontScheme
                                                            .kNunitoBold(size: getRelativeHeight(9.0)))
                                                        .fontWeight(.bold)
                                                        .foregroundColor(ColorConstants.WhiteA700)
                                                        .minimumScaleFactor(0.5)
                                                        .multilineTextAlignment(.leading)
                                                        .frame(width: getRelativeWidth(13.0),
                                                               height: getRelativeWidth(13.0),
                                                               alignment: .topLeading)
                                                        .padding(.horizontal, getRelativeWidth(5.0))
                                                }
                                                .frame(width: getRelativeWidth(24.0),
                                                       height: getRelativeHeight(25.0),
                                                       alignment: .topTrailing)
                                                .background(RoundedCorners(topLeft: 12.135,
                                                                           topRight: 12.135,
                                                                           bottomLeft: 12.135,
                                                                           bottomRight: 12.135)
                                                        .fill(ColorConstants
                                                            .Cyan800))
                                                .padding(.bottom, getRelativeHeight(71.0))
                                                .padding(.leading, getRelativeWidth(154.57))
                                            }
                                            .hideNavigationBar()
                                            .frame(width: getRelativeWidth(188.0),
                                                   height: getRelativeHeight(103.0),
                                                   alignment: .leading)
                                            Text(StringConstants.kMsgBroadwayBarber)
                                                .font(FontScheme
                                                    .kNunitoSemiBold(size: getRelativeHeight(14.0)))
                                                .fontWeight(.semibold)
                                                .foregroundColor(ColorConstants.Cyan800)
                                                .minimumScaleFactor(0.5)
                                                .multilineTextAlignment(.leading)
                                                .frame(width: getRelativeWidth(177.0),
                                                       height: getRelativeHeight(18.0),
                                                       alignment: .topLeading)
                                                .padding(.top, getRelativeHeight(10.0))
                                                .padding(.trailing, getRelativeWidth(10.0))
                                            Text(StringConstants.kMsg4thFloorAlZ)
                                                .font(FontScheme
                                                    .kNunitoRegular(size: getRelativeHeight(12.0)))
                                                .fontWeight(.regular)
                                                .foregroundColor(ColorConstants.Gray800)
                                                .minimumScaleFactor(0.5)
                                                .multilineTextAlignment(.leading)
                                                .frame(width: getRelativeWidth(185.0),
                                                       height: getRelativeHeight(34.0),
                                                       alignment: .topLeading)
                                                .padding(.top, getRelativeHeight(4.0))
                                                .padding(.horizontal, getRelativeWidth(1.0))
                                        }
                                        .frame(width: getRelativeWidth(188.0),
                                               height: getRelativeHeight(170.0),
                                               alignment: .topLeading)
                                        .padding(.bottom, getRelativeHeight(40.0))
                                        .padding(.trailing, getRelativeWidth(12.0))
                                    }
                                    .hideNavigationBar()
                                    .frame(width: getRelativeWidth(81.0),
                                           height: getRelativeHeight(220.0), alignment: .leading)
                                }
                                HStack(spacing: 0) {
                                    ScrollView(.horizontal, showsIndicators: false) {
                                        LazyHStack {
                                            ForEach(0 ... 1, id: \.self) { index in
//                                                MapSaloonCardCell()
                                            }
                                        }
                                    }
                                }
                                .frame(width: getRelativeWidth(299.0), alignment: .center)
                                .padding(.leading, getRelativeWidth(10.0))
                            }
                            .frame(width: UIScreen.main.bounds.width,
                                   height: getRelativeHeight(232.0), alignment: .center)
                        }
                        .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(254.0),
                               alignment: .bottomLeading)
                        .background(ColorConstants.WhiteA700)
                        .padding(.top, getRelativeHeight(465.13))
                    }
                    .hideNavigationBar()
                    .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(719.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(39.0))
                }
                .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(808.0),
                       alignment: .leading)
            }
            .hideNavigationBar()
            .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
            .background(ColorConstants.WhiteA700)
            .padding(.top, getRelativeHeight(30.0))
            .padding(.bottom, getRelativeHeight(10.0))
        }
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(ColorConstants.WhiteA700)
        .ignoresSafeArea()
        .hideNavigationBar()
    }
}

struct MapviewpopupView_Previews: PreviewProvider {
    static var previews: some View {
        MapviewpopupView()
    }
}
