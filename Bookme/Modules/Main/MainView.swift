//
//  MainView.swift
//  Bookme
//
//  Created by Apple on 30/01/2024.
//

import SwiftUI

struct MainView: View {
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var locationManager: LocationManager
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @Environment(\.scenePhase) var scenePhase
    @State private var showAlert = false
    var body: some View {
        Group {
            if appState.isConnected {
                switch appState.initialScreen {
                case .splash:
                    SplashView(isHomeSplash: false)
                        .transition(.backslide)
                case .onboarding:

                    //                Group {
                    //                    if mapLocationManager.isAuthorized {
                    //                        OnboardingView()
                    //                    } else {
                    //                        LocationDeniedView()
                    //                    }
                    //                }

                    OnboardingView()

                        .transition(.backslide)
                    
                case .dashboard:
                    DashboardView()
                        .id(appState.rootViewId)
                        .transition(.backslide)
                case .authentication:
                    Text("Authentication Section")
                        .transition(.backslide)
                }
            } else {
                OfflineView {
                    appState.networkMonitor.checkConnectivity()
                }
                

//                CustomPlaceholder(placeholderType: .networkError, title: "Network error", subTitle: "Please check your internet connection and try again")
//                    .padding()
            }
        }
        .onLoad {
            locationManager.requestUserCurrentLocation { _, _ in }
        }
        .animation(.easeInOut, value: appState.initialScreen)
        .alert("Location Permission Denied",
               isPresented: $showAlert,
               actions: {
                   Button("Settings") {
                       if let appSettings = URL(string: UIApplication.openSettingsURLString) {
                           UIApplication.shared.open(appSettings)
                       }
                   }

               },
               message: {
                   Text("Access to Location Services was denied. Please enable it in Settings > Privacy > Location Services.")
               })

        .onChange(of: locationManager.isPermissionDenied) { _, newValue in
            showAlert = newValue
        }
        .onChange(of: scenePhase) { _, newPhase in
            print("scenePhase", scenePhase)
            Utilities.enQueue(after: .now() + 0.1) {
                if newPhase == .active {
                    // Ensure the location permission status is checked when app becomes active
                    locationManager.requestUserCurrentLocation { _, _ in
                        showAlert = locationManager.isPermissionDenied
                    }
                }
            }
        }
    }
}

#Preview {
    NavigationStack {
        MainView().attachAllEnvironmentObjects()
    }
}

extension AnyTransition {
    static var backslide: AnyTransition {
        AnyTransition.asymmetric(
            insertion: .move(edge: .trailing),
            removal: .move(edge: .leading))
    }
}

struct OfflineView: View {
    let onRetryAction: () -> Void
    var body: some View {
        VStack(spacing: 20) {
            Image(.noNetwork)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .foregroundColor(.primary)
                .frame(width: 180, height: 180)

            VStack(spacing: 16){
                Text("Connection Error")
                    .font(FontScheme
                        .kNunitoExtraBold(size: 20.relativeFontSize))
                    .fontWeight(.black)
                    .foregroundStyle(Color("Cyan800"))

                Text("Please check your internet connection and try again.")
                    .font(FontScheme
                        .kNunitoRegular(size: 14.relativeFontSize))
                    .fontWeight(.regular)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 36)
                    .foregroundColor(.black)
            }
            .padding(.bottom)

            Button(action: onRetryAction, label: {
                HStack(spacing: 0) {
                    Text("Try Again")
                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.heavy)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(15.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.center)
                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                .fill(ColorConstants.Cyan800))
                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                }
            })
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(ColorConstants.WhiteA700)
        
    }
}
