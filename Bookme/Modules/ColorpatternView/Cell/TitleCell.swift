import SwiftUI

struct TitleCell: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(StringConstants.kMsgWhiteAreaTex)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Black900)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(119.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
            ZStack {}
                .hideNavigationBar()
                .frame(width: getRelativeWidth(110.0), height: getRelativeHeight(81.0),
                       alignment: .leading)
                .background(ColorConstants.Black900B2)
                .padding(.trailing, getRelativeWidth(9.0))
            Text(StringConstants.kLbl000000)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Black900)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(60.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(5.0))
                .padding(.horizontal, getRelativeWidth(22.0))
            Text(StringConstants.kLblOpacity70)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Black900)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(88.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.horizontal, getRelativeWidth(14.0))
        }
        .frame(width: getRelativeWidth(119.0), alignment: .leading)
        .hideNavigationBar()
    }
}

/* struct TitleCell_Previews: PreviewProvider {

 static var previews: some View {
 			TitleCell()
 }
 } */
