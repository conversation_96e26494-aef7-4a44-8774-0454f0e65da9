import SwiftUI

struct ColorpatternView: View {
    @StateObject var colorpatternViewModel = ColorpatternViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            VStack {
                ScrollView(.vertical, showsIndicators: false) {
                    VStack {
                        Text(StringConstants.kLblMainColor)
                            .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.heavy)
                            .foregroundColor(ColorConstants.Black900)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(66.0), height: getRelativeHeight(17.0),
                                   alignment: .topLeading)
                            .padding(.horizontal, getRelativeWidth(9.0))
                        HStack {
                            VStack {
                                ZStack {}
                                    .hideNavigationBar()
                                    .frame(width: getRelativeWidth(112.0),
                                           height: getRelativeHeight(81.0), alignment: .leading)
                                    .background(ColorConstants.Cyan800)
                                Text(StringConstants.kLbl008f96)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(62.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(5.0))
                                    .padding(.horizontal, getRelativeWidth(22.0))
                            }
                            .frame(width: getRelativeWidth(112.0), height: getRelativeHeight(106.0),
                                   alignment: .center)
                            Spacer()
                            VStack {
                                ZStack {}
                                    .hideNavigationBar()
                                    .frame(width: getRelativeWidth(113.0),
                                           height: getRelativeHeight(81.0), alignment: .leading)
                                    .background(ColorConstants.Black900)
                                Text(StringConstants.kLbl000000)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(62.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(5.0))
                                    .padding(.horizontal, getRelativeWidth(22.0))
                            }
                            .frame(width: getRelativeWidth(113.0), height: getRelativeHeight(106.0),
                                   alignment: .center)
                            Spacer()
                            VStack {
                                ZStack {}
                                    .hideNavigationBar()
                                    .frame(width: getRelativeWidth(112.0),
                                           height: getRelativeHeight(81.0), alignment: .leading)
                                    .overlay(RoundedCorners()
                                        .stroke(ColorConstants.Gray200, lineWidth: 1))
                                    .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                                Text(StringConstants.kLblFfffff)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(60.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(5.0))
                                    .padding(.horizontal, getRelativeWidth(25.0))
                            }
                            .frame(width: getRelativeWidth(112.0), height: getRelativeHeight(106.0),
                                   alignment: .center)
                        }
                        .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(106.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(15.0))
                        .padding(.trailing, getRelativeWidth(12.0))
                        Text(StringConstants.kLblGradient)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black900)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(60.0), height: getRelativeHeight(20.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(32.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                        ZStack {}
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(133.0), height: getRelativeHeight(81.0),
                                   alignment: .leading)
                            .background(LinearGradient(gradient: Gradient(colors: [ColorConstants
                                        .Cyan900,
                                    ColorConstants
                                        .Black901]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing))
                            .padding(.top, getRelativeHeight(8.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                        Text(StringConstants.kMsg000000005d)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black900)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(123.0), height: getRelativeHeight(17.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(5.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                        HStack {
                            VStack(alignment: .trailing, spacing: 0) {
                                Text(StringConstants.kLblTitileColor)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(77.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.leading)
                                    .padding(.leading)
                                    .padding(.trailing, getRelativeWidth(23.0))
                                ZStack {}
                                    .hideNavigationBar()
                                    .frame(width: getRelativeWidth(112.0),
                                           height: getRelativeHeight(81.0), alignment: .leading)
                                    .background(ColorConstants.Cyan800)
                                Text(StringConstants.kLbl008f96)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(62.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(5.0))
                                    .padding(.leading, getRelativeWidth(22.0))
                                    .padding(.trailing, getRelativeWidth(28.0))
                            }
                            .frame(width: getRelativeWidth(112.0), height: getRelativeHeight(127.0),
                                   alignment: .top)
                            .padding(.bottom, getRelativeHeight(19.0))
                            Spacer()
                            HStack(spacing: 0) {
                                ScrollView(.horizontal, showsIndicators: false) {
                                    LazyHStack {
                                        ForEach(0 ... 1, id: \.self) { index in
                                            TitleCell()
                                        }
                                    }
                                }
                            }
                            .frame(width: getRelativeWidth(242.0), alignment: .center)
                        }
                        .frame(width: getRelativeWidth(362.0), height: getRelativeHeight(146.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(17.0))
                        .padding(.trailing, getRelativeWidth(7.0))
                        HStack {
                            VStack(alignment: .leading, spacing: 0) {
                                Text(StringConstants.kMsgButtonColorI)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(77.0),
                                           height: getRelativeHeight(35.0), alignment: .center)
                                    .padding(.horizontal, getRelativeWidth(14.0))
                                VStack {
                                    ZStack {}
                                        .hideNavigationBar()
                                        .frame(width: getRelativeWidth(82.0),
                                               height: getRelativeHeight(34.0), alignment: .center)
                                        .background(RoundedCorners(topLeft: 17.0, topRight: 17.0,
                                                                   bottomLeft: 17.0,
                                                                   bottomRight: 17.0)
                                                .fill(ColorConstants.Black9003f))
                                        .padding(.top, getRelativeHeight(22.0))
                                        .padding(.bottom, getRelativeHeight(25.0))
                                        .padding(.horizontal, getRelativeWidth(14.0))
                                }
                                .frame(width: getRelativeWidth(112.0),
                                       height: getRelativeHeight(81.0), alignment: .leading)
                                .background(ColorConstants.Cyan800)
                                Text(StringConstants.kLbl000000)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(62.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(14.0))
                                Text(StringConstants.kLblOpacity25)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(90.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(14.0))
                                    .padding(.trailing, getRelativeWidth(8.0))
                            }
                            .frame(width: getRelativeWidth(112.0), height: getRelativeHeight(162.0),
                                   alignment: .top)
                            .padding(.vertical, getRelativeHeight(1.0))
                            Spacer()
                            ZStack(alignment: .topTrailing) {
                                VStack(alignment: .leading, spacing: 0) {
                                    Text(StringConstants.kMsgBoxRoundShp)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(115.0),
                                               height: getRelativeHeight(35.0), alignment: .center)
                                        .padding(.trailing)
                                    VStack {
                                        ZStack {}
                                            .hideNavigationBar()
                                            .frame(width: getRelativeWidth(76.0),
                                                   height: getRelativeHeight(35.0),
                                                   alignment: .center)
                                            .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0,
                                                                    bottomLeft: 6.0,
                                                                    bottomRight: 6.0)
                                                    .stroke(ColorConstants.Cyan800,
                                                            lineWidth: 1))
                                            .background(RoundedCorners(topLeft: 6.0, topRight: 6.0,
                                                                       bottomLeft: 6.0,
                                                                       bottomRight: 6.0)
                                                    .fill(ColorConstants.Cyan8003f))
                                            .padding(.vertical, getRelativeHeight(21.0))
                                            .padding(.horizontal, getRelativeWidth(18.0))
                                    }
                                    .frame(width: getRelativeWidth(112.0),
                                           height: getRelativeHeight(81.0), alignment: .leading)
                                    .overlay(RoundedCorners()
                                        .stroke(ColorConstants.Gray300, lineWidth: 1))
                                    .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                                    .padding(.trailing, getRelativeWidth(10.0))
                                    Text(StringConstants.kLbl008f96)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(62.0),
                                               height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.top, getRelativeHeight(16.0))
                                        .padding(.horizontal, getRelativeWidth(24.0))
                                    Text(StringConstants.kMsgOpacity25Bor)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(122.0),
                                               height: getRelativeHeight(36.0), alignment: .center)
                                }
                                .frame(width: getRelativeWidth(122.0),
                                       height: getRelativeHeight(188.0), alignment: .leading)
                                .padding(.trailing, getRelativeWidth(110.0))
                                VStack {
                                    Text(StringConstants.kLblTextInBox)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(77.0),
                                               height: getRelativeHeight(17.0),
                                               alignment: .topLeading)
                                        .padding(.horizontal, getRelativeWidth(16.0))
                                    VStack {
                                        ZStack {}
                                            .hideNavigationBar()
                                            .frame(width: getRelativeWidth(76.0),
                                                   height: getRelativeHeight(35.0),
                                                   alignment: .center)
                                            .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0,
                                                                    bottomLeft: 6.0,
                                                                    bottomRight: 6.0)
                                                    .stroke(ColorConstants.Cyan800,
                                                            lineWidth: 1))
                                            .background(RoundedCorners(topLeft: 6.0, topRight: 6.0,
                                                                       bottomLeft: 6.0,
                                                                       bottomRight: 6.0)
                                                    .fill(ColorConstants.Cyan8003f))
                                            .padding(.vertical, getRelativeHeight(21.0))
                                            .padding(.horizontal, getRelativeWidth(18.0))
                                    }
                                    .frame(width: getRelativeWidth(112.0),
                                           height: getRelativeHeight(81.0), alignment: .leading)
                                    .overlay(RoundedCorners()
                                        .stroke(ColorConstants.Gray300, lineWidth: 1))
                                    .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                                    .padding(.top, getRelativeHeight(18.0))
                                    Text(StringConstants.kLbl008f96)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(62.0),
                                               height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.top, getRelativeHeight(16.0))
                                        .padding(.horizontal, getRelativeWidth(16.0))
                                    Text(StringConstants.kLblOpacity50)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(90.0),
                                               height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.leading, getRelativeWidth(16.0))
                                        .padding(.trailing, getRelativeWidth(6.0))
                                }
                                .frame(width: getRelativeWidth(112.0),
                                       height: getRelativeHeight(172.0), alignment: .topTrailing)
                                .padding(.bottom, getRelativeHeight(16.0))
                                .padding(.leading, getRelativeWidth(120.0))
                            }
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(232.0), height: getRelativeHeight(188.0),
                                   alignment: .center)
                        }
                        .frame(width: getRelativeWidth(362.0), height: getRelativeHeight(188.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(36.0))
                        .padding(.trailing, getRelativeWidth(7.0))
                        HStack {
                            VStack(alignment: .leading, spacing: 0) {
                                Text(StringConstants.kLblHoverInBox)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(87.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                                    .padding(.horizontal, getRelativeWidth(12.0))
                                VStack {
                                    ZStack {}
                                        .hideNavigationBar()
                                        .frame(width: getRelativeWidth(76.0),
                                               height: getRelativeHeight(35.0), alignment: .center)
                                        .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0,
                                                                bottomLeft: 6.0, bottomRight: 6.0)
                                                .stroke(ColorConstants.Cyan800,
                                                        lineWidth: 1))
                                        .background(RoundedCorners(topLeft: 6.0, topRight: 6.0,
                                                                   bottomLeft: 6.0,
                                                                   bottomRight: 6.0)
                                                .fill(ColorConstants.Cyan800))
                                        .padding(.vertical, getRelativeHeight(21.0))
                                        .padding(.horizontal, getRelativeWidth(18.0))
                                }
                                .frame(width: getRelativeWidth(112.0),
                                       height: getRelativeHeight(81.0), alignment: .leading)
                                .overlay(RoundedCorners()
                                    .stroke(ColorConstants.Gray300, lineWidth: 1))
                                .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                                .padding(.top, getRelativeHeight(18.0))
                                Text(StringConstants.kMsg008f96Text)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(102.0),
                                           height: getRelativeHeight(36.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(10.0))
                                    .padding(.leading, getRelativeWidth(7.0))
                            }
                            .frame(width: getRelativeWidth(112.0), height: getRelativeHeight(162.0),
                                   alignment: .center)
                            Spacer()
                            VStack(alignment: .leading, spacing: 0) {
                                Text(StringConstants.kMsgWhiteAreaFad)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(75.0),
                                           height: getRelativeHeight(35.0), alignment: .center)
                                    .padding(.horizontal, getRelativeWidth(13.0))
                                Button(action: {}, label: {
                                    HStack(spacing: 0) {
                                        Text(StringConstants.kLblFade)
                                            .font(FontScheme
                                                .kNunitoBold(size: getRelativeHeight(14.0)))
                                            .fontWeight(.bold)
                                            .padding(.horizontal, getRelativeWidth(30.0))
                                            .padding(.vertical, getRelativeHeight(28.0))
                                            .foregroundColor(ColorConstants.Cyan8007f)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.center)
                                            .frame(width: getRelativeWidth(112.0),
                                                   height: getRelativeHeight(81.0),
                                                   alignment: .center)
                                            .overlay(RoundedCorners()
                                                .stroke(ColorConstants.Gray300, lineWidth: 1))
                                            .background(RoundedCorners()
                                                .fill(ColorConstants.WhiteA700))
                                    }
                                })
                                .frame(width: getRelativeWidth(112.0),
                                       height: getRelativeHeight(81.0), alignment: .center)
                                .overlay(RoundedCorners()
                                    .stroke(ColorConstants.Gray300, lineWidth: 1))
                                .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                                Text(StringConstants.kMsg008f96Opacity)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(90.0),
                                           height: getRelativeHeight(38.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(10.0))
                                    .padding(.leading, getRelativeWidth(13.0))
                                    .padding(.trailing, getRelativeWidth(9.0))
                            }
                            .frame(width: getRelativeWidth(112.0), height: getRelativeHeight(164.0),
                                   alignment: .center)
                            Spacer()
                            VStack {
                                Text(StringConstants.kLblSubText)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(53.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                                    .padding(.horizontal, getRelativeWidth(13.0))
                                Button(action: {}, label: {
                                    HStack(spacing: 0) {
                                        Text(StringConstants.kLblFade)
                                            .font(FontScheme
                                                .kNunitoBold(size: getRelativeHeight(14.0)))
                                            .fontWeight(.bold)
                                            .padding(.horizontal, getRelativeWidth(30.0))
                                            .padding(.vertical, getRelativeHeight(28.0))
                                            .foregroundColor(ColorConstants.Cyan800B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.center)
                                            .frame(width: getRelativeWidth(112.0),
                                                   height: getRelativeHeight(81.0),
                                                   alignment: .center)
                                            .overlay(RoundedCorners()
                                                .stroke(ColorConstants.Gray300, lineWidth: 1))
                                            .background(RoundedCorners()
                                                .fill(ColorConstants.WhiteA700))
                                            .padding(.top, getRelativeHeight(9.0))
                                    }
                                })
                                .frame(width: getRelativeWidth(112.0),
                                       height: getRelativeHeight(81.0), alignment: .center)
                                .overlay(RoundedCorners()
                                    .stroke(ColorConstants.Gray300, lineWidth: 1))
                                .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                                .padding(.top, getRelativeHeight(9.0))
                                Text(StringConstants.kMsg008f96Opacity2)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(90.0),
                                           height: getRelativeHeight(38.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(10.0))
                                    .padding(.leading, getRelativeWidth(13.0))
                                    .padding(.trailing, getRelativeWidth(9.0))
                            }
                            .frame(width: getRelativeWidth(112.0), height: getRelativeHeight(156.0),
                                   alignment: .bottom)
                            .padding(.top, getRelativeHeight(8.0))
                        }
                        .frame(width: getRelativeWidth(372.0), height: getRelativeHeight(165.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(30.0))
                    }
                    .frame(width: getRelativeWidth(372.0), alignment: .topLeading)
                }
            }
            .frame(width: getRelativeWidth(372.0), alignment: .topLeading)
            .background(ColorConstants.WhiteA700)
            .padding(.top, getRelativeHeight(30.0))
            .padding(.bottom, getRelativeHeight(10.0))
        }
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(ColorConstants.WhiteA700)
        .ignoresSafeArea()
        .hideNavigationBar()
    }
}

struct ColorpatternView_Previews: PreviewProvider {
    static var previews: some View {
        ColorpatternView()
    }
}
