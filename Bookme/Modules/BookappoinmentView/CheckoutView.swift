//
//  CheckoutView.swift
//  Bookme
//
//  Created by Apple on 12/11/2024.
//

import SwiftUI

struct CheckoutView: View {
    @StateObject var viewModel: BookAppointmentViewModel
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @StateObject private var keyboardResponder = KeyboardResponder()

    var body: some View {
        SuperView(pageState: $viewModel.pageState) {} content: {
            MainScrollBody(backButtonWithTitle: "Checkout") {
                VStack(alignment: .leading) {
                    Text("Selected Services")
                        .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Black900)
                        .multilineTextAlignment(.leading)
                        .padding(.horizontal, getRelativeWidth(10.0))

                    VStack(alignment: .leading, spacing: 8) {
                        if viewModel.isCartLoading || viewModel.isTimeslotLoading || viewModel.isCalendarLoading {
                            VStack(alignment: .leading, spacing: 16) {
                                ForEach(0 ... 1, id: \.self) { index in

                                    CartItemCellShimmerView()

                                    Divider()
                                        .frame(height: getRelativeHeight(1.0), alignment: .center)
                                        .background(.black.opacity(0.21))
                                        .padding(.horizontal, getRelativeWidth(4.0))
                                        .padding(.vertical)
                                        .overlay(content: {
                                            HStack(spacing: 6.0.relativeWidth) {
                                                Image(.timeFill)
                                                    .resizable()
                                                    .frame(width: 15, height: 15)
                                                Text("30 minutes")
                                                    .font(Font.custom("Nunito", size: 9.relativeFontSize))
                                                    .foregroundColor(.black.opacity(0.5))
                                            }
                                            .padding(.horizontal)
                                            .background(ColorConstants.WhiteA700)
                                        })
                                        .visibility(index < 1 ? .visible : .gone)

                                }.shimmerize()
                            }

                        } else {
                            VStack(alignment: .leading, spacing: 16) {
                                ForEach(Array(viewModel.cartModelList.enumerated()), id: \.element) { index, model in

                                    if
                                        let delay = model.delay1,
                                        let delayInMinutes = Int(delay),
                                        delayInMinutes > 0,
                                        (index + 1).isMultiple(of: 2)
                                    {
                                        Divider()
                                            .frame(height: getRelativeHeight(1.0), alignment: .center)
                                            .background(.black.opacity(0.21))
                                            .padding(.horizontal, getRelativeWidth(4.0))
                                            .overlay(content: {
                                                HStack(spacing: 6.0.relativeWidth) {
                                                    Image(.timeFill)
                                                        .resizable()
                                                        .frame(width: 15, height: 15)
                                                    Text("\(delayInMinutes) minutes")
                                                        .font(Font.custom("Nunito", size: 9.relativeFontSize))
                                                        .foregroundColor(.black.opacity(0.5))
                                                }
                                                .padding(.horizontal)
                                                .background(ColorConstants.WhiteA700)
                                            })
                                    }

                                    CartItemCellView(model: model, onDelete: viewModel.cartModelList.count > 1 ? viewModel.deleteCartItem : nil)
                                }
                            }
                        }
                    }
                    .background(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                               bottomRight: 25.0)
                            .fill(ColorConstants.WhiteA700))
                    .padding(.top, getRelativeHeight(8.0))

                    Rectangle()
                        .foregroundColor(.clear)
                        .frame(width: 370, height: 1)
                        .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.21))
                        .padding(.vertical, 32)

                    VStack(alignment: .leading) {
                        Text("Apply Promo Code")
                            .font(
                                Font.custom("Nunito", size: 16)
                                    .weight(.bold)
                            )
                            .multilineTextAlignment(.center)
                            .foregroundColor(.black)

                        if viewModel.isCouponLoading {
                            HStack {
                                TextField("Enter Promo Code", text: $viewModel.couponCode)
                                    .font(
                                        Font.custom("Nunito", size: 14)
                                            .weight(.medium)
                                    )
                                    .kerning(0.07)
                                    .foregroundColor(Color(red: 0.67, green: 0.67, blue: 0.67))
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .disableWithOpacity(viewModel.couponCodeModel != nil)

                                Button(action: viewModel.onCouponCodeApply) {
                                    Text(viewModel.couponCodeModel != nil ? "Remove" : "Apply")
                                        .font(
                                            Font.custom("Nunito", size: 14)
                                                .weight(.bold)
                                        )
                                        .kerning(0.07)
                                        .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                                }
                                .disableWithOpacity(viewModel.couponCode.isEmpty)
                               
                            }
                            .padding(.horizontal)
                            .frame(height: 50.relativeHeight)
                            .background(.white.opacity(0.25))
                            .cornerRadius(13)
                            .overlay(
                                RoundedRectangle(cornerRadius: 13)
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0, green: 0.56, blue: 0.59), lineWidth: 1)
                            )
                            .shimmerize()
                        } else {
                            HStack {
                                TextField("Enter Promo Code", text: $viewModel.couponCode)
                                    .font(
                                        Font.custom("Nunito", size: 14)
                                            .weight(.medium)
                                    )
                                    .kerning(0.07)
                                    .foregroundColor(Color(red: 0.67, green: 0.67, blue: 0.67))
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .disableWithOpacity(viewModel.couponCodeModel != nil)

                                Button(action: viewModel.onCouponCodeApply) {
                                    Text(viewModel.couponCodeModel != nil ? "Remove" : "Apply")
                                        .font(
                                            Font.custom("Nunito", size: 14)
                                                .weight(.bold)
                                        )
                                        .kerning(0.07)
                                        .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                                }
                                .disableWithOpacity(viewModel.couponCode.isEmpty)
                            }
                            .padding(.horizontal)
                            .frame(height: 50.relativeHeight)
                            .background(.white.opacity(0.25))
                            .cornerRadius(13)
                            .overlay(
                                RoundedRectangle(cornerRadius: 13)
                                    .inset(by: 0.5)
                                    .stroke(Color(red: 0, green: 0.56, blue: 0.59), lineWidth: 1)
                            )
                        }

                        // Error Text

                        Text("\(viewModel.couponCodeErrorMessage ?? "")")
                            .foregroundColor(Color(hex: "#FF1010"))
                            .font(Font.custom("Nunito", size: 14).weight(.medium))
                            .frame(maxWidth: .infinity)
                            .visibility(viewModel.couponCodeErrorMessage != nil ? .visible: .gone)
                    }
                    .padding(.bottom)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .onChange(of: viewModel.couponCode) { old, new in
                    viewModel.updateCouponCodeErrorMessage(nil)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .leading)
                .onTapGesture {
                    // Dismiss the keyboard when tapping outside
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
            }
            .safeAreaInset(edge: .bottom) {
                VStack(alignment: .leading, spacing: 0) {
                    Text("Booking info")
                        .font(
                            Font.custom("Nunito", size: 16)
                                .weight(.bold)
                        )
                        .kerning(0.5)
                        .foregroundColor(.black)
                        .padding(.bottom, 28.relativeHeight)

                    VStack(spacing: 8.relativeHeight) {
                        HStack {
                            Text("Subtotal")
                                .font(Font.custom("Nunito", size: 14))
                                .kerning(0.5)
                                .foregroundColor(.black)

                            CurrencyText("\(viewModel.subTotal)")
                                .font(Font.custom("Nunito", size: 15))
                                .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                                .frame(maxWidth: .infinity, alignment: .trailing)
                                .contentTransition(.numericText())
                                .animation(.bouncy, value: viewModel.subTotal)
                        }

                        HStack {
                            Text("Discount")
                                .font(Font.custom("Nunito", size: 14))
                                .kerning(0.5)
                                .foregroundColor(.black)

                            CurrencyText("\(viewModel.couponCodeDiscount)")
                                .font(Font.custom("Nunito", size: 15))
                                .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                                .frame(maxWidth: .infinity, alignment: .trailing)
                                .contentTransition(.numericText())
                                .animation(.bouncy, value: viewModel.couponCodeDiscount)
                        }
                    }

                    Rectangle()
                        .foregroundColor(.clear)
                        .frame(height: 1)
                        .background(Color(red: 0.05, green: 0.29, blue: 0.19).opacity(0.15))
                        .padding(.horizontal, -16.relativeWidth)
                        .padding(.vertical, 12.relativeHeight)

                    HStack {
                        Text("Total")
                            .font(
                                Font.custom("Nunito", size: 16)
                                    .weight(.bold)
                            )
                            .kerning(0.5)
                            .foregroundColor(.black)

                        CurrencyText("\(viewModel.grandTotal)")
                            .font(Font.custom("Nunito", size: 16).weight(.bold))
                            .kerning(0.5)
                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                            .contentTransition(.numericText())
                            .animation(.bouncy, value: viewModel.grandTotal)
                            .frame(maxWidth: .infinity, alignment: .trailing)
                    }
                    .padding(.top, 6.relativeHeight)

                    Spacer()

                    Button {
                        let type = routerManager.mapRouterWithTab(appState: appState)
                        self.viewModel.onBook { link in
                            routerManager.push(to: .paymentGateway(link: link, onRedirect: .init(onBack: { model in
                                if let model = model {
                                    if model.paymentStatus == "Paid" {
                                        self.routerManager.goBack(where: type)
                                        routerManager.replace(stack: [.bookingSuccess(type: type, model: model)], where: type)
                                    } else {
                                        routerManager.goBack(where: type)
                                        self.viewModel.updatePageState(.failure(error: "Your Payment was failed. Please try again."))
                                    }
                                }

                            })), where: type)

//
                        }

                    } label: {
                        Text("Proceed to Payment")
                            .font(
                                Font.custom("Nunito", size: 14)
                                    .weight(.heavy)
                            )
                            .kerning(0.5)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.white)
                            .frame(width: 193, height: 32)
                            .frame(width: 240, height: 44)
                            .background(Color(red: 0, green: 0.56, blue: 0.59))
                            .cornerRadius(50)
                            .shadow(color: .black.opacity(0.27), radius: 1.25, x: 0, y: 1)
                    }
                    .frame(maxWidth: .infinity)
                }
                .padding(.horizontal, 16.relativeWidth)
                .padding(.top, 20.relativeHeight)
                .padding(.bottom, 4)
                .frame(height: 275.0.relativeHeight, alignment: .topLeading)
                .frame(maxWidth: .infinity, alignment: .topLeading)
                .background(.white)
                .cornerRadius(23)
                .shadow(color: .black.opacity(0.13), radius: 3.95, x: 3, y: -10)
                .visibility(keyboardResponder.isVisible ? .gone : .visible)
            }
        }
        .overlay {
            if viewModel.isBookingLoading {
                ActivityLoaderView()
            }
        }
    }
}

// #Preview {
//    NavigationStack {
//        CheckoutView().attachAllEnvironmentObjects()
//    }
// }

import Combine
import SwiftUI

class KeyboardResponder: ObservableObject {
    @Published var isVisible: Bool = false

    private var cancellableSet: Set<AnyCancellable> = []

    init() {
        let keyboardWillShow = NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)
            .map { _ in true }

        let keyboardWillHide = NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)
            .map { _ in false }

        Publishers.Merge(keyboardWillShow, keyboardWillHide)
            .assign(to: \.isVisible, on: self)
            .store(in: &cancellableSet)
    }
}
