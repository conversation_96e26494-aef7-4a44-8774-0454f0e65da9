//
//  Untitled.swift
//  Bookme
//
//  Created by Apple on 22/10/2024.
//

import SwiftUI

struct CartItemCellView: View {
    let model: CartModel
    let onDelete: ((CartModel) -> Void)?
    var onStaffSelect: ((CartModel) -> Void)?
    var body: some View {
        VStack(spacing: 0) {
            HStack {
                HStack(alignment: .center) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(model.serviceName)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black900)
                            .multilineTextAlignment(.leading)
                            .frame(
                                alignment: .topLeading)
                    }
                    Spacer(minLength: 8)
                   
                    VStack(alignment: .trailing, spacing: 0) {
                        CurrencyText(model.price)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black900)
                            .multilineTextAlignment(.leading)

                       
                        if let _ = onStaffSelect {
                            let date: String = model.monthFormattedDateCheckoutString ?? ""
                           
                            Text(date)
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(10.0)))
                                .fontWeight(.regular)
                                .animation(.bouncy, value: date)
                                .contentTransition(.numericText())
                                .foregroundColor(ColorConstants.Cyan800B2)
                                .multilineTextAlignment(.leading)
                           
                            let time: String = "\(model.startTime) - \(model.endTime)"
                            Text(time)
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(10.0)))
                                .fontWeight(.regular)
                                .animation(.bouncy, value: time)
                                .contentTransition(.numericText())
                                .foregroundColor(ColorConstants.Cyan800B2)
                                .multilineTextAlignment(.leading)
                        }
                        
                        
                    }
                    .padding(.vertical, onStaffSelect != nil ? 0 : 8)
                }
               
                if let onDelete = onDelete {
                    Button {
                        onDelete(model)
                    } label: {
                        Image(.trashBin)
                            .resizable()
                            .scaledToFit()
                            .frame(width: 11.relativeWidth, height: 14.relativeHeight)
                            .foregroundColor(ColorConstants.Black900)
                            .padding(6)
                            .background(Color(hex: "#FF0000"))
                            .clipShape(Circle())
                    }
                    .padding(.leading, getRelativeWidth(8.0))
                }
            }
            .padding(.vertical, getRelativeHeight(8.0))
            .padding(.horizontal, getRelativeWidth(16.0))
            .transition(.move(edge: .leading))
           
//           Divider().visibility(viewModel.bookingRequestModelList.last != model ? .visible : .gone)
           
            Divider()
                .frame(height: getRelativeHeight(1.0), alignment: .center)
                .background(ColorConstants.Cyan80072)
                .padding(.horizontal, getRelativeWidth(4.0))
           
            HStack(alignment: .center) {
                HStack(spacing: 5.relativeWidth) {
                    Text("Staff: ")
                        .font(
                            Font.custom("Nunito", size: 11)
                                .weight(.semibold)
                        )
                        .foregroundColor(.black.opacity(0.5))
                       
                    NetworkImageView(path: model.staffImageUrl)
                        .frame(width: getRelativeWidth(23.0),
                               height: getRelativeWidth(23.0), alignment: .center)
                       
                        .clipShape(Circle())
                    Text(model.staffName)
                        .font(Font.custom("Nunito", size: 11.relativeFontSize).weight(.semibold))
                        .foregroundColor(.black)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(151.0),
                               height: getRelativeHeight(18.0), alignment: .topLeading)
                        .padding(.leading, getRelativeWidth(2.0))
                }
                   
                .padding(.vertical, getRelativeHeight(8.0))
                   
                Spacer()
                
                if let onStaffSelect = onStaffSelect {
                    Button {
    //                   let routesType: RoutesType = routerManager.mapRouterWithTab(appState: appState)
    //                   let date: String = model.date.toString(outputFormate: dateOutputFormat) ?? ""
    //                   let time: String = model.time ?? ""
    //                   let staffModelRequest: StaffModelRequest = .init(serviceID: model.service.servicesID, vendorID: viewModel.vendor.vendorID, date: date, time: time)
    //                   let route: Route = .shopDetailsStaffModel(selectedService: model.service, staffModelRequest: staffModelRequest, selectedStaff: model.service.selectedStaff, onSelect: .init(onBack: {
    //                       viewModel.onSelect(model: $0, isStaffSelection: true) {
    //                           routerManager.goBack(where: routesType)
    //                       }
    //                   }))
    //
    //                   routerManager.push(to: route, where: routesType)
                       
                        onStaffSelect(model)
                       
                    } label: {
                        Text("Change")
                            .font(Font.custom("Nunito", size: 13.relativeFontSize).weight(.semibold))
                            .multilineTextAlignment(.center)
                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                            .frame(width: 80.relativeWidth, height: 28.relativeHeight, alignment: .center)
                            .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.21).shadow(color: .black.opacity(0.16), radius: 1, x: 0, y: 1))
                            .clipShape(.capsule)
                    }
                }else{
                    VStack(alignment: .trailing) {
                        let date: String = model.monthFormattedDateCheckoutString ?? ""
                       
                        Text(date)
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(10.0)))
                            .fontWeight(.regular)
                            .animation(.bouncy, value: date)
                            .contentTransition(.numericText())
                           
                            .multilineTextAlignment(.leading)
                       
                        let time: String = "\(model.startTime) - \(model.endTime)"
                        Text(time)
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(10.0)))
                            .fontWeight(.regular)
                            .animation(.bouncy, value: time)
                            .contentTransition(.numericText())
                           
                            .multilineTextAlignment(.leading)
                    }
                    .foregroundColor(ColorConstants.Black900)
                }

            }
            .frame(height: 40.0, alignment: .center)
            .padding(.vertical, 4.0)
            .padding(.horizontal)
        }
        .overlay(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                bottomRight: 25.0)
                .stroke(ColorConstants.Cyan800,
                        lineWidth: 1))
    }
}

struct CartItemCellShimmerView: View {
    var body: some View {
        VStack(spacing: 0) {
            HStack {
                HStack(alignment: .center) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("model.serviceName")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black900)
                            .multilineTextAlignment(.leading)
                            .frame(
                                alignment: .topLeading)
                    }
                    Spacer(minLength: 8)
                   
                    VStack(alignment: .trailing, spacing: 0) {
                        CurrencyText("0.000")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black900)
                            .multilineTextAlignment(.leading)

                        Text("MMMM dd,yyyy")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(10.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Cyan800B2)
                            .multilineTextAlignment(.leading)
                       
                        let time: String = "\("startTime") - \("endTime")"
                        Text(time)
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(10.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Cyan800B2)
                            .multilineTextAlignment(.leading)
                    }
                }
               
                Button {} label: {
                    Image(.trashBin)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 11.relativeWidth, height: 14.relativeHeight)
                        .foregroundColor(ColorConstants.Black900)
                        .padding(6)
                        .background(Color(hex: "#FF0000"))
                        .clipShape(Circle())
                }
                .padding(.leading, getRelativeWidth(8.0))
            }
            .padding(.vertical, getRelativeHeight(8.0))
            .padding(.horizontal, getRelativeWidth(16.0))
            .transition(.move(edge: .leading))
           
//           Divider().visibility(viewModel.bookingRequestModelList.last != model ? .visible : .gone)
           
            Divider()
                .frame(height: getRelativeHeight(1.0), alignment: .center)
                .background(ColorConstants.Cyan80072)
                .padding(.horizontal, getRelativeWidth(4.0))
           
            HStack(alignment: .center) {
                HStack(spacing: 5.relativeWidth) {
                    Text("Staff: ")
                        .font(
                            Font.custom("Nunito", size: 11)
                                .weight(.semibold)
                        )
                        .foregroundColor(.black.opacity(0.5))
                       
                    NetworkImageView(path: nil)
                        .frame(width: getRelativeWidth(23.0),
                               height: getRelativeWidth(23.0), alignment: .center)
                       
                        .clipShape(Circle())
                    Text("model.staffName")
                        .font(Font.custom("Nunito", size: 11.relativeFontSize).weight(.semibold))
                        .foregroundColor(.black)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(151.0),
                               height: getRelativeHeight(18.0), alignment: .topLeading)
                        .padding(.leading, getRelativeWidth(2.0))
                }
                   
                .padding(.vertical, getRelativeHeight(8.0))
                   
                Spacer()
                Button {} label: {
                    Text("Change")
                        .font(Font.custom("Nunito", size: 13.relativeFontSize).weight(.semibold))
                        .multilineTextAlignment(.center)
                        .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                        .frame(width: 80.relativeWidth, height: 28.relativeHeight, alignment: .center)
                        .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.21).shadow(color: .black.opacity(0.16), radius: 1, x: 0, y: 1))
                        .clipShape(.capsule)
                }
            }
            .frame(height: 40.0, alignment: .center)
            .padding(.vertical, 4.0)
            .padding(.horizontal)
        }
        .overlay(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                bottomRight: 25.0)
                .stroke(ColorConstants.Cyan800,
                        lineWidth: 1))
    }
}
