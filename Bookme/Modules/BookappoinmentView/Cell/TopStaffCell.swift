//
//  Untitled.swift
//  Bookme
//
//  Created by Apple on 19/10/2024.
//

import SwiftUI

struct TopStaffCell: View {
    let model:StaffModel.StaffData
    let isSelected: Bool
    let onSelect: (StaffModel.StaffData) -> Void
  
    var body: some View {
        
        Button {
            onSelect(model)
        } label: {
            VStack(spacing: 4) {
                // Circle image with border
                NetworkImageView(path:  model.staffImageUrl)
                    .frame(width: 38.relativeFontSize, height: 38.relativeFontSize)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .inset(by: 0.5)
                            .stroke(isSelected ? Color(hex: "#008F96") : Color(red: 0, green: 0.56, blue: 0.59).opacity(0.21) , lineWidth: 1)
                    ).overlay(alignment: .topTrailing) {
                        // Checkmark icon if selected
                        if isSelected {
                            Image(.staffCheckmark)
                                .resizable()
                                .padding(4)
                                .frame(width: 20.relativeFontSize, height: 20.relativeFontSize)
                                .offset(x: 7, y: -1)
                        }
                    }

                // User name below the circle image
                Text(model.staffName)
                    .font(Font.custom("Nunito", size: 9.relativeFontSize))
                    .multilineTextAlignment(.center)
                    .foregroundColor(.black.opacity(0.75))
            }
        }
//        .disableWithOpacity(model.notAvailable)

     
    }
}

struct TopStaffShimmerCell: View {
    var body: some View {
        VStack(spacing: 4) {
            // Circle image with border
            NetworkImageView(path:  nil)
                .frame(width: 38.relativeFontSize, height: 38.relativeFontSize)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .inset(by: 0.5)
                        .stroke(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.21) , lineWidth: 1)
                ).overlay(alignment: .topTrailing) {
                   
                }

            // User name below the circle image
            Text("staffName")
                .font(Font.custom("Nunito", size: 9.relativeFontSize))
                .multilineTextAlignment(.center)
                .foregroundColor(.black.opacity(0.75))
        }
    }
}

//#Preview {
//    TopStaffCell(isSelected: true)
//}
