import SwiftUI

struct RowdateitemCell: View {
    var body: some View {
        HStack {
            Text(StringConstants.kLbl6)
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .padding(.horizontal, getRelativeWidth(11.0))
                .padding(.bottom, getRelativeHeight(7.0))
                .padding(.top, getRelativeHeight(5.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.center)
                .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                        bottomRight: 15.03)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan8003f)
            Text(StringConstants.kLbl7)
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .padding(.horizontal, getRelativeWidth(10.0))
                .padding(.bottom, getRelativeHeight(7.0))
                .padding(.top, getRelativeHeight(5.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.center)
                .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                        bottomRight: 15.03)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan8003f)
                .padding(.leading, getRelativeWidth(22.0))
            ZStack(alignment: .center) {
                Text(StringConstants.kLbl8)
                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Bluegray102)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(5.0), height: getRelativeHeight(17.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(5.93))
                    .padding(.bottom, getRelativeHeight(7.07))
                    .padding(.horizontal, getRelativeWidth(11.14))
                Divider()
                    .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .background(ColorConstants.Bluegray101)
                    .padding(.top, getRelativeHeight(7.06))
                    .padding(.bottom, getRelativeHeight(6.94))
                    .padding(.horizontal, getRelativeWidth(2.79))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                    bottomRight: 15.03)
                    .stroke(ColorConstants.Bluegray101,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                       bottomRight: 15.03)
                    .fill(ColorConstants.Gray5003f))
            .padding(.leading, getRelativeWidth(22.0))
            ZStack(alignment: .center) {
                Divider()
                    .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .background(ColorConstants.Bluegray101)
                    .padding(.top, getRelativeHeight(7.06))
                    .padding(.bottom, getRelativeHeight(6.94))
                    .padding(.horizontal, getRelativeWidth(3.19))
                Text(StringConstants.kLbl9)
                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Bluegray102)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(5.0), height: getRelativeHeight(17.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(5.94))
                    .padding(.bottom, getRelativeHeight(7.06))
                    .padding(.horizontal, getRelativeWidth(10.69))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                    bottomRight: 15.03)
                    .stroke(ColorConstants.Bluegray101,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                       bottomRight: 15.03)
                    .fill(ColorConstants.Gray5003f1))
            .padding(.leading, getRelativeWidth(22.0))
            Text(StringConstants.kLbl102)
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .padding(.leading, getRelativeWidth(8.0))
                .padding(.bottom, getRelativeHeight(7.0))
                .padding(.top, getRelativeHeight(5.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
                       alignment: .leading)
                .overlay(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                        bottomRight: 15.03)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan8003f)
                .padding(.leading, getRelativeWidth(22.0))
            Text(StringConstants.kLbl11)
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .padding(.leading, getRelativeWidth(9.0))
                .padding(.bottom, getRelativeHeight(7.0))
                .padding(.top, getRelativeHeight(5.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
                       alignment: .leading)
                .overlay(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                        bottomRight: 15.03)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan8003f)
                .padding(.leading, getRelativeWidth(22.0))
            Text(StringConstants.kLbl12)
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .padding(.leading, getRelativeWidth(9.0))
                .padding(.bottom, getRelativeHeight(7.0))
                .padding(.top, getRelativeHeight(5.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(28.0), height: getRelativeWidth(30.0),
                       alignment: .leading)
                .overlay(RoundedCorners(topLeft: 15.03, topRight: 15.03, bottomLeft: 15.03,
                                        bottomRight: 15.03)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan8003f)
                .padding(.leading, getRelativeWidth(22.0))
        }
        .frame(width: getRelativeWidth(343.0), alignment: .leading)
        .hideNavigationBar()
    }
}

/* struct RowdateitemCell_Previews: PreviewProvider {

 static var previews: some View {
 			RowdateitemCell()
 }
 } */
