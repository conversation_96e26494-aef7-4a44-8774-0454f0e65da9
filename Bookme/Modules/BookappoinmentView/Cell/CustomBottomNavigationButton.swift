//
//  CustomBottomNavigationButton.swift
//  Bookme
//
//  Created by Apple on 19/10/2024.
//

import SwiftUI

struct CustomBottomNavigationButton: View {
    let subTotal: Double
    let numberOfServices: Int
    var disable: Bool = false
    let onAction: () -> Void
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 0) {
                Text("\(numberOfServices) Service")
                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                    .fontWeight(.medium)
                    .contentTransition(.numericText())
                    .foregroundColor(ColorConstants.WhiteA700)
//                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(
                        height: getRelativeHeight(20.0), alignment: .topLeading)
                    .padding(.horizontal, getRelativeWidth(5.0))
                CurrencyText("\(subTotal)")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.WhiteA700)
                    .contentTransition(.numericText())
                    .multilineTextAlignment(.leading)
                    .frame(
                        height: getRelativeHeight(23.0), alignment: .topLeading)
//                Text("Sub Total")
//                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
//                    .fontWeight(.medium)
//                    .foregroundColor(ColorConstants.WhiteA700)
//                    .minimumScaleFactor(0.5)
//                    .multilineTextAlignment(.leading)
//                    .frame(width: getRelativeWidth(65.0),
//                           height: getRelativeHeight(18.0), alignment: .topLeading)
//                    .padding(.leading, getRelativeWidth(7.0))
//                    .padding(.trailing, getRelativeWidth(10.0))
            }
            .frame(height: getRelativeHeight(61.0),
                   alignment: .center)
            .padding(.top, getRelativeHeight(6.0))
            .padding(.bottom, getRelativeHeight(4.0))
            .padding(.leading, getRelativeWidth(24.0))
            Button(action: onAction, label: {
                HStack(spacing: 0) {
                    Text("Book")
                        .font(FontScheme
                            .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.heavy)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(12.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(
                            height: getRelativeHeight(44.0), alignment: .center)
                        .frame(maxWidth: .infinity)
                        .background(RoundedCorners(topLeft: 22.0, topRight: 22.0,
                                                   bottomLeft: 22.0,
                                                   bottomRight: 22.0)
                                .fill(ColorConstants.Black9003f))
                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0,
                                y: 1)
                        .padding(.top, getRelativeHeight(13.0))
                        .padding(.bottom, getRelativeHeight(15.0))
                        .padding(.leading, getRelativeWidth(16.0))
                }
            })
            
//                    .padding(.bottom, getRelativeHeight(15.0))
            .padding(.horizontal, getRelativeWidth(16.0))
            .disableWithOpacity(disable)
        }
        .animation(.bouncy, value: subTotal)
        .padding(.bottom, AppConstants.tabBarTopPadding.relativeHeight)
        .frame(maxWidth: .infinity)
        .background(RoundedCorners(topLeft: 40.0, topRight: 40.0).fill(ColorConstants.Cyan800))
//        .clipped()
//        .shadow(color: ColorConstants.Black9003f, radius: 6, x: 0, y: 0)
        
        .edgesIgnoringSafeArea(.bottom)
        .contentShape(RoundedCorners(topLeft: 40.0, topRight: 40.0))
    }
}

