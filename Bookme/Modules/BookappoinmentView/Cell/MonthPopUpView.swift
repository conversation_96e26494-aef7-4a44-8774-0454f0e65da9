//
//  MonthPopUpView.swift
//  Bookme
//
//  Created by Apple on 27/05/2024.
//

import SwiftUI

struct MonthPopUpView: View {
    @Environment(\.calendar) var calendar
    @State var selectedMoth: Int // month index (0-11)
    let selectedDate: Date // Used to get the current year context
    let onSubmit: (Date?) -> Void
    let onClose: () -> Void
    
    // New properties for comprehensive disabling logic
    let staffOffDays: [String]
    let vendorMaximumAllowedDate: Date?

    init(
        selectedMonth: Int,
        selectedDate: Date,
        staffOffDays: [String] = [], // New
        vendorMaximumAllowedDate: Date? = nil, // New
        onSubmit: @escaping (Date?) -> Void,
        onClose: @escaping () -> Void
    )
    {
        self.onClose = onClose
        self.onSubmit = onSubmit
        self.selectedMoth = selectedMonth
        self.selectedDate = selectedDate
        self.staffOffDays = staffOffDays
        self.vendorMaximumAllowedDate = vendorMaximumAllowedDate
    }
    @Namespace private var animation
    func updateSelectedMoth(_ value: Int) { self.selectedMoth = value }
    func isSelectedMoth(_ value: Int) -> Bool { value == self.selectedMoth }
    // Renamed and enhanced for clarity and new logic
    private func isMonthEffectivelyDisabled(monthIndex: Int) -> Bool {
        let currentYear = calendar.component(.year, from: selectedDate)
        guard let monthStartDate = calendar.date(from: DateComponents(year: currentYear, month: monthIndex + 1, day: 1)) else {
            return true // Cannot form date, assume disabled
        }

        // If the month itself is in the past (e.g., trying to select Jan 2025 when it's May 2025, but vendor schedule allows it)
        // This check might be too aggressive if vendor schedule allows future years.
        // For now, let's assume we are primarily concerned with the current year context from selectedDate.
        // A more robust solution would consider the year of the monthStartDate vs. today.
        let today = calendar.startOfDay(for: Date())
        if monthStartDate < today && !calendar.isDate(monthStartDate, equalTo: today, toGranularity: .month) {
             // If the entire month is before the current month (and not the current month itself)
             // Check if any day in this past month could still be valid due to a very long vendorMaximumAllowedDate
             // This edge case is complex. For now, let's simplify: if monthStartDate is before today's month, check against maximumAllowedDate.
        }


        guard let monthInterval = calendar.dateInterval(of: .month, for: monthStartDate) else {
            return true
        }
        let daysInMonth = calendar.generateDays(for: monthInterval) // Assumes calendar.generateDays is available
        
        if daysInMonth.isEmpty { return true }

        for dayInMonth in daysInMonth {
            // Replicate the isDisabledDate logic from CalendarSelectionView
            let dateString = dayInMonth.toString(outputFormate: "yyyy-MM-d")
            let isStaffOffDay = staffOffDays.contains(dateString)

            var isBeyondVendorSchedule = false
            if let maxDate = vendorMaximumAllowedDate {
                if calendar.startOfDay(for: dayInMonth) > calendar.startOfDay(for: maxDate) {
                    isBeyondVendorSchedule = true
                }
            }
            
            let isPastActualDate = dayInMonth.isPastDate // Individual day check

            if !(isPastActualDate || isStaffOffDay || isBeyondVendorSchedule) {
                return false // Found at least one enabled day
            }
        }
        return true // All days in this month are effectively disabled
    }
    var body: some View {
            VStack(spacing: 30.relativeHeight) {
                VStack(spacing: 16.0.relativeHeight) {
                    VStack(spacing: 0) {
                        HStack {
                            Text("Select Month")
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(16.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Black900)
                                .padding()
                            Spacer()

                            Button(action: self.onClose) {
                                Image("img_closeroundlig")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(20.0),
                                           height: getRelativeWidth(20.0), alignment: .center)

                                    .padding(.horizontal, getRelativeWidth(6.0))
                            }
                        }

                        Divider()
                            .frame(width: getRelativeWidth(349.0),
                                   height: getRelativeHeight(1.0), alignment: .center)
                            .background(ColorConstants.Cyan8003f)
                            .padding(.trailing, getRelativeWidth(4.0))
                    }
                    // generateMonths is redundant. You can get the month names using Calendar.monthSymbols
                    let months = self.calendar.monthSymbols
                    LazyVGrid(columns: Array(repeating: GridItem(), count: 4)) {
                        ForEach(months.indices, id: \.self) { index in
                            let monthText: String = months[index]
                               
                            Button(action: {
                                self.updateSelectedMoth(index)
                            }, label: {
                                Text(monthText)
                                    .font(FontScheme
                                        .kNunitoSemiBold(size: getRelativeHeight(11.0)))
                                    .fontWeight(.semibold)
                                    .padding(.horizontal, getRelativeWidth(14.0))
                                    .foregroundColor(
                                        self.isSelectedMoth(index) ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                                    .multilineTextAlignment(.center)
                                    .fixedSize()
                                    .frame(width: 80.0.relativeWidth,
                                           height: getRelativeHeight(29.0),
                                           
                                           alignment: .center)
                                           
                                    .background(
                                        self.isSelectedMoth(index) ? ColorConstants.Cyan800.clipShape(.capsule) : ColorConstants.Cyan80035.clipShape(.capsule))
                                    .overlay(Capsule().stroke(
                                        ColorConstants.Cyan800,
                                        lineWidth: 1))
                                    .clipShape(.capsule)
                                            
                            })
                            
                            .if(self.isSelectedMoth(index), transform: {
                                $0.matchedGeometryEffect(id: "time.slot", in: self.animation)
                            })
                                
                            .padding(2)
                            .tag(index)
                            .disableWithOpacity(self.isMonthEffectivelyDisabled(monthIndex: index))
                        }
                    }
                    .animation(.bouncy, value: self.selectedMoth)
                    .padding(.top, getRelativeHeight(12.0))
                        
                }.padding(.bottom, 6)
                 
                Button(action: {
                    self.onClose()
                    
                    if let date = monthNumberIntoDate(self.selectedMoth + 1, selectedDate: selectedDate) {
                        self.onSubmit(date)
                    } else {
                        self.onSubmit(nil)
                    }
                    
                }, label: {
                    HStack(spacing: 0) {
                        Text("Submit")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5,
                                                       bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.bottom, AppConstants.tabBarHeight.relativeHeight + 16.0.relativeHeight)
                            .padding(.horizontal, getRelativeWidth(21.0))
                    }
                })
            }
            .padding(.horizontal)
            .frame(maxWidth: .infinity)
            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0).fill(ColorConstants.WhiteA700))
            .clipped()
            .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
    }
}
struct SlideUpAnimationContainerView<Content>: View where Content: View {
    let perform: () -> Void
    let content: () -> Content
    var body: some View {
        ZStack(alignment: .bottom) {
            ZStack {}
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(ColorConstants.Black90066)
                .transition(.opacity)
                .onTapGesture(perform: self.perform)
                .transaction { $0.animation = nil }
            self.content()
        }
    }
}

// struct PreViewContainer: View {
//    @Environment(\.calendar) var calendar
//    @State var selectedDate: Date = .now
//    var body: some View {
//        MonthPopUpView(selectedMonth: Calendar.current.component(.month, from: self.selectedDate) - 1, onSubmit: { number in
//            // CurrentDate
//
//            let gregorianCalendar = Calendar(identifier: .gregorian)
//            var components = gregorianCalendar.dateComponents([.year, .month, .day, .hour, .minute, .second], from: self.selectedDate)
//
//            // Change int value which you want to minus from current Date.
//            components.month = number + 1
//            let date = gregorianCalendar.date(from: components)!
//            print("Old Date = \(self.selectedDate)")
//            print("New Date = \(date)")
//
//        }, onClose: {})
//    }
// }

// #Preview {
//    PreViewContainer()
// }
