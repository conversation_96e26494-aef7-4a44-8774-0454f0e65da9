//
//  BookAppoinmentModel.swift
//  Bookme
//
//  Created by Apple on 08/05/2024.

import Foundation

enum BookAppointmentPopUpType {
    case month, note
}

struct BookingRequestModel: Identifiable, Equatable, Hashable {
    let id: UUID = .init()
    var date: Date = .now
    var time: String?
    var service: [VendorDetailsModel.Service]
}

// MARK: - CheckoutSuccessModel

struct CheckoutSuccessModel: Codable {
    let bookid: Int?
    let mobile: String?
    let status, vendorID: Int
    let totalPrice: String
    let userID: Int
    let serviceDate, serviceTime: String
    let link:String?
    
    
    var serviceDateFormatted:String{
        serviceDate.timeDateFromDateString(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: "EEEE, MMMM dd, yyyy")
    }
    
    
    var serviceTimeFormatted:String{
        serviceDate.timeDateFromDateString(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: "hh:mma")
    }

    enum CodingKeys: String, CodingKey {
        case bookid, mobile, status, link
        case vendorID = "vendor_id"
        case totalPrice = "total_price"
        case userID = "user_id"
        case serviceDate = "service_date"
        case serviceTime = "service_time"
    }
}





// MARK: - SubmitLocationResponse
struct PaymentGatewayModel: Codable, Equatable, Hashable {
    let bookid: Int
    let totalPrice: String
    let vendorID, userID: Int
    let mobile:String?
    let serviceDate, serviceTime: String
    let status: Int
    let couponID: String?
    let couponDiscount, paymentMethod, paymentID, paymentStatus: String
    let trackID, paymentDate: String?

    
    var serviceDateFormatted:String{
        serviceDate.timeDateFromDateString(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: "EEEE, MMMM dd, yyyy")
    }
    
    
    var serviceTimeFormatted:String{
        serviceDate.timeDateFromDateString(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: "hh:mma")
    }
    
    
    enum CodingKeys: String, CodingKey {
        case bookid
        case totalPrice = "total_price"
        case vendorID = "vendor_id"
        case userID = "user_id"
        case serviceDate = "service_date"
        case mobile
        case serviceTime = "service_time"
        case status
        case couponID = "coupon_id"
        case couponDiscount = "coupon_discount"
        case paymentMethod = "payment_method"
        case paymentID = "payment_id"
        case paymentStatus = "payment_status"
        case trackID = "track_id"
        case paymentDate = "payment_date"
    }
}





// MARK: - VendorTimeslotModel

struct VendorTimeslotModel: Codable, Equatable {
    let timeSlot: [TimeSlot]

    enum CodingKeys: String, CodingKey {
        case timeSlot = "time_slot"
    }

    struct TimeSlot: Codable, Identifiable, Equatable {
        let id: UUID = .init()
        let timeslot: String
        let disabled: Bool
        var isDisabled: Bool { disabled }
        enum CodingKeys: String, CodingKey {
            case timeslot = "time", disabled
        }
    }
}

struct CartModel: Codable, Identifiable, Equatable, Hashable {
    let cartID: Int
    let serviceName: String
    let serviceID, userID, vendorID: Int
    let startDate, duration, price: String
    let delay1: String?
//    let startTime, endTime, date1: String
    let startTime, endTime: String
    let staffID: String
    let staffName, staffImage, designation: String

    var id: Int { cartID }

    var staffImageUrl: String {
        AppConstants.Server.baseURL + staffImage
    }
    
    
    var formattedDateString: String? {
        startDate.dateFormatter(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: dateOutputFormat)
    }
    
    var monthFormattedDateString: String? {
        startDate.dateFormatter(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: "MMMM dd,yyyy")
    }
    
    var monthFormattedDateCheckoutString: String? {
        startDate.dateFormatter(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: "MMM-dd-yyyy")
    }

    enum CodingKeys: String, CodingKey {
        case cartID = "Cart_ID"
        case serviceName = "service_name"
        case serviceID = "service_id"
        case userID = "user_id"
        case vendorID = "vendor_id"
        case startDate = "start_date"
        case duration, price, delay1
        case startTime = "start_time"
        case endTime = "end_time"
        case staffID = "staff_id"
        case staffName = "staff_name"
        case staffImage = "staff_image"
        case designation
    }
}

enum UpdateCartTypeEnum {
    case dateTime(date:String, time:String), staff(staffID: String), dateTimeAndStaff(date:String, time:String, staffID: String)
}





// MARK: - CouponCodeModel
struct CouponCodeModel: Codable {
    let afterDiscountTotalPrice: Int
    let deductedAmount: String
}


