//
//  UPaymentCardView.swift
//  Bookme
//
//  Created by Apple on 27/11/2024.
//

import SwiftUI

struct UPaymentCardView: View {
    let url: String
    let onPaymentCapture: (Bool) -> Void
    @StateObject var model: WebViewModel
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager: RouterManager
    init(url: String, onPaymentCapture: @escaping (Bool) -> Void) {
        self.url = url
        self.onPaymentCapture = onPaymentCapture
        self._model = StateObject(wrappedValue: WebViewModel(url: url))
    }

    var body: some View {
        SuperView(pageState: $model.pageState) {
            MainScrollBody(backButtonWithTitle: "", enableScroll: false) {
                LoadingView(isShowing: self.$model.isLoading) {
                    WebView<VoidStruct>(redirectURL: "https://projects.crisance.com/bookme/adminpanel/public/api/AddCardResultPage", viewModel: self.model, onPaymentCapture: { response in
                        let type = self.routerManager.mapRouterWithTab(appState: self.appState)
                        if response.success {
                            self.onPaymentCapture(response.success)
                        } else {
                            self.model.updatePageState(.message(config: .init(title: "Failure", text: response.message, cancelButtonText: "", okButtonText: "Ok", alertType: .alert, onOk: {
                                self.routerManager.goBack(where: type)
                            })))
                        }

                    })
                }
                .onChange(of: self.model.isLoading, initial: true, self.model.onChangeOfLoading)
            }
        }
    }
}
