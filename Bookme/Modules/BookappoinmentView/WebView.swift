////
////  WebView.swift
////  Bookme
////
////  Created by Apple on 14/11/2024.
////
//
//import SwiftUI
//import WebKit
//
//struct WebView: UIViewRepresentable {
//    let url: URL
//    let redirectURL: String
//    let onRedirect: (PaymentGatewayModel?) -> Void // Modify onRedirect to return ParsedData?
//    @Binding var isLoading: Bool
//
//    func makeCoordinator() -> Coordinator {
//        Coordinator(self)
//    }
//
//    func makeUIView(context: Context) -> WKWebView {
//        let webView = WKWebView()
//        webView.navigationDelegate = context.coordinator
//        webView.load(URLRequest(url: url))
//        return webView
//    }
//
//    func updateUIView(_ webView: WKWebView, context: Context) {}
//
//    class Coordinator: NSObject, WKNavigationDelegate {
//        var parent: WebView
//
//        init(_ parent: WebView) {
//            self.parent = parent
//        }
//
//        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
//            parent.isLoading = true
//        }
//
//        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
//            parent.isLoading = false
//        }
//
//        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
//            if let urlString = navigationAction.request.url?.absoluteString, urlString.contains(parent.redirectURL) {
//                // Only call onRedirect when redirect URL is matched
//                webView.evaluateJavaScript("document.documentElement.outerHTML.toString()") { html, error in
//                    guard error == nil, let html = html as? String else { return }
//
//                    // Parse the model from HTML and return it
//                    let parsedData = self.parseHTMLToJSON(html: html)
//                    self.parent.onRedirect(parsedData)
//                }
//
//                decisionHandler(.cancel)
//                return
//            }
//            decisionHandler(.allow)
//        }
//
//        private func parseHTMLToJSON(html: String) -> PaymentGatewayModel? {
//            let pattern = #"(?<=<script id="json-data">)(.*?)(?=</script>)"# // Example pattern for JSON in a script tag
//            guard let regex = try? NSRegularExpression(pattern: pattern, options: .dotMatchesLineSeparators) else { return nil }
//
//            // Extract JSON string from HTML
//            if let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.utf16.count)),
//               let jsonRange = Range(match.range, in: html)
//            {
//                let jsonString = String(html[jsonRange])
//
//                // Decode JSON string to ParsedData using Codable
//                let jsonData = Data(jsonString.utf8)
//                let decoder = JSONDecoder()
//                do {
//                    let parsedData = try decoder.decode(CommonApiResponse<PaymentGatewayModel>.self, from: jsonData)
//                    return parsedData.data
//                } catch {
//                    print("Decoding error: \(error)")
//                    return nil
//                }
//            }
//
//            print("JSON data not found in HTML")
//            return nil
//        }
//    }
//}
