import SwiftUI

struct StaffSelectionView: View {
    let isLoading: Bool
    let staffList: [ServiceStaffModel]
    let selectedStaffID: Int?
    let onSelect: (ServiceStaffModel) -> Void
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: 16) {
                if isLoading {
                    ForEach(0...9, id: \.self) { _ in
                        TopStaffShimmerCell()
                    }
                    .shimmerize()
                } else {
                    ForEach(staffList, id: \.self) { staff in
                        TopStaffCell(
                            model: staff,
                            isSelected: selectedStaffID == staff.staffID,
                            onSelect: { onSelect(staff) }
                        )
                    }
                }
            }
        }
        .scrollClipDisabled()
        .safeAreaPadding(.horizontal)
        .scrollIndicators(.hidden)
        .padding(.top)
        .padding(.bottom, 32.relativeHeight)
    }
}

// MARK: - Preview
#Preview {
    StaffSelectionView(
        isLoading: false,
        staffList: [
            .init(staffID: 1, name: "<PERSON>", image: "staff1"),
            .init(staffID: 2, name: "<PERSON>", image: "staff2")
        ],
        selectedStaffID: 1,
        onSelect: { _ in }
    )
}

// Helper structs if not already defined elsewhere
private struct TopStaffCell: View {
    let model: ServiceStaffModel
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 8) {
                Circle()
                    .fill(isSelected ? ColorConstants.Cyan800 : ColorConstants.WhiteA700)
                    .frame(width: 60, height: 60)
                    .overlay {
                        AsyncImage(url: URL(string: model.image)) { phase in
                            switch phase {
                            case .empty:
                                ProgressView()
                            case .success(let image):
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            case .failure:
                                Image(systemName: "person.fill")
                            @unknown default:
                                EmptyView()
                            }
                        }
                        .clipShape(Circle())
                    }
                    .overlay {
                        Circle()
                            .stroke(isSelected ? ColorConstants.Cyan800 : .clear, lineWidth: 2)
                    }
                
                Text(model.name)
                    .font(.system(size: 12))
                    .foregroundColor(isSelected ? ColorConstants.Cyan800 : .black)
                    .lineLimit(1)
            }
            .frame(width: 80)
        }
    }
}

private struct TopStaffShimmerCell: View {
    var body: some View {
        VStack(spacing: 8) {
            Circle()
                .fill(ColorConstants.Gray300)
                .frame(width: 60, height: 60)
            
            Rectangle()
                .fill(ColorConstants.Gray300)
                .frame(width: 60, height: 12)
                .cornerRadius(6)
        }
        .frame(width: 80)
    }
}
