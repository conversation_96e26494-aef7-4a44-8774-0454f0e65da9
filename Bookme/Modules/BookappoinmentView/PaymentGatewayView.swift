//
//  PaymentGateway.swift
//  Bookme
//
//  Created by Apple on 14/11/2024.
//
import SwiftUI
import <PERSON><PERSON><PERSON><PERSON><PERSON>
@preconcurrency import WebKit


class WebViewModel: SuperViewModel {
    @Published var url: String
    @Published var isLoading: Bool = true

    init(url: String) {
        self.url = url
    }

    func onChangeOfLoading(oldValue: Bool, newValue: Bool) {
        if newValue {
            updatePageState(.loading(true))
        } else {
            updatePageState(.stable)
        }
    }
}

struct WebView<T: Codable>: UIViewRepresentable {
    let redirectURL: String
    @ObservedObject var viewModel: WebViewModel
    let webView = WKWebView()
    let onPaymentCapture: (CommonApiResponse<T>) -> Void
    func makeCoordinator() -> Coordinator<T> {
           Coordinator(self.redirectURL, self.viewModel, onPaymentCapture: self.onPaymentCapture)
       }

    func updateUIView(_ uiView: UIView, context: UIViewRepresentableContext<WebView<T>>) {}

    func makeUIView(context: Context) -> UIView {
        self.webView.navigationDelegate = context.coordinator

        if let url = URL(string: self.viewModel.url) {
            self.webView.load(URLRequest(url: url))
        }

        return self.webView
    }

    class Coordinator<T: Codable>: NSObject, WKNavigationDelegate {
        let redirectURL: String
        private var viewModel: WebViewModel
        let onPaymentCapture: (CommonApiResponse<T>) -> Void

        init(_ redirectURL: String, _ viewModel: WebViewModel, onPaymentCapture: @escaping (CommonApiResponse<T>) -> Void) {
            self.redirectURL = redirectURL
            self.viewModel = viewModel
            self.onPaymentCapture = onPaymentCapture
        }

        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            self.viewModel.isLoading = true
        }

        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            
            
            
           

            let url = webView.url

            if let url = url {
                print(url.absoluteString)
                let isFinalPage: Bool = url.absoluteString.contains(self.redirectURL)
                
                if !isFinalPage {
                    self.viewModel.isLoading = false
                }
                
                
                if isFinalPage {
                    webView.evaluateJavaScript("document.body.innerText", completionHandler: { (value: Any!, error: Error!) in
                        if error != nil { return }

                        if let data = value as? String {
                            let json = SwiftyJSON.JSON(parseJSON: data)
                            print(json)
                            if let model = data.responseDecodable(of: CommonApiResponse<T>.self) {
                                self.onPaymentCapture(model)
                            }
                        } else {
                            self.viewModel.updatePageState(.failure(error: "Parsing error"))
                        }
                    })
                }
            }
        }

        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            decisionHandler(.allow)
        }

        func webView(_ webView: WKWebView, decidePolicyFor navigationResponse: WKNavigationResponse, decisionHandler: @escaping (WKNavigationResponsePolicy) -> Void) {
            // TODO:
            decisionHandler(.allow)
        }
    }
}


