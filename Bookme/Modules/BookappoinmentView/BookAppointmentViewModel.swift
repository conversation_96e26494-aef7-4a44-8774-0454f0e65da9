
import SwiftUI

let dateOutputFormat: String = "yyyy-MM-dd"
let dateInputFormat: String = "yyyy-MM-dd"

class BookAppointmentViewModel: SuperViewModel {
    @Published var selectedBookingRequestModel: BookingRequestModel?
    
    @Published var vendorTimeslotModelList: [VendorTimeslotModel.TimeSlot] = []
    @Published var selectedTimeSlot: VendorTimeslotModel.TimeSlot?
    
    @Published var scrollPosition: String?
    
    @Published var cartModelList: [CartModel] = []
    @Published var selectedCartModel: CartModel?
    
    @Published var timeSlotErrorMessage: String?
    @Published var selectedDate: Date = BookAppointmentViewModel.now
   
    @Published var bookAppointmentPopUpType: BookAppointmentPopUpType? = nil
    
    private var additionalNote: String?
    @Published var additionalNoteText: String = .init()
    
    @Published private(set) var serviceStaffModelList: [StaffModel.StaffData] = []
    @Published private(set) var selectedServiceStaff: StaffModel.StaffData?
    
    // Track if staff was manually selected by user
    @Published private(set) var isStaffManuallySelected: Bool = false
    
    @Published var selectedServiceStaffOffDays: [String] = []
    
    @Published var couponCode: String = .init()
    @Published var couponCodeErrorMessage: String?
    @Published var couponCodeModel: CouponCodeModel?
    
    static var now = Date() // Cache now
    
    var maximumAllowedDate: Date? {
        guard vendor.calendarSchedule > 0 else { return nil } 
        let today = Calendar.current.startOfDay(for: Date())
        return Calendar.current.date(byAdding: .day, value: vendor.calendarSchedule - 1, to: today)
    }
    
    var isInitialLoad: Bool = false
   
    let vendor: VendorShortDetailsModel
    let selectedFirstService: [VendorDetailsModel.Service]
    let bookID: Int?
    init(vendor: VendorShortDetailsModel, selectedService: [VendorDetailsModel.Service], bookID: Int?) {
        self.vendor = vendor
        self.selectedFirstService = selectedService
        self.bookID = bookID
        super.init()
        addServiceToBookingRequest(selectedService)
    }
    
    var appliedCouponCode: String? {
        guard let _ = couponCodeModel, !couponCode.isEmpty else { return nil }
        return couponCode
    }

    func addServiceToBookingRequest(_ value: [VendorDetailsModel.Service]) {
        selectedBookingRequestModel = .init(date: selectedDate, service: selectedFirstService)
    }
    
    // Separate function to handle date change logic
    func handleDateChange(_ oldValue: Date, _ newValue: Date) {
        // Update the date in the model immediately
        var currentBookingModel = selectedBookingRequestModel
        currentBookingModel?.date = newValue
        selectedBookingRequestModel = currentBookingModel // This is the source of truth for the date

        // Only set timeslot loading to true, not calendar loading
        // This way the calendar won't show the shimmer effect when only the date changes
        isTimeslotLoading = true
        
        // Always refresh staff and timeslots for the new date
        guard let firstService = selectedFirstService.first else {
            isTimeslotLoading = false // Reset if we can't proceed
            return
        }
        
        // This call will initiate the chain: fetch staff -> select staff -> fetch timeslots
        getServiceStaffList(firstService)
    }
    
    func onCouponCodeApply() {
        if couponCodeModel != nil {
            removeCouponCode()
        } else {
            couponCodeApply()
        }
    }
    
    @Published var isBookingLoading: Bool = false
    
    // Convert the `Published` property to a `Binding`
    var isBookingLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isBookingLoading }, // Getter for the binding
            set: { self.isBookingLoading = $0 } // Setter for the binding
        )
    }
    
    func onBook(completion: @escaping (String) -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        var parameters: [String: Any] = ["user_id": userID]
        if let additionalNote = additionalNote {
            parameters.updateValue(additionalNote, forKey: "notes")
        }
        
        if let couponCode = appliedCouponCode {
            parameters.updateValue(couponCode, forKey: "coupon_code")
        }
        print(parameters)
        onApiCall(api.bookings, parameters: parameters, customLoadingBinding: isBookingLoadingBinding) { response in
           
            if let link = response.data?.link { completion(link) }
                
//                self.updatePageState(.message(config: .init(title: "Success", text: response.message, cancelButtonText: "", okButtonText: "Ok", alertType: .alert, onOk: {
//                    if let link = response.data?.link { completion(link) }
//                })))
        }
    }
    
    @Published var isCouponLoading: Bool = false
    
    // Convert the `Published` property to a `Binding`
    var isCouponLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isCouponLoading }, // Getter for the binding
            set: { self.isCouponLoading = $0 } // Setter for the binding
        )
    }
    
    func updateCouponCodeErrorMessage(_ value: String?) { couponCodeErrorMessage = value }
    
    func couponCodeApply(showLoading: Bool = true) {
        updateCouponCodeErrorMessage(nil)
        
        guard
            let userID = AppState.userModel?.user.id
        else { return }
        let parameters: [String: Any] = ["user_id": userID, "vendor_id": vendor.vendorID, "coupon_code": couponCode]
        
        onApiCall(api.couponCodeApply, parameters: parameters, hideClientSideError: true, customLoadingBinding: isCouponLoadingBinding, onSuccess: {
            self.couponCodeModel = $0.data
            self.couponCodeErrorMessage = $0.error ? $0.message : nil
        }, onFailure: { error in
            self.updateCouponCodeErrorMessage(error)
        })
    }
    
    func removeCouponCode() {
        couponCodeModel = nil
        couponCode.removeAll()
    }
    
    func addToCart(model: BookingRequestModel, time: String) {
        guard
            let userID = AppState.userModel?.user.id,
            let staffID = model.service[0].selectedStaff?.staffID
        else { return }
        
        let date: String = model.date.toString(outputFormate: dateOutputFormat)
        var parameters: [String: Any] = [
            "staff": staffID,
            "vendor_id": vendor.vendorID,
            "date": date,
            "user_id": userID,
            "services": model.service.map { String($0.servicesID) }.joined(separator: ","),
            "time": time,
            "newcart": isInitialLoad ? 0 : 1,
        ]
        
        if let bookID = bookID {
            parameters.updateValue(bookID, forKey: "book_id")
        }
        
        onApiCall(api.addToCart, parameters: parameters, customLoadingBinding: isCartLoadingBinding) {
            if $0.success {
                if !self.isInitialLoad {
                    self.isInitialLoad = true
                }
                
                self.getCartList()
            }
        }
    }
    
    @Published var isCartLoading: Bool = false
    // Convert the `Published` property to a `Binding`
    var isCartLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isCartLoading }, // Getter for the binding
            set: { self.isCartLoading = $0 } // Setter for the binding
        )
    }
    
    func getCartList(preselect: Bool = false) {
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": userID, "vendor_id": vendor.vendorID]
        onApiCall(api.viewCart, parameters: parameters, customLoadingBinding: isCartLoadingBinding) {
            self.cartModelList = $0.data ?? []
            
            if let model = self.cartModelList.first {
                self.selectedCartModel = model
                
                if preselect {
                    if let selectedBookingRequestModel = self.selectedBookingRequestModel {
                        self.getStaffTimeslot(selectedBookingRequestModel, preselect: model.startTime) {}
                    }
                }
            }
            
            if let appliedCouponCode = self.appliedCouponCode {
                self.couponCodeApply(showLoading: false)
            }
        }
    }
    
    func onChangeStaffFromService(model: CartModel, staff: StaffModel.StaffData, updateLocalStaff: Bool, refreshTimeSlot: Bool = false, preselect: String? = nil) {
//        if cartModelList.count == 1 || model.cartID == cartModelList.first?.cartID {
//            updateSelectedServiceStaff(staff: staff, cartModel: model, updateLocalStaff: updateLocalStaff)
//        }else{
//        updateToCart(model: model, updateType: .staff(staffID: staff.staffID), updateLocalStaff: updateLocalStaff)
//        }
        
//
        if refreshTimeSlot {
            if let selectedBookingRequestModel = selectedBookingRequestModel {
                var data = selectedBookingRequestModel
                data.service[0].selectedStaff = staff
                getStaffTimeslot(data) {
                    let selectedDate: String = self.selectedDate.toString(outputFormate: dateOutputFormat)
                    let selectedTime = self.selectedTimeSlot?.timeslot ?? ""
                    
//                    self.updateToCart(model: model, updateType: .staff(staffID: staff.staffID), updateLocalStaff: updateLocalStaff)
                    self.updateToCart(model: model, updateType: .dateTimeAndStaff(date: selectedDate, time: selectedTime, staffID: staff.staffID), updateLocalStaff: true)
                }
            }
        } else {
            updateToCart(model: model, updateType: .staff(staffID: staff.staffID), updateLocalStaff: updateLocalStaff, preselect: preselect)
        }
    }
    
    func updateToCart(model: CartModel, updateType: UpdateCartTypeEnum, updateLocalStaff: Bool = false, preselect: String? = nil) {
//        let cartDate: String = model.formattedDateString
        
        var parameters: [String: Any] = [
            "vendor_id": model.vendorID,
            "user_id": AppState.userModel?.user.id ?? "",
        ]
        
        switch updateType {
        case let .dateTime(date, time):
            parameters.updateValue(date, forKey: "selected_date")
            parameters.updateValue(time, forKey: "time")
        case let .staff(staffID):
            guard staffID != String(model.staffID) else { return }
            parameters.updateValue(model.cartID, forKey: "Cart_ID")
            parameters.updateValue(staffID, forKey: "staff")
        case let .dateTimeAndStaff(date: date, time: time, staffID: staffID):
            parameters.updateValue(date, forKey: "selected_date")
            parameters.updateValue(time, forKey: "time")
            parameters.updateValue(staffID, forKey: "staff")
            parameters.updateValue(model.cartID, forKey: "Cart_ID")
        }
        
        onApiCall(api.updateCart, parameters: parameters, customLoadingBinding: isCartLoadingBinding) {
            if $0.success {
                self.getCartList()
                
                if updateLocalStaff {
                    if case let .staff(staffID) = updateType {
                        self.updateSelectedServiceStaff(staff: self.serviceStaffModelList.first(where: { $0.staffID == staffID }), preselect: preselect, isManualSelection: false)
//                        self.selectedServiceStaff = self.serviceStaffModelList.first(where: { $0.staffID == staffID })
//                        self.updateCalendarLoading()
                    }
                    
                    if case let .dateTimeAndStaff(date, time, staffID) = updateType {
                        self.updateSelectedServiceStaff(staff: self.serviceStaffModelList.first(where: { $0.staffID == staffID }), isManualSelection: false)
                    }
                }
            }
        } onFailure: { _ in
        }
    }
    
    func updateCalendarLoading(forceLoading: Bool = false) {
        // Only set calendar loading to true if explicitly requested
        if forceLoading {
            isCalendarLoading = true
            
            Utilities.enQueue(after: .now() + 0.5) {
                self.isCalendarLoading = false
            }
        }
    }
    
    func deleteCartItem(model: CartModel) {
        guard let userID = AppState.userModel?.user.id else { return }
        
        let parameters: [String: Any] = ["user_id": userID, "Cart_ID": model.cartID, "time": selectedTimeSlot?.timeslot]
        onApiCall(api.removeFromCart, parameters: parameters, customLoadingBinding: isCartLoadingBinding) {
            if $0.success {
                self.getCartList(preselect: true)
            }
        }
    }
    
    var isBookingDisabled: Bool {
        cartModelList.isEmpty
    }
 
    func updateSelectedServiceStaff(staff: StaffModel.StaffData? = nil, cartModel: CartModel? = nil, updateLocalStaff: Bool = false, preselect: String? = nil, isManualSelection: Bool = true) {
//        guard cartModelList.count < 2 else { return }
//        guard selectedServiceStaff?.staffID != staff?.staffID else { return }
        
        // Only set calendar loading to true when staff changes
        // This is the key change - calendar will only show shimmer when staff changes
        let staffChanged = selectedServiceStaff?.staffID != staff?.staffID
        if staffChanged {
            isCalendarLoading = true
        }
        
        selectedServiceStaff = staff
        selectedServiceStaffOffDays = staff?.offDays ?? []
        
        // Mark as manually selected if this is a user action
        if isManualSelection {
            isStaffManuallySelected = true
        }
        
        let dateString = selectedDate.toString(outputFormate: dateOutputFormat)
        let isPastDate = selectedDate.isPastDate
        let isOffDay: Bool = selectedServiceStaffOffDays.contains(dateString)
//        in here implement selected date
        if isPastDate || isOffDay {
            // Use the extracted function
            let nextAvailableDate = findNextAvailableDate(from: selectedDate, offDays: selectedServiceStaffOffDays)
            // Update selectedDate to the next available date
            Utilities.enQueue(after: .now() + 0.5) {
                self.selectedDate = nextAvailableDate
            }
        }
        
       
        
        var model = selectedBookingRequestModel
        model?.service[0].selectedStaff = selectedServiceStaff
        updateSelectedBookingRequestModel(model, staff: staff, cartModel: cartModel, updateLocalStaff: updateLocalStaff, preselect: preselect)
        
        // Only reset calendar loading if it was set to true (which happens when staff changes)
        if isCalendarLoading {
            Utilities.enQueue(after: .now() + 0.5) {
                self.isCalendarLoading = false
            }
        }
    }
    
    func findNextAvailableDate(from date: Date, offDays: [String]) -> Date {
        let calendar = Calendar.current
        var nextAvailableDate = date

        while true {
            let dateString = nextAvailableDate.toString(outputFormate: dateOutputFormat)
            
            // Check if the date is in the past or is an off day
            if nextAvailableDate.isPastDate || offDays.contains(dateString) {
                // Move to the next day
                nextAvailableDate = calendar.date(byAdding: .day, value: 1, to: nextAvailableDate) ?? nextAvailableDate
            } else {
                break // Exit the loop when an available date is found
            }
        }

        return nextAvailableDate
    }
    
    @Published var isServiceStaffListLoading: Bool = false
    
    @Published var isCalendarLoading: Bool = true
    
    // Convert the `Published` property to a `Binding`
    var isServiceStaffListLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isServiceStaffListLoading }, // Getter for the binding
            set: { self.isServiceStaffListLoading = $0 } // Setter for the binding
        )
    }
    
    func areDatesInDifferentMonthsAndYears(_ date1: Date, _ date2: Date) -> Bool {
        let calendar = Calendar.current
        
        let date1Components = calendar.dateComponents([.year, .month], from: date1)
        let date2Components = calendar.dateComponents([.year, .month], from: date2)
        
        return date1Components.year != date2Components.year || date1Components.month != date2Components.month
    }

    func getServiceStaffList(_ service: VendorDetailsModel.Service) {
        guard cartModelList.count < 2 else { return }
        // Only set timeslot loading to true, not calendar loading
        // Calendar loading will only be set to true when staff changes
        isTimeslotLoading = true
        
        // Store current staff selection before making API call
        let previouslySelectedStaffID = selectedServiceStaff?.staffID
        let wasManuallySelected = isStaffManuallySelected
        
        var parameters: [String: Any] = ["service": service.servicesID, "vendor_id": vendor.vendorID, "date": selectedDate.toString(outputFormate: dateOutputFormat)]
         
        if let selectedTimeSlot = selectedTimeSlot?.timeslot {
            parameters.updateValue(selectedTimeSlot, forKey: "time")
        }
        
        onApiCall(api.staffAvailableSlots, parameters: parameters, customLoadingBinding: isServiceStaffListLoadingBinding) {
            // isServiceStaffListLoading is handled by its binding.
            // isCalendarLoading was set true in handleDateChange and will be set false upon completion of getStaffTimeslot or on failure.
            // isTimeslotLoading was set true at the start of this function and will be set false by getStaffTimeslot or on failure.
            self.serviceStaffModelList = $0.data?.staffData.uniqued() ?? []
            
            // Try to preserve previous staff selection if it was manually selected and that staff is available on the new date
            if wasManuallySelected,
               let previousID = previouslySelectedStaffID,
               let previouslySelectedStaff = self.serviceStaffModelList.first(where: { $0.staffID == previousID }) {
                // Reselect the previously selected staff, but mark it as not a manual selection since we're doing this programmatically
                self.updateSelectedServiceStaff(staff: previouslySelectedStaff, isManualSelection: false)
            } else {
                // Fall back to default selection logic if previous staff is not available or wasn't manually selected
                let nullableStaffID: Int? = self.selectedFirstService.first?.staffID
                let firstStaffID: String = nullableStaffID == nil ? self.vendor.staffID : String(nullableStaffID ?? 0)
                let selectedStaff = self.serviceStaffModelList.first(where: { $0.staffID == firstStaffID })
                // Use default staff, mark as not manually selected
                self.updateSelectedServiceStaff(staff: selectedStaff, isManualSelection: false)
            }
        } onFailure: { _ in
            // Ensure loading states are reset if fetching staff fails
            self.isCalendarLoading = false
            self.isTimeslotLoading = false
            // isServiceStaffListLoading is handled by its binding
        }
    }
    
    func updateBookAppointmentPopUpType(_ value: BookAppointmentPopUpType?) {
        bookAppointmentPopUpType = value
    }
    
    var couponCodeDiscount: Double {
        couponCodeModel?.deductedAmount.toDouble ?? 0
    }
    
    var subTotal: Double {
        cartModelList.sum(\.price.toDouble)
    }
    
    var grandTotal: Double {
        subTotal - couponCodeDiscount
    }
    
    func updateSelectedBookingRequestModel(_ value: BookingRequestModel?, staff: StaffModel.StaffData? = nil, cartModel: CartModel? = nil, updateLocalStaff: Bool = false, preselect: String? = nil) {
        selectedBookingRequestModel = value
        if let value = value {
            selectedDate = value.date
            getStaffTimeslot(value, preselect: preselect) {
                if
                    let staff = staff,
                    let cartModel = cartModel
                {
                    self.updateToCart(model: cartModel, updateType: .staff(staffID: staff.staffID), updateLocalStaff: updateLocalStaff)
                }
            }
        }
    }
    
    func updateAdditionalNote(_ value: BookAppointmentPopUpType?, onSubmit: Bool = false) {
        if let _ = value {
            additionalNoteText = additionalNote ?? ""
        } else {
            if onSubmit {
                additionalNote = additionalNoteText
            } else {
                additionalNoteText = ""
            }
        }

        bookAppointmentPopUpType = value
    }
    
    func onDateChange(oldValue: Date, newValue: Date) {
        // The primary logic for fetching data on date change is now handled by 
        // getServiceStaffList and its subsequent chained calls, initiated from handleDateChange.
        // This function might be simplified or removed if its only purpose was to trigger timeslot fetching.
        // For now, ensure it doesn't make a premature API call.
        // The selectedBookingRequestModel.date is already updated by handleDateChange.
        if var data = selectedBookingRequestModel { // Ensure model consistency if used elsewhere
            if oldValue.toString(outputFormate: dateOutputFormat) != newValue.toString(outputFormate: dateOutputFormat) {
                data.date = newValue
                // REMOVED: getStaffTimeslot(data) - This is now handled by the chain starting in getServiceStaffList
            }
        }
    }
    
    func isSelectedTimeSlot(_ value: VendorTimeslotModel.TimeSlot) -> Bool { value == selectedTimeSlot }
    
    func updateTimeSlot(_ value: VendorTimeslotModel.TimeSlot?, preselect: String? = nil) {
        withAnimation(.bouncy) {
            selectedTimeSlot = value
            
            if let preselect = preselect {
                scrollPosition = preselect
            }
        }
    }

    @Published var isTimeslotLoading: Bool = true
    
    // Convert the `Published` property to a `Binding`
    var isTimeslotLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isTimeslotLoading }, // Getter for the binding
            set: { self.isTimeslotLoading = $0 } // Setter for the binding
        )
    }
    
    func getStaffTimeslot(_ model: BookingRequestModel, preselect: String? = nil, onCompletion: (() -> Void)? = nil) {
//        if let preselect = preselect, selectedTimeSlot?.timeslot == preselect { return }
        
        vendorTimeslotModelList.removeAll()
        
        let dateString = selectedDate.toString(outputFormate: "yyyy-MM-d")
        let isContain = selectedServiceStaffOffDays.contains(dateString)
        
        guard !isContain else { return }
        
        var staffID: String?
        
        if cartModelList.count > 1 {
            staffID = cartModelList.first?.staffID
        } else {
            staffID = model.service[0].selectedStaff?.staffID
        }
        
        guard let staffID = staffID else { return }
        guard let userID = AppState.userModel?.user.id else { return }
        
        var parameters: [String: Any] = [
            "date": model.date.toString(outputFormate: dateOutputFormat),
            "vendor_id": "\(vendor.vendorID)",
            "service": "\(model.service[0].servicesID)",
            "staff": "\(staffID)",
            "after_delete": preselect != nil ? 1 : 0,
            "user_id": userID,
        ]
        
        onApiCall(api.staffTimeslot, parameters: parameters, withLoadingIndicator: false, customLoadingBinding: isTimeslotLoadingBinding) {
            self.timeSlotErrorMessage = nil
            self.vendorTimeslotModelList = $0.data?.timeSlot ?? []
            // isTimeslotLoading is handled by its binding.
            // Always reset calendar loading when timeslots are loaded
            self.isCalendarLoading = false
            
            var data = self.selectedBookingRequestModel
            if let preselect = preselect {
                let firstAvailableSlot = self.vendorTimeslotModelList.first(where: { $0.isDisabled == false && $0.timeslot == preselect })
                
                data?.time = firstAvailableSlot?.timeslot
                self.updateTimeSlot(firstAvailableSlot, preselect: preselect)
            } else {
                let firstAvailableSlot = self.vendorTimeslotModelList.first(where: { $0.isDisabled == false })
               
                data?.time = firstAvailableSlot?.timeslot
                self.updateTimeSlot(firstAvailableSlot, preselect: preselect)
            }
            
            onCompletion?()
//            self.updateBookingRequestModelList(data, isChangeTime: true)
            
            if
                let data = data, !self.isInitialLoad,
                let time = data.time
            {
//                self.isInitialLoad = true
                self.addToCart(model: data, time: time)
            }

        } onFailure: {
            self.timeSlotErrorMessage = $0
            // Ensure loading states are reset if fetching timeslots fails
            self.isCalendarLoading = false
            // isTimeslotLoading is handled by its binding
        }
    }
    
    func updateTimeSlotWithTimeString(_ value: String?) {
        if let value = value {
            let timeSlot = vendorTimeslotModelList.first(where: { $0.timeslot == value })
            updateTimeSlot(timeSlot)
        }
    }
    
    func onBook(value: [BookingRequestModel], completion: @escaping () -> Void, authFail: @escaping () -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else {
            authFail()
            return
        }
        let date: String = value.first?.date.toString(outputFormate: dateOutputFormat) ?? ""
        
        let time = "\(value.first?.time ?? "")"
        
        let parameters: [String: Any] = [
            //            "vendor_id": vendor.vendorID,
//            "date": date,
            "user_id": userID,
            "notes": additionalNote ?? "",
//            "services": value.map { $0.service.servicesID },
//            "time": time,
        ]
        
//        print(parameters)
     
        onApiCall(api.bookings, parameters: parameters) { response in
            if response.success {
                self.updatePageState(.message(config: .init(title: "Success", text: response.message, cancelButtonText: "", okButtonText: "Ok", alertType: .alert, onOk: {
                    completion()
                })))
            }
        }
    }
}

extension BookAppointmentViewModel: Hashable {
    static func == (lhs: BookAppointmentViewModel, rhs: BookAppointmentViewModel) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }
}
