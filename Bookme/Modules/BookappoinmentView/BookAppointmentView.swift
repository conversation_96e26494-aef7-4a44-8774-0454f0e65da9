import Combine
import SwiftUI

struct BookAppointmentView: View {
    @StateObject var viewModel: BookAppointmentViewModel
    @Environment(\.dismiss) var dismiss: DismissAction
    @Namespace private var animation
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @State private var debounceDateChangeTimer: AnyCancellable?
    init(vendor: VendorShortDetailsModel, selectedService: [VendorDetailsModel.Service], bookID: Int?) {
        self._viewModel = StateObject(wrappedValue: BookAppointmentViewModel(vendor: vendor, selectedService: selectedService, bookID: bookID))
    }
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {} content: {
            VStack(spacing: 0) {
                MainScrollBody(backButtonWithTitle: "Book an Appointment") {
                    VStack(alignment: .leading, spacing: 0) {
                        ScrollView(.horizontal, showsIndicators: false) {
                            LazyHStack(spacing: 16) {
                                if viewModel.isServiceStaffListLoading {
                                    ForEach(0 ... 9, id: \.self) { _ in
                                        TopStaffShimmerCell()
                                    }
                                    .shimmerize()
                                    
                                } else {
                                    ForEach(viewModel.serviceStaffModelList, id: \.self) {
                                        TopStaffCell(model: $0, isSelected: viewModel.selectedServiceStaff?.staffID == $0.staffID, onSelect: { staff in
                                            
                                            viewModel.updateSelectedServiceStaff(staff: staff)
                                            
                                        })
                                    }
                                }
                            }
                        }
                        .scrollClipDisabled()
                        .safeAreaPadding(.horizontal)
                        .scrollIndicators(.hidden)
                        .padding(.top)
                        .padding(.bottom, 32.relativeHeight)
                        .visibility(viewModel.cartModelList.count > 1 ? .gone : .visible)
                        
                        Group {
                            if viewModel.isCalendarLoading {
                                CalendarSelectionView(selectedDate: $viewModel.selectedDate, disabledDates: .constant([]), maximumAllowedDate: viewModel.maximumAllowedDate, onMonth: {})
                                    .frame(height: 280.relativeHeight)
                                    .shimmerize()
                            } else {
                                CalendarSelectionView(selectedDate: $viewModel.selectedDate, disabledDates: $viewModel.selectedServiceStaffOffDays, maximumAllowedDate: viewModel.maximumAllowedDate, onMonth: { viewModel.updateBookAppointmentPopUpType(.month) })
                                    .frame(height: 280.relativeHeight)
                            }
                        }
                        .onChange(of: viewModel.selectedDate, initial: true) { oldValue, newValue in
                            guard oldValue != newValue || !viewModel.isInitialLoad else { return }
                                                    
                            // Debounce logic to avoid rapid API calls
                            debounceDateChangeTimer?.cancel()
                            debounceDateChangeTimer = Just(newValue)
                                .delay(for: .milliseconds(300), scheduler: RunLoop.main)
                                .sink { debouncedNewDate in
                                    // Now call the methods after debounce delay
                                    viewModel.handleDateChange(oldValue, debouncedNewDate)
                                }
                        }
                      
                        Divider()
                            .frame(width: getRelativeWidth(370.0), height: getRelativeHeight(1.0),
                                   alignment: .center)
                            .background(ColorConstants.Cyan80035)
                            .padding(.top, getRelativeHeight(30.0))
                            .padding(.leading, getRelativeWidth(14.0))
                            .padding(.trailing, getRelativeWidth(6.0))
                        VStack {
                            VStack(alignment: .leading) {
                                Text("Select Time")
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(16.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(86.0),
                                           height: getRelativeHeight(22.0), alignment: .topLeading)
                                    .padding(.horizontal, getRelativeWidth(10.0))
                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack {
//                                        if viewModel.isTimeslotLoading || viewModel.isCalendarLoading {
                                        if viewModel.isTimeslotLoading{
                                            ForEach(0 ... 9, id: \.self) { _ in
                                                Text("timeslot")
                                                    .font(FontScheme
                                                        .kNunitoSemiBold(size: getRelativeHeight(11.0)))
                                                    .fontWeight(.semibold)
                                                    .padding(.horizontal, getRelativeWidth(14.0))
                                                    .foregroundColor(
                                                        ColorConstants.Cyan8007f)
                                                    .multilineTextAlignment(.center)
                                                    .frame(width: 80.0.relativeWidth, height: getRelativeHeight(29.0), alignment: .center)
                                                    .background(ColorConstants.Cyan80035.clipShape(.capsule))
                                                    .overlay(Capsule().stroke(ColorConstants.Cyan800, lineWidth: 1))
                                                    .clipShape(.capsule)
                                            }
                                            .shimmerize()
                                        } else {
                                            if let timeSlotErrorMessage = viewModel.timeSlotErrorMessage {
                                                Text(timeSlotErrorMessage)
                                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                                    .fontWeight(.semibold)
                                                    .foregroundColor(ColorConstants.Black50)
                                                    .multilineTextAlignment(.leading)
                                                    .padding(.horizontal, getRelativeWidth(10.0))
                                            } else {
                                                if viewModel.vendorTimeslotModelList.isEmpty {
                                                    CustomPlaceholder(placeholderType: .emptyTimeSlot, title: "There is no availability for this date", subTitle: "There is no time slot available for this date with the selected staff")
                                                        .frame(width: UIScreen.main.bounds.width - 32.relativeWidth)
                                                        .onAppear {
                                                            viewModel.cartModelList.removeAll()
                                                        }
                                               
                                                } else {
                                                    ForEach(Array(viewModel.vendorTimeslotModelList.enumerated()), id: \.element.id) { index, model in
                                                            
                                                        Button(action: {
                                                            let bookingListCount = viewModel.cartModelList.count
                                                            let endIndex = viewModel.vendorTimeslotModelList.endIndex
                                                           
                                                            if bookingListCount > 1, index >= (endIndex - (bookingListCount - 1)) {
                                                                viewModel.updatePageState(.failure(error: "Please select a previous time"))
                                                            } else {
                                                                viewModel.updateTimeSlot(model)
                                                            }
                                                            
                                                        }, label: {
                                                            Text(model.timeslot)
                                                                .font(FontScheme
                                                                    .kNunitoSemiBold(size: getRelativeHeight(11.0)))
                                                                .fontWeight(.semibold)
                                                                .padding(.horizontal, getRelativeWidth(14.0))
                                                                .foregroundColor(
                                                                    model.isDisabled ? ColorConstants.Bluegray102 :
                                                                        viewModel.isSelectedTimeSlot(model) ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                                                                .multilineTextAlignment(.center)
                                                                .frame(width: 80.0.relativeWidth,
                                                                       height: getRelativeHeight(29.0),
                                                                       
                                                                       alignment: .center)
                                                                .if(model.isDisabled, transform: {
                                                                    $0.overlay {
                                                                        Divider()
                                                                                
                                                                            .rotationEffect(.degrees(70))
                                                                            .frame(width: 2, height: 80.0.relativeWidth)
                                                                    }
                                                                })
                                                                .background(
                                                                    model.isDisabled ?
                                                                        ColorConstants.Gray5003f1
                                                                        .clipShape(.capsule) :
                                                                        viewModel.isSelectedTimeSlot(model) ? ColorConstants.Cyan800.clipShape(.capsule) : ColorConstants.Cyan80035.clipShape(.capsule))
                                                                .overlay(Capsule().stroke(
                                                                    model.isDisabled ? ColorConstants.Bluegray101 :
                                                                        ColorConstants.Cyan800,
                                                                    lineWidth: 1))
                                                                .clipShape(.capsule)
                                                                    
                                                        })
                                                        .id(model.timeslot) // Assign a unique ID to each item
                                                        .if(viewModel.isSelectedTimeSlot(model), transform: {
                                                            $0.matchedGeometryEffect(id: "time.slot", in: animation)
                                                        })
                                                        .disabled(model.isDisabled)
                                                        .padding(2)
                                                    }
                                                    
                                                    .onChange(of: viewModel.selectedTimeSlot) { oldValue, newValue in
                                                        guard oldValue != newValue else { return }
                                                        var data = viewModel.selectedBookingRequestModel
                                                        data?.time = newValue?.timeslot
                                                        
//                                                        viewModel.updateBookingRequestModelList(data, isChangeTime: true)
//                                                        viewModel.selectedCartModel = viewModel.cartModelList.first
                                                        if
                                                            let selectedCartModel = viewModel.selectedCartModel,
                                                            let selectedTime = newValue?.timeslot
                                                        {
                                                            let selectedDate: String = viewModel.selectedDate.toString(outputFormate: dateOutputFormat)
//                                                            let cartDate: String = selectedCartModel.formattedDateString ?? ""
//                                                            let cartTime: String = selectedCartModel.startTime
                                                            
//                                                            if selectedDate != cartDate || selectedTime != cartTime{
                                                            if
                                                                let selectedServiceStaffID = viewModel.selectedServiceStaff?.staffID,
                                                                let selectedCartStaffID = viewModel.selectedCartModel?.staffID,
                                                                selectedCartStaffID != selectedServiceStaffID
                                                            {
                                                                self.viewModel.updateToCart(model: selectedCartModel, updateType: .dateTimeAndStaff(date: selectedDate, time: selectedTime, staffID: selectedServiceStaffID))
                                                            } else {
                                                                self.viewModel.updateToCart(model: selectedCartModel, updateType: .dateTime(date: selectedDate, time: selectedTime))
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                .scrollPosition(id: $viewModel.scrollPosition, anchor: .center)
                                .scrollDisabled(viewModel.vendorTimeslotModelList.isEmpty)
                                .scrollClipDisabled()
                                .padding(.top, getRelativeHeight(12.0))
                                .padding(.leading, getRelativeWidth(7.0))
                            }

                            .padding(.leading, getRelativeWidth(6.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                            Divider()
                                .frame(width: getRelativeWidth(370.0),
                                       height: getRelativeHeight(1.0), alignment: .leading)
                                .background(ColorConstants.Cyan80035)
                                .padding(.top, getRelativeHeight(30.0))
                                .padding(.trailing, getRelativeWidth(10.0))
                        }

                        .padding(.top, getRelativeHeight(23.0))
                        
                        VStack(alignment: .leading, spacing: 8) {
//                            if viewModel.isCartLoading || viewModel.isTimeslotLoading || viewModel.isCalendarLoading {
                            if viewModel.isCartLoading {
                                VStack(alignment: .leading, spacing: 16) {
                                    ForEach(0 ... 1, id: \.self) { index in
                                       
                                        CartItemCellShimmerView()
                                            
                                        Divider()
                                            .frame(height: getRelativeHeight(1.0), alignment: .center)
                                            .background(.black.opacity(0.21))
                                            .padding(.horizontal, getRelativeWidth(4.0))
                                            .padding(.vertical)
                                            .overlay(content: {
                                                HStack(spacing: 6.0.relativeWidth) {
                                                    Image(.timeFill)
                                                        .resizable()
                                                        .frame(width: 15, height: 15)
                                                    Text("30 minutes")
                                                        .font(Font.custom("Nunito", size: 9.relativeFontSize))
                                                        .foregroundColor(.black.opacity(0.5))
                                                }
                                                .padding(.horizontal)
                                                .background(ColorConstants.WhiteA700)
                                            })
                                            .visibility(index < 1 ? .visible : .gone)
                                        
                                    }.shimmerize()
                                }
                               
                            } else {
                                VStack(alignment: .leading, spacing: 16) {
                                    ForEach(Array(viewModel.cartModelList.enumerated()), id: \.element) { index, model in
                                        
                                        if
                                            let delay = model.delay1,
                                            let delayInMinutes = Int(delay),
                                            delayInMinutes > 0,
                                            (index + 1).isMultiple(of: 2)
                                        {
                                            Divider()
                                                .frame(height: getRelativeHeight(1.0), alignment: .center)
                                                .background(.black.opacity(0.21))
                                                .padding(.horizontal, getRelativeWidth(4.0))
                                                .overlay(content: {
                                                    HStack(spacing: 6.0.relativeWidth) {
                                                        Image(.timeFill)
                                                            .resizable()
                                                            .frame(width: 15, height: 15)
                                                        Text("\(delayInMinutes) minutes")
                                                            .font(Font.custom("Nunito", size: 9.relativeFontSize))
                                                            .foregroundColor(.black.opacity(0.5))
                                                    }
                                                    .padding(.horizontal)
                                                    .background(ColorConstants.WhiteA700)
                                                })
                                        }
                                        
                                        CartItemCellView(model: model, onDelete: viewModel.cartModelList.count > 1 ? viewModel.deleteCartItem : nil, onStaffSelect: { model in
                                            
                                            let routesType: RoutesType = routerManager.mapRouterWithTab(appState: appState)
                                            let date: String = model.formattedDateString ?? ""
                                            let time: String = model.startTime
                                            
                                            let staffModelRequest: StaffModelRequest = .init(serviceID: model.serviceID, vendorID: String(model.vendorID), date: date, time: time)
                                            let route: Route = .shopDetailsStaffModel(staffModelRequest: staffModelRequest, selectedStaffID: String(model.staffID), onSelect: .init(onBack: { staff in
                                                
                                                routerManager.goBack(where: routesType)
                                                
                                                viewModel.onChangeStaffFromService(model: model, staff: staff, updateLocalStaff: index == 0 && viewModel.cartModelList.count == 1, refreshTimeSlot: false, preselect: viewModel.selectedTimeSlot?.timeslot)
                                            }))
                                    
                                            routerManager.push(to: route, where: routesType)
                                            
                                        })
                                    }
                                }
                            }
                        }
                       
//                        .animation(.bouncy, value: viewModel.cartModelList)
                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                                   bottomRight: 25.0)
                                .fill(ColorConstants.WhiteA700))
                        .padding(.top, getRelativeHeight(29.0))
                        .padding(.horizontal, getRelativeWidth(10.0))
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Button(action: {
                                let selectedServiceIDs: [Int] = viewModel.cartModelList.map { $0.serviceID }
                                
                                routerManager.push(to: .shopDetailsService(vendorID: Int(viewModel.vendor.vendorID), selectedServiceIDs: selectedServiceIDs, onSelect: .init(onBack: { model in
                                    
                                    routerManager.goBack(where: routerManager.mapRouterWithTab(appState: appState))
                                    
                                    if
                                        let time = viewModel.cartModelList.last?.endTime,
                                        var data = viewModel.selectedBookingRequestModel
                                    
                                    {
                                        data.service[0] = model
                                        data.service[0].selectedStaff = viewModel.serviceStaffModelList.first
                                        viewModel.addToCart(model: data, time: time)
                                    }
                                })), where: routerManager.mapRouterWithTab(appState: appState))
                                
                            }, label: {
                                HStack {
                                    Image("img_addround")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: getRelativeWidth(20.0),
                                               height: getRelativeWidth(20.0), alignment: .center)
                                           
                                        .clipped()
                                    Text("Add another service")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(126.0),
                                               height: getRelativeHeight(18.0), alignment: .topLeading)
                                }
                            })
                            .frame(width: getRelativeWidth(151.0), height: getRelativeHeight(18.0),
                                   alignment: .center)
                            .padding(.leading, getRelativeWidth(6.0))
                            
                            Button(action: {
                                withAnimation(.bouncy) {
                                    viewModel.updateAdditionalNote(.note)
                                }
                               
                            }, label: {
                                HStack {
                                    Image("img_chatalt2ligh")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: getRelativeWidth(20.0),
                                               height: getRelativeWidth(20.0), alignment: .center)
                                           
                                        .clipped()
                                    Text("Leave a note (optional)")
                                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(11.0)))
                                        .fontWeight(.regular)
                                        .foregroundColor(ColorConstants.Cyan8007f1)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(111.0),
                                               height: getRelativeHeight(16.0), alignment: .topLeading)
                                }
                            })
                            .frame(width: getRelativeWidth(133.0), height: getRelativeHeight(16.0),
                                   alignment: .leading)
                            .padding(.vertical, getRelativeHeight(7.0))
                            .padding(.leading, getRelativeWidth(6.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                        }
                        .visibility(viewModel.cartModelList.isEmpty ? .gone : .visible)
                        .frame(width: getRelativeWidth(157.0), height: getRelativeHeight(47.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(21.0))
                    }
                    .padding(.top, 16.relativeHeight)
                    .background(ColorConstants.WhiteA700)
                }
                .background(ColorConstants.WhiteA700)
            }
            .overlay(alignment: .bottom, content: {
                Group {
                    switch viewModel.bookAppointmentPopUpType {
                    case .month:

                        SlideUpAnimationContainerView {
                            viewModel.updateBookAppointmentPopUpType(nil)
                        } content: {
                            MonthPopUpView(
                                selectedMonth: Calendar.current.component(.month, from: self.viewModel.selectedDate) - 1,
                                selectedDate: viewModel.selectedDate,
                                staffOffDays: viewModel.selectedServiceStaffOffDays, // Pass staff off-days
                                vendorMaximumAllowedDate: viewModel.maximumAllowedDate, // Pass vendor's schedule limit
                                onSubmit: { date in
                                    if let date = date {
                                        // Check if the selected month in popup is entirely disabled before changing selectedDate
                                        // This logic is now inside MonthPopUpView for button disabling,
                                        // and the onSubmit from MonthPopUpView should ideally only give a valid month's date.
                                        // However, the original onSubmit logic for MonthPopUpView was to change the selectedDate
                                        // to the first of the chosen month. We need to ensure this new date is valid.
                                        
                                        // Let's refine the onSubmit logic here or ensure MonthPopUpView's onSubmit
                                        // only calls back if a *valid* month (not fully disabled) was chosen and a valid date can be formed.
                                        // For now, assume MonthPopUpView's submit button itself would be disabled if no valid selection can be made.
                                        // The primary goal here is to change the month view in the main calendar.
                                        
                                        // If the selected month from popup is valid, update the main calendar's selectedDate.
                                        // The existing onChange handler for viewModel.selectedDate will then fetch data.
                                        viewModel.selectedDate = date
                                    }
                                },
                                onClose: { viewModel.updateBookAppointmentPopUpType(nil) }
                            )
                            .padding(.bottom, -AppConstants.tabBarHeight)
                        }
                        
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                            
                    case .note:
                        
                        SlideUpAnimationContainerView {
                            Utilities.dismissKeyboard()
                            viewModel.updateAdditionalNote(nil)
                        } content: {
                            VStack(spacing: 30.relativeHeight) {
                                VStack(spacing: 16.0.relativeHeight) {
                                    VStack(spacing: 0) {
                                        HStack {
                                            Text("Add Note")
                                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(16.0)))
                                                .fontWeight(.medium)
                                                .foregroundColor(ColorConstants.Black900)
                                                .padding()
                                            Spacer()

                                            Button(action: {
                                                Utilities.dismissKeyboard()
                                                viewModel.updateAdditionalNote(nil) }) {
                                                Image("img_closeroundlig")
                                                    .resizable()
                                                    .scaledToFit()
                                                    .frame(width: getRelativeWidth(20.0),
                                                           height: getRelativeWidth(20.0), alignment: .center)

                                                    .padding(.horizontal, getRelativeWidth(6.0))
                                            }
                                        }

                                        Divider()
                                            .frame(width: getRelativeWidth(349.0),
                                                   height: getRelativeHeight(1.0), alignment: .center)
                                            .background(ColorConstants.Cyan8003f)
                                            .padding(.trailing, getRelativeWidth(4.0))
                                    }
                                    // generateMonths is redundant. You can get the month names using Calendar.monthSymbols
                                        
                                    VStack(alignment: .leading, spacing: 0) {
                                        Text("Write your note")
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Cyan800)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(125.0), height: getRelativeHeight(20.0),
                                                   alignment: .topLeading)
                                            .padding(.trailing)
                                        VStack {
                                            TextField("What did you like to add?", text: $viewModel.additionalNoteText, axis: .vertical)
                                                .lineLimit(5 ... 5)
                                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                                .fontWeight(.regular)
                                                .foregroundColor(ColorConstants.Cyan800)
                                                .minimumScaleFactor(0.5)
                                                .multilineTextAlignment(.leading)
                                                .padding(.vertical, getRelativeHeight(14.0))
                                                .padding(.horizontal, getRelativeWidth(13.0))
                                        }
                                            
                                        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                                bottomRight: 13.0)
                                                .stroke(ColorConstants.Cyan800,
                                                        lineWidth: 1))
                                        .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                                   bottomRight: 13.0)
                                                .fill(ColorConstants.WhiteA7003f))
                                        .padding(.top, getRelativeHeight(8.0))
                                    }
                                        
                                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                               bottomRight: 13.0).fill(.clear))
                                    .padding(.top, getRelativeHeight(17.0))
                                        
                                }.padding(.bottom, 6)

                                Button(action: {
                                    Utilities.dismissKeyboard()
                                    viewModel.updateAdditionalNote(nil, onSubmit: true)
                                }, label: {
                                    HStack(spacing: 0) {
                                        Text("Submit")
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                            .fontWeight(.bold)
                                            .padding(.horizontal, getRelativeWidth(30.0))
                                            .padding(.vertical, getRelativeHeight(7.0))
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.center)
                                            .frame(width: getRelativeWidth(129.0),
                                                   height: getRelativeHeight(35.0), alignment: .center)
                                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                                       bottomLeft: 17.5,
                                                                       bottomRight: 17.5)
                                                    .fill(ColorConstants.Cyan800))
                                            .padding(.bottom, AppConstants.tabBarHeight.relativeHeight + 16.0.relativeHeight)
                                            .padding(.horizontal, getRelativeWidth(21.0))
                                    }
                                })
                            }
                            .padding(.horizontal)
                            .frame(maxWidth: .infinity)
                            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                                .fill(ColorConstants.WhiteA700))
                            .clipped()
                            .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
                            .padding(.bottom, -AppConstants.tabBarHeight)
                        }
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                    default:
                        EmptyView()
                    }
                }
                .animation(.bouncy, value: viewModel.bookAppointmentPopUpType)
            })
     
            .safeAreaInset(edge: .bottom) {
                CustomBottomNavigationButton(subTotal: viewModel.subTotal, numberOfServices: viewModel.cartModelList.count, disable: viewModel.isBookingDisabled) {
                    let type = routerManager.mapRouterWithTab(appState: appState)
                    routerManager.push(to: .checkoutView(viewModel: viewModel), where: type)
                    
                    let eventTitle = "You have booked an Appointment at \(viewModel.vendor.salonName)"
                    let eventNote = "For services: \(viewModel.cartModelList.map(\.serviceName).uniqued().formatted(.list(type: .and, width: .short)))"
                    let eventStartDate = viewModel.selectedDate.settingTime(from: viewModel.cartModelList.first?.startTime ?? "")
                    let eventEndDate = viewModel.selectedDate.settingTime(from: viewModel.cartModelList.last?.endTime ?? "")
                    let alertOffset: TimeInterval = -2 * 60 * 60
                    if
                        let eventStartDate = eventStartDate,
                        let eventEndDate = eventEndDate
                    {
                        appState.event = .init(title: eventTitle, startDate: eventStartDate, endDate: eventEndDate, note: eventNote, location: viewModel.vendor.address, alertOffset: alertOffset)
                    }
                }
            }
            .ignoresSafeArea(.container, edges: .bottom)
        }
    }
}

public func monthNumberIntoDate(_ month: Int, selectedDate: Date) -> Date? {
    let gregorianCalendar = Calendar(identifier: .gregorian)
    let selectedDateMonth: Int = selectedDate.get(.month)
    let addingValue: Int = month - selectedDateMonth
    guard let newDate = gregorianCalendar.date(byAdding: .month, value: addingValue, to: selectedDate) else { return nil }
    return newDate
}

extension Date {
    /// Returns a new `Date` with the time from a string (e.g., "10:00 AM") applied to the existing date.
    /// - Parameter timeString: A string representing the time (e.g., "10:00 AM").
    /// - Returns: A new `Date` with the updated time, or `nil` if the time string is invalid.
    func settingTime(from timeString: String) -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "hh:mm a"
        
        guard let time = formatter.date(from: timeString) else {
            return nil // Invalid time format
        }
        
        let calendar = Calendar.current
        var dateComponents = calendar.dateComponents([.year, .month, .day], from: self)
        let timeComponents = calendar.dateComponents([.hour, .minute], from: time)
        
        dateComponents.hour = timeComponents.hour
        dateComponents.minute = timeComponents.minute
        
        return calendar.date(from: dateComponents)
    }
}

extension Array where Element == String {
    /// Converts an array of date strings in the format "yyyy-MM-d" to an array of Date objects.
    /// Returns an array of Date objects. Invalid date strings are skipped.
    func toDateArray(dateFormat: String = "yyyy-MM-d") -> [Date] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = dateFormat
        
        return compactMap { dateString in
            dateFormatter.date(from: dateString)
        }
    }
}
