//
//  ChangePasswordView.swift
//  Bookme
//
//  Created by Apple on 21/08/2024.
//

import SwiftUI

struct ChangePasswordView: View {
    let routesType: RoutesType
    @StateObject var authViewModel: ForgotPasswordViewModel
    @Environment(\.dismiss) var dismiss
    @Environment(RouterManager.self) var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    
    @FocusState private var isOTPFieldFocused: Bool
    
    var body: some View {
        GeometryReader(content: { geometry in
            SuperView(pageState: $authViewModel.pageState) {
                MainScrollBody(invertColor: false, backButtonWithTitle: "") {
                    VStack {
                        VStack(spacing: 16.0.relativeHeight) {
                            Text("Create New Password")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .multilineTextAlignment(.leading)
                                .padding(.horizontal, getRelativeWidth(42.0))
//                            Text("Enter the verification code we just sent on your email address")
//                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
//                                .fontWeight(.regular)
//                                .foregroundColor(ColorConstants.Gray400)
//                                .multilineTextAlignment(.center)
//                                .padding(.horizontal, getRelativeWidth(42.0))
                        }
                        
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        CustomTextField(title: "New Password", placeholder: StringConstants.kMsg, text: self.$authViewModel.passwordText, invalidText: self.authViewModel.passwordErrorText, isSecure: true)
                            .keyboardType(.default)
                            .textContentType(.oneTimeCode)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.authViewModel.passwordText) { _, newValue in
                                withAnimation(.bouncy) {
                                    self.authViewModel.passwordErrorText = newValue.isEmptyOrWhitespace
                                        ? "Password field can't be empty"
                                        : nil
                                }
                            }
                        
                        CustomTextField(title: "Confirm new password", placeholder: StringConstants.kMsg, text: self.$authViewModel.confirmPasswordText, invalidText: self.authViewModel.confirmPasswordErrorText, isSecure: true)
                            .keyboardType(.default)
                            .textContentType(.oneTimeCode)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.authViewModel.confirmPasswordText) { _, newValue in
                                withAnimation(.bouncy) {
                                    self.authViewModel.confirmPasswordErrorText = newValue.isEmptyOrWhitespace
                                        ? "Confirm Password field can't be empty"
                                        : nil
                                }
                            }
                        
                        VStack {
                            Button(action: {
                                authViewModel.onResetPassword { _ in
                                    routerManager.popToRout(to: "signIn", where: routesType)
                                    routerManager.push(to: .changePasswordSuccess(type: routesType, fromForgotPassword: true), where: routesType)
                                }
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("Reset Password")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .multilineTextAlignment(.center)
//                                        .frame(width: getRelativeWidth(320.0),
//                                               height: getRelativeHeight(50.0), alignment: .center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            }).disableWithOpacity(authViewModel.isResetPasswordDisabled)
                        }
                        
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        Spacer()
                    }
                    
                    .padding(.bottom, 32.0.relativeHeight)
                    .frame(height: geometry.size.height)
                }
                .background(ColorConstants.bgGradient)
            }
        })
    }
}

#Preview {
    NavigationStack {
        ChangePasswordView(routesType: .myAccountRoute, authViewModel: ForgotPasswordViewModel()).attachAllEnvironmentObjects()
    }
}
