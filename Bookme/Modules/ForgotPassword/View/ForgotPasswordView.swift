//
//  ForgotPasswordView.swift
//  Bookme
//
//  Created by Apple on 21/08/2024.
//

import SwiftUI





struct ForgotPasswordView: View {
    let routesType: RoutesType
    @StateObject private var authViewModel = ForgotPasswordViewModel()
    @Environment(\.dismiss) var dismiss
    @Environment(RouterManager.self) var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    var body: some View {
        
        GeometryReader(content: { geometry in
            SuperView(pageState: $authViewModel.pageState) {
                MainScrollBody(invertColor: false,backButtonWithTitle: "") {
                   
                    VStack {
                        VStack(spacing:16.0.relativeHeight) {
                            Text("Forgot Password")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.WhiteA700)
                                
                                .multilineTextAlignment(.center)
                               
                                .padding(.horizontal, getRelativeWidth(42.0))
                            Text("Don’t worry ! it occurs. Please enter the email address or mobile number linked with your account.")
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Gray400)
                               
                                .multilineTextAlignment(.center)
                                
                                
                                .padding(.horizontal, getRelativeWidth(42.0))
                                
                            
                            
                            
                            
                        }
                        
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        
                        CustomTextField(title: "Email or Mobile Number", placeholder: "Email or Mobile Number", text: self.$authViewModel.emailText, invalidText: self.authViewModel.emailErrorText)
                            .keyboardType(.emailAddress)
                  
                            .textInputAutocapitalization(.never)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
                            .onChange(of: self.authViewModel.emailText) { _, newValue in
                                withAnimation(.bouncy) {
                                    authViewModel.validateInput(newValue)
                                
                                }
                            }
                        
                        
                        
                        
                        
                        VStack {
                            Button(action: {
                                authViewModel.onSendCode { success in
                                    routerManager.push(to: .forgotPasswordOtp(type: routesType, isEmail: self.authViewModel.emailText.isEmail, authViewModel), where: routesType)
                                }
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("Send Code")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .multilineTextAlignment(.center)
//                                        .frame(width: getRelativeWidth(320.0),
//                                               height: getRelativeHeight(50.0), alignment: .center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                            .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            }).disableWithOpacity(authViewModel.isSignInDisabled)
                            
                            
                        }
                        
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        
                        Spacer()
                        
                        VStack(alignment: .leading, spacing: 0) {
                            HStack{
                                Text("Remember Password?")
                                Button {
                                    dismiss()
                                } label: {
                                    Text("Login")
                                        .foregroundColor(ColorConstants.Cyan800)
                                }
                                
                            }
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(186.0), height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.horizontal, getRelativeWidth(72.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(18.0),
                               alignment: .leading)
                        .padding(.vertical, getRelativeHeight(53.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        
                        
                    }
                    
                        .padding(.bottom,32.0.relativeHeight)
                        .frame(height: geometry.size.height)
                      
                       
                    
                }
                .background(ColorConstants.bgGradient)
                
                
            }
        })
        
        
       
    }
}


#Preview {
    NavigationStack{
        ForgotPasswordView(routesType: .myAccountRoute).attachAllEnvironmentObjects()
    }
}



extension String {
    var isEmail: Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: self)
    }
}
