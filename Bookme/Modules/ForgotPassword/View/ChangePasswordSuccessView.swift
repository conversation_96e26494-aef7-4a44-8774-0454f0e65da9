//
//  ChangePasswordSuccessView.swift
//  Bookme
//
//  Created by Apple on 21/08/2024.
//

import SwiftUI

struct ChangePasswordSuccessView: View {
    
    let routesType: RoutesType
    let fromForgotPassword: Bool
 
    @Environment(RouterManager.self) var routerManager: RouterManager
    @Environment(\.dismiss) var dismiss
    @EnvironmentObject private var appState: AppState
    var body: some View {
        GeometryReader(content: { geometry in
          
                MainScrollBody(invertColor: false, backButtonWithTitle: "") {
                    VStack {
                        VStack(spacing: 16.0.relativeHeight) {
                            Image(.verifiedCheckmark)
                                .padding(.bottom, 32.0.relativeHeight)
                            
                            Text("Password Changed")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, getRelativeWidth(42.0))
                            Text("Your password has been changed successfully")
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Gray400)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, getRelativeWidth(42.0))
                        }
                        
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        VStack {
                            Button(action: {
                                
                                routerManager.goBack(where: routesType)
                                
                                
                            }, label: {
                                HStack(spacing: 0) {
                                    Text(fromForgotPassword ? "Back to Login" : "Done")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                
                                        .multilineTextAlignment(.center)
//                                        .frame(width: getRelativeWidth(320.0),
//                                               height: getRelativeHeight(50.0), alignment: .center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            })
                        }
                        
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                    }
                    .padding(.top, 64.0.relativeHeight)
                    .padding(.bottom, 32.0.relativeHeight)
                    .frame(height: geometry.size.height, alignment: .top)
                }
                .background(ColorConstants.bgGradient)
            
        })
    }
}

#Preview {
    NavigationStack {
        ChangePasswordSuccessView(routesType: .myAccountRoute, fromForgotPassword: false).attachAllEnvironmentObjects()
    }
}
