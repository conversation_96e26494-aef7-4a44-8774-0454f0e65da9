//
//  OtpView.swift
//  Bookme
//
//  Created by Apple on 21/08/2024.
//

import SwiftUI

struct ForgotPasswordOtpView: View {
    let routesType: RoutesType
    let isEmail:Bool
    @StateObject var authViewModel: ForgotPasswordViewModel
    @Environment(\.dismiss) var dismiss
    @Environment(RouterManager.self) var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    
    @FocusState private var isOTPFieldFocused: Bool
    
    var body: some View {
        GeometryReader(content: { geometry in
            SuperView(pageState: $authViewModel.pageState) {
                MainScrollBody(invertColor: false, backButtonWithTitle: "") {
                    VStack {
                        VStack(spacing: 16.0.relativeHeight) {
                            Text("OTP Verification")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .multilineTextAlignment(.leading)
                                .padding(.horizontal, getRelativeWidth(42.0))
                            Text("Enter the verification code we just sent on your \(isEmail ? "Email" : "Mobile Number")")
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Gray400)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, getRelativeWidth(42.0))
                        }
                        
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        OTPFieldView(numberOfFields: authViewModel.numberOfFieldsInOTP, otp: $authViewModel.otp, isError: authViewModel.otpErrorMessage != nil)
                            .environment(\.layoutDirection, .leftToRight) // 👈 Forces LTR even in Arabic
                            .padding(.vertical)
                            .onChange(of: authViewModel.otp) { _, newValue in
                                if newValue.count == authViewModel.numberOfFieldsInOTP {
                                    // Verify OTP
                                }
                            }
                            .focused($isOTPFieldFocused)
                        
                        
                        Text(authViewModel.otpErrorMessage ?? "")
                            .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.semibold)
                            .foregroundColor(Color(hex: "#FF4848"))
                            .padding(.horizontal, getRelativeWidth(22.0))
                            .visibility(authViewModel.otpErrorMessage != nil ? .visible : .gone)
                            .frame(maxWidth: .infinity)
                        
                        VStack {
                            Button(action: {
                                authViewModel.onVerifyCode { _ in
                                    routerManager.goBack(where: routesType)
                                    routerManager.push(to: .changesPassword(type: routesType, authViewModel), where: routesType)
                                }
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("Verify")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .multilineTextAlignment(.center)
//                                        .frame(width: getRelativeWidth(320.0),
//                                               height: getRelativeHeight(50.0), alignment: .center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            }).disableWithOpacity(authViewModel.isVerifyDisabled)
                        }
                        
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        Spacer()
                        
                        VStack(alignment: .leading, spacing: 0) {
                            HStack {
                                Text("Didn’t received code?")
                                if authViewModel.timerExpired {
                                    Button {
                                        authViewModel.onResendCode { _ in }
                                    } label: {
                                        Text("Resend")
                                            .foregroundColor(ColorConstants.Cyan800)
                                    }
                                } else {
                                    Text(authViewModel.timeStr)
                                        .fontWeight(.semibold)
                                        .contentTransition(.numericText())
                                        .animation(.bouncy, value: authViewModel.timeStr)
                                        .onReceive(authViewModel.timer) { _ in
                                            authViewModel.countDownString()
                                        }
                                }
                            }
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(186.0), height: getRelativeHeight(18.0),
                                   alignment: .center)
                            .padding(.horizontal, getRelativeWidth(72.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(18.0),
                               alignment: .leading)
                        .padding(.vertical, getRelativeHeight(53.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                    }
                    .addDoneToKeyboard()
                    .padding(.bottom, 32.0.relativeHeight)
                    .frame(height: geometry.size.height)
                }
                .background(ColorConstants.bgGradient)
            }
        })
    }
}

#Preview {
    NavigationStack {
        ForgotPasswordOtpView(routesType: .myAccountRoute, isEmail: true, authViewModel: ForgotPasswordViewModel()).attachAllEnvironmentObjects()
    }
}
