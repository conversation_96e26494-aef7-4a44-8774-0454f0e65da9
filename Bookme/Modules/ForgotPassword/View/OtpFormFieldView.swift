//
//  OtpFormFieldView.swift
//  Bookme
//
//  Created by Apple on 21/08/2024.
//

import Combine
import SwiftUI

// A SwiftUI view for entering OTP (One-Time Password).
struct OTPFieldView: View {
    @FocusState private var pinFocusState: FocusPin?
    @Binding private var otp: String
    @State private var pins: [String]
    
    var numberOfFields: Int
    let isError: Bool
    
    enum FocusPin: Hashable {
        case pin(Int)
    }
    
    init(numberOfFields: Int, otp: Binding<String>, isError: Bool) {
        self.numberOfFields = numberOfFields
        self.isError = isError
        self._otp = otp
        self._pins = State(initialValue: Array(repeating: "", count: numberOfFields))
    }
    
    var body: some View {
        let size = UIScreen.main.bounds.width / CGFloat(numberOfFields + 3)
        HStack(spacing: size / 3) {
            ForEach(0 ..< numberOfFields, id: \.self) { index in
                TextField("", text: $pins[index])
                    .modifier(OtpModifier(numberOfFields: numberOfFields, isError: isError, pin: $pins[index]))
                    
                    .foregroundColor(.white)
                    .textContentType(.oneTimeCode) // Allows auto-fill OTP
                    .focused($pinFocusState, equals: FocusPin.pin(index))
                    .onTapGesture {
                        // Set focus to the current field when tapped
                        pinFocusState = FocusPin.pin(index)
                    }
                    .onChange(of: pins[index]) { _, newValue in
                        handleOTPInput(index: index, newValue: newValue)
                    }
            }
        }
        .onAppear {
            // Initialize pins based on the OTP string
            updatePinsFromOTP()
            
            pinFocusState = FocusPin.pin(0)
        }
    }
    
    private func handleOTPInput(index: Int, newValue: String) {
        if newValue.count == numberOfFields, let intValue = Int(newValue) {
            // If the full OTP is auto-filled, set it directly
            otp = newValue
            updatePinsFromOTP()
            pinFocusState = nil // Remove focus after auto-fill
        } else if newValue.count == 1 {
            // Move to the next field if a single digit is entered
            if index < numberOfFields - 1 {
                pinFocusState = FocusPin.pin(index + 1)
            }
        } else if newValue.isEmpty, index > 0 {
            // Move back when deleting
            pinFocusState = FocusPin.pin(index - 1)
        }
        updateOTPString()
    }
    
    
    private func updatePinsFromOTP() {
        let otpArray = Array(otp.prefix(numberOfFields))
        for (index, char) in otpArray.enumerated() {
            pins[index] = String(char)
        }
    }
    
    private func updateOTPString() {
        otp = pins.joined()
    }
}

struct OtpModifier: ViewModifier {
    var numberOfFields: Int
    let isError: Bool
    @Binding var pin: String
    
    var textLimit = 1
    
    func limitText(_ upper: Int) {
        if pin.count > upper {
            pin = String(pin.prefix(upper))
        }
    }
    
    func body(content: Content) -> some View {
        let size = UIScreen.main.bounds.width / CGFloat(numberOfFields + 2)
        
        content
            .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(18.0)).weight(.black))
            .multilineTextAlignment(.center)
            .keyboardType(.numberPad)
            .textContentType(.oneTimeCode)
            .onReceive(Just(pin)) { _ in limitText(textLimit) }
            .frame(width: size, height: size)
            .font(.system(size: 14))
            .tint(Color(hex: "#008F96"))
            .background(
                RoundedRectangle(cornerRadius: 5)
                    .stroke(Color(hex: isError ? "#FF4848" : "#008F96"), lineWidth: 2)
            )
    }
}

struct OTPFieldView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("VERIFICATION CODE")
                .foregroundColor(Color.gray)
                .font(.system(size: 12))
            OTPFieldView(numberOfFields: 4, otp: .constant("54321"), isError: false)
                .previewLayout(.sizeThatFits)
        }
    }
}
