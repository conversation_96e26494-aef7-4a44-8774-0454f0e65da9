//
//  ForgotPasswordViewModel.swift
//  Bookme
//
//  Created by Apple on 21/08/2024.
//

import Foundation
import UIKit

class ForgotPasswordViewModel: SuperViewModel {
    @Published var emailText: String = .init()
    @Published var passwordText: String = .init()
    @Published var confirmPasswordText: String = .init()
    
    @Published var emailErrorText: String?
    @Published var passwordErrorText: String?
    @Published var confirmPasswordErrorText: String?
    
    @Published var timerExpired = false
    @Published var timeStr = ""
    @Published var timeRemaining = AppConstants.COUNTDOWN_TIMER_LENGTH
    
    @Published var otpErrorMessage: String?
    
    var timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    var userID: Int?
    
    @Published var otp: String = ""
    let numberOfFieldsInOTP = 6
    
    var isSignInDisabled: Bool { (emailText.isEmptyOrWhitespace) || emailErrorText != nil }
   
    var isResetPasswordDisabled: Bool { (passwordText.isEmptyOrWhitespace || confirmPasswordText.isEmptyOrWhitespace) || passwordErrorText != nil || confirmPasswordErrorText != nil }
    
    var isVerifyDisabled: Bool { otp.count < numberOfFieldsInOTP }
    
    // MARK: functions

    func startTimer() {
        timerExpired = false
        timeRemaining = AppConstants.COUNTDOWN_TIMER_LENGTH
        timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    }
        
    func stopTimer() {
        timerExpired = true
        timer.upstream.connect().cancel()
    }
    
    // Validation logic for email and phone number
    func validateInput(_ value: String) {
        if isValidEmail(value) {
            emailErrorText = nil
        } else if isValidKuwaitPhoneNumber(value) {
            emailErrorText = nil
        } else if value.isEmpty {
            emailErrorText = "Field is required."
        } else if isNumeric(value) {
            emailErrorText = "Invalid Kuwaiti phone number. It should be 8 digits and start with 5, 6, or 9."
        } else if !isValidEmail(value) {
            emailErrorText = "Invalid email format."
        }
    }

    func countDownString() {
        guard timeRemaining > 0 else {
            timer.upstream.connect().cancel()
            timerExpired = true
            timeStr = String(format: "%02d:%02d", 00, 00)
            return
        }
            
        timeRemaining -= 1
        timeStr = String(format: "%02d:%02d", 00, timeRemaining)
    }
    
    func onSendCode(completion: @escaping (Bool) -> Void) {
        let parameters: [String: Any] = ["user_email": "\(emailText)"]

        onApiCall(api.forgotPassword, parameters: parameters) {
            completion($0.success)
        }
    }
    
    func onResendCode(completion: @escaping (Bool) -> Void) {
        let parameters: [String: Any] = ["user_email": "\(emailText)"]

        onApiCall(api.resendOTP, parameters: parameters) {
            completion($0.success)
            if $0.success { self.startTimer() }
        }
    }
    
    func updateOtpErrorMessage(_ value: String?) { otpErrorMessage = value }
    
    func onVerifyCode(completion: @escaping (Bool) -> Void) {
        let parameters: [String: Any] = ["user_email": "\(emailText)", "otp": "\(otp)"]
        updateOtpErrorMessage(nil)
        
        onApiCall(api.verifyOTP, parameters: parameters, hideClientSideError: true) {
            if let data = $0.data { self.userID = data.id }
            completion($0.success)
            self.otpErrorMessage = $0.error ? $0.message : nil
        } onFailure: { error in
            self.updatePageState(.failure(error: error))
            self.updateOtpErrorMessage(error)
        }
    }
    
    func onResetPassword(completion: @escaping (Bool) -> Void) {
        guard let userID = userID else { return }
        let parameters: [String: Any] = ["user_id": "\(userID)", "password": "\(passwordText)"]

        onApiCall(api.changePassword, parameters: parameters) {
            completion($0.success)
        }
    }
}

extension ForgotPasswordViewModel: RoutableProtocol {
    nonisolated static func == (lhs: ForgotPasswordViewModel, rhs: ForgotPasswordViewModel) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    nonisolated func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }
}
