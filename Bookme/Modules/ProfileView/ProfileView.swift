import SwiftUI

struct ProfileView: View {
    @StateObject var viewModel = ProfileViewModel()
    @Environment(\.dismiss) var dismiss: DismissAction
    @EnvironmentObject private var appState: AppState
    // Add a state variable to track the currently focused field
    @State private var focusedFieldIndex: Int?
    @FocusState private var focusedField: TextFieldFocus?
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainSection
                .shimmerize()
                .background(ColorConstants.bgGradient)
        } content: {
            MainSection
        }
    }

    var MainSection: some View {
        VStack {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Profile") {
                VStack {
                    VStack {
                        ZStack(alignment: .bottomTrailing) {
                            if let image = viewModel.image {
                                Image(uiImage: image)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: getRelativeWidth(96.0), height: getRelativeWidth(96.0),
                                           alignment: .center)

                                    .clipShape(.circle)
                                    .overlay(Circle().stroke(ColorConstants.Cyan800, lineWidth: 2))
                            } else {
                                NetworkImageView(path: self.viewModel.profileModel?.imageUrl, contentMode: .fill)
                                    .frame(width: getRelativeWidth(96.0), height: getRelativeWidth(96.0),
                                           alignment: .center)

                                    .clipShape(Circle())
                                    .overlay(Circle().stroke(ColorConstants.Cyan800, lineWidth: 2))
                            }
                            self.addImageMenuView()
                        }
                        .fullScreenCover(isPresented: self.$viewModel.showSheet) {
                            // Pick an image from the photo library:
                            ImagePicker(sourceType: viewModel.sourceType, selectedImage: self.$viewModel.image, isShowingCropper: $viewModel.isShowingCropper)
                        }
                        .fullScreenCover(isPresented: $viewModel.isShowingCropper) {
                            if let selectedImage = self.viewModel.image {
                                ImageCropper(image: selectedImage, croppedImage: self.$viewModel.image, onDidFinishCancelled: self.viewModel.onCancelImageCrop)
                            }
                        }

                        .hideNavigationBar()
                        .frame(width: getRelativeWidth(96.0), height: getRelativeHeight(97.0),
                               alignment: .center)
                        .padding(.horizontal, getRelativeWidth(13.0))

                        let nameIndex: Int = 0
                        CustomTextField(title: "Name", placeholder: "Name", text: self.$viewModel.nameText)
                            .keyboardType(.default)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
//                                .onChange(of: self.viewModel.nameText) { _, newValue in
//                                    withAnimation(.bouncy) {
//                                        self.viewModel.nameErrorText = newValue.isEmptyOrWhitespace
//                                            ? "Name field can't be empty"
//                                            : nil
//                                    }
//                                }
                            .focused($focusedField, equals: .field(nameIndex))
                            .focusable(fieldIndex: nameIndex, focusedFieldIndex: $focusedFieldIndex)

                        let mobileIndex: Int = 1
                        CustomTextField(title: "Mobile Number", placeholder: "Enter your phone number", text: self.$viewModel.phoneNumberText, invalidText: viewModel.phoneNumberErrorText, isPhone: true)
                            .keyboardType(.phonePad)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
//                                .onChange(of: self.viewModel.phoneText) { _, newValue in
//                                    withAnimation(.bouncy) {
//                                        self.viewModel.phoneErrorText = newValue.isEmptyOrWhitespace
//                                            ? "Phone Number field can't be empty"
//                                            : nil
//                                    }
//                                }
                            .focused($focusedField, equals: .field(mobileIndex))
                            .focusable(fieldIndex: mobileIndex, focusedFieldIndex: $focusedFieldIndex)

                        let emailIndex: Int = 2
                        CustomTextField(title: "Email", placeholder: "Email", text: self.$viewModel.emailText)
                            .keyboardType(.emailAddress)
                            .frame(width: getRelativeWidth(321.0), alignment: .center)
                            .padding(.top, getRelativeHeight(16))
                            .padding(.leading, getRelativeWidth(9.0))
//                                .onChange(of: self.viewModel.nameText) { _, newValue in
//                                    withAnimation(.bouncy) {
//                                        self.viewModel.nameErrorText = newValue.isEmptyOrWhitespace
//                                            ? "Name field can't be empty"
//                                            : nil
//                                    }
//                                }
                            .focused($focusedField, equals: .field(emailIndex))
                            .focusable(fieldIndex: emailIndex, focusedFieldIndex: $focusedFieldIndex)

                        VStack {
                            CustomTextField(title: "DOB", placeholder: "DD/MM/YY", text: .constant(viewModel.selectedDate.toString(outputFormate: "dd/MM/yyyy")))
                                .disabled(true)
                                .frame(width: getRelativeWidth(321.0), alignment: .center)
                                .padding(.top, getRelativeHeight(16))
                                .padding(.leading, getRelativeWidth(9.0))
                        }
                        .onTapGesture {
                            viewModel.showPopUpType = .date
                            dismissKeyboard()
                        }
                    }
                    .padding(.top, getRelativeHeight(23.0))
                    .padding(.horizontal, getRelativeWidth(22.0))
                    .coordinateFocus(focusedField: _focusedField, focusedFieldIndex: $focusedFieldIndex)
                    .addKeyboardNavigation(focusedFieldIndex: $focusedFieldIndex, totalFields: 4, onDismiss: dismissKeyboard)

                    VStack(alignment: .leading) {
                        HStack(spacing: 4) {
                            Text("Gender")

                            Text("*")
                                .foregroundColor(.red)
                        }

                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(46.0), height: getRelativeHeight(18.0),
                               alignment: .topLeading)
                        .padding(.horizontal, getRelativeWidth(25.0))
                        .padding(.top, getRelativeHeight(19.0))

                        HStack {
                            Menu {
                                ForEach(GenderType.allCases) { type in
                                    Button {
                                        withAnimation(.bouncy) {
                                            //                                                selectedPlace = place
                                            self.viewModel.selectedGenderType = type
                                            dismissKeyboard()
                                        }

                                    } label: {
                                        Text(type.rawValue.capitalized)
                                    }
                                }

                            } label: {
                                HStack {
                                    if let selectedGenderType = viewModel.selectedGenderType {
                                        HStack {
                                            Text(selectedGenderType.rawValue.capitalized)
                                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                                .fontWeight(.medium)
                                                .foregroundColor(ColorConstants.WhiteA700)
                                                .fixedSize()
                                                .multilineTextAlignment(.leading)
                                                .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(18.0),
                                                       alignment: .topLeading)
                                                .padding(.top, getRelativeHeight(15.0))
                                                .padding(.bottom, getRelativeHeight(16.0))
                                                .padding(.leading, getRelativeWidth(20.0))
                                        }
                                    } else {
                                        Text("Select")
                                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Cyan800.opacity(0.77))
                                            .fixedSize()
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.top, getRelativeHeight(15.0))
                                            .padding(.bottom, getRelativeHeight(16.0))
                                            .padding(.leading, getRelativeWidth(20.0))
                                    }
                                    Spacer()
                                    Image("img_arrowup")
                                        .resizable()
                                        .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(6.0),
                                               alignment: .center)
                                        .scaledToFit()
                                        .clipped()
                                        .padding(.vertical, getRelativeHeight(22.0))
                                        .padding(.trailing, getRelativeWidth(24.0))
                                }
                                //                    .bold()
                                .foregroundColor(.blue)
                                .padding(.vertical, 12)
                            }
                        }
                        .frame(width: getRelativeWidth(321.0), height: getRelativeHeight(50.0),
                               alignment: .center)
                        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                bottomRight: 13.0)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                   bottomRight: 13.0)
                                .fill(ColorConstants.Cyan8003f1))
                        .padding(.leading, getRelativeWidth(13.0))
                    }

                    .padding(.top, getRelativeHeight(7.0))
                    .padding(.horizontal, getRelativeWidth(22.0))
                }
            }
            .preferredColorScheme(.dark)
            .safeAreaInset(edge: .bottom) {
                Button(action: {
                    self.viewModel.updateUserProfile {
                        Utilities.enQueue(after: .now() + 0.5) {
                            self.dismiss()
                        }
                    }
                }, label: {
                    HStack(spacing: 0) {
                        Text("Update Profile")
                            .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.heavy)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(15.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                            .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                       bottomLeft: 25.0, bottomRight: 25.0)
                                    .fill(ColorConstants.Cyan800))
                            .disableWithOpacity(self.viewModel.disableUpdateButton)
                            .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                       bottomLeft: 25.0, bottomRight: 25.0)
                                    .fill(ColorConstants.Cyan800))
                            .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                            .padding(.leading, getRelativeWidth(13.0))
                    }
                })
                .disabled(self.viewModel.disableUpdateButton)
                .onChange(of: self.viewModel.disableUpdateButton) { _, newValue in
                    print(newValue)
                }
                .padding(.top, getRelativeHeight(72.0))
//                .padding(.bottom, (AppConstants.tabBarHeight + 8).relativeHeight)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(ColorConstants.bgGradient)
        .onTapGesture(perform: dismissKeyboard)
        .onAppear {
            // Set AppState reference for reactive updates
            viewModel.appState = appState
        }
        .overlay {
            VStack {
                if viewModel.showPopUpType != .hide {
                    CustomDatePicker(date: $viewModel.selectedDate, range: .to(Date()), isShowCalendar: $viewModel.showPopUpType, changed: viewModel.onDateChange)
                        .transition(.scale.combined(with: .opacity))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(.black.opacity(0.01))
                }
            }
            .animation(.easeInOut, value: viewModel.showPopUpType)
        }
    }

    // Add helper method to dismiss keyboard
    private func dismissKeyboard() {
        focusedField = nil
        focusedFieldIndex = nil
    }
    @ViewBuilder
    func addImageMenuView() -> some View {
        Menu {
            Button {
                // Add this item to a list of favorites.
                self.viewModel.onAddImage(.photoLibrary)
                self.dismissKeyboard()

            } label: {Label("Photo Library", systemImage: "photo.fill") }
            Button {
                self.viewModel.onAddImage(.camera)
                self.dismissKeyboard()
            } label: { Label("Camera", systemImage: "camera.fill")}
        } label: {
            Image("img_group148")
            .frame(width: getRelativeWidth(26.0), height: getRelativeWidth(26.0),
                   alignment: .center)
            .background(Circle().fill(ColorConstants.Cyan800))
            .padding(.top, getRelativeHeight(71.0))
            .padding(.leading, getRelativeWidth(69.0))
        }
    }
}

struct ProfileView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            ProfileView()
                .attachAllEnvironmentObjects()
                .preferredColorScheme(.dark)
        }
    }
}
