import Foundation
import SwiftUI
import SDWebImageSwiftUI

class ProfileViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var nameText: String = ""
    @Published var emailText: String = ""
    @Published var phoneNumberText: String = ""
    @Published var isValidEmailoneText: Bool = true
//    @Published var dobText: String = ""
    @Published var selectedGenderType: GenderType?
    @Published var profileModel: ProfileModel?
    @Published var image: UIImage?
    @Published var showSheet = false
    @Published var sourceType: UIImagePickerController.SourceType = .photoLibrary
    @Published var isShowingCropper = false
    @Published var selectedDate = Date()
    let dateOutputFormate = "dd/MM/yyyy"
    let inputFormate = "yyyy-MM-dd"

    @Published var showPopUpType: popUpType = .hide

    @Published var phoneNumberErrorText: String?

    // Reference to AppState for reactive updates
    weak var appState: AppState?

    override init() {
        super.init()

        getUserProfile(setUserDefaults: true)
    }

    func onAddImage(_ sourceType: UIImagePickerController.SourceType) {
        checkForCameraExceptions(sourceType: sourceType) {
            withAnimation(.spring()) {
                self.sourceType = sourceType
                self.showSheet = true
            }
        }
    }

    func onCancelImageCrop(_ cancel: Bool){
        guard cancel else { return }
        self.isShowingCropper = false
        self.image = nil
    }

    func onDateChange(newDate: Date) {
//        selectedDate = newDate
//        guard let selectedDate = selectedDate else { return }
//        currentDate = selectedDate
//        print(selectedDate.format(with: "yyyy-MM-dd"))
    }

    func onCloseImage() { withAnimation(.spring()) {
        self.image = nil
    } }

    // Disable the update button only when no changes are detected in the fields
//    var disableUpdateButton: Bool {
//        let isNameEqual: Bool = nameText.trimmed == profileModel?.name
//        let isPhoneEqual: Bool = phoneNumberText.trimmed == profileModel?.userPhone
//        let isEmailEqual: Bool = emailText.trimmed == profileModel?.email
//        let isGenderEqual: Bool = selectedGenderType?.rawValue == profileModel?.gender.lowercased()
//        let isDateEqual: Bool = selectedDate == profileModel?.dob.toDate(inputFormate: "yyyy-MM-dd")
//        let isImageNotChanged: Bool = image == nil
//        return nameText.isEmptyOrWhitespace ||
////            phoneNumberText.isEmptyOrWhitespace ||
//            emailText.isEmptyOrWhitespace ||
//            selectedGenderType == nil ||
//            (isNameEqual &&
//                isPhoneEqual &&
//                isEmailEqual &&
//                isGenderEqual &&
//                isDateEqual && isImageNotChanged)
//    }
    var disableUpdateButton: Bool {
        guard let profile = profileModel else { return true }

        let hasFieldChanged = nameText.trimmed != profile.name ||
                              phoneNumberText.trimmed != (profile.userPhone ?? "") ||
                              emailText.trimmed != (profile.email ?? "") ||
                              selectedGenderType?.rawValue != profile.gender.lowercased() ||
                              selectedDate != profile.dob.toDate(inputFormate: "yyyy-MM-dd") ||
                              image != nil // ✅ new image selected

        let hasRequiredFieldEmpty = nameText.isEmptyOrWhitespace ||
                                    emailText.isEmptyOrWhitespace ||
                                    selectedGenderType == nil

        return hasRequiredFieldEmpty || !hasFieldChanged
    }

    func onChangeDate(value: Date) {
//        selectedDOBDate = value
    }

    func assignValues(value: ProfileModel) {
        nameText = value.name
        phoneNumberText = value.userPhone ?? ""

        if let email = value.email {
            if email.isEmail {
                emailText = email
            } else {
                phoneNumberText = email
            }
        }
//        selectedDOBDate = value.dob.toDate(inputFormate: inputFormate)
//        dobText = selectedDate.toString(outputFormate: dateOutputFormate)
        selectedDate = value.dob.toDate(inputFormate: "yyyy-MM-dd")
        selectedGenderType = .init(rawValue: value.gender.lowercased())
    }

    func assignValueForUserDefaults(value: ProfileModel) {
        var model = AppState.userModel
        let oldImageUrl = model?.user.imageUrl

        model?.user.image = value.image
        model?.user.name = value.name
        model?.user.userPhone = value.userPhone

        if let email = value.email {
            if email.isEmail {
                model?.user.email = email
            } else {
                model?.user.userPhone = email
            }
        }

        model?.user.dob = value.dob
        model?.user.gender = value.gender

        // Clear image cache if image URL changed
        let newImageUrl = model?.user.imageUrl
        if oldImageUrl != newImageUrl, let oldUrl = oldImageUrl {
            clearImageCache(for: oldUrl)
        }

        // Update AppState reactively to trigger UI updates
        if let appState = appState {
            appState.updateUserModel(model)
        } else {
            AppState.userModel = model
        }
    }

    /// Clears SDWebImage cache for a specific image URL
    /// - Parameter imageUrl: The image URL to clear from cache
    private func clearImageCache(for imageUrl: String) {
        guard let url = URL(string: imageUrl) else { return }

        // Clear from memory cache
        SDImageCache.shared.removeImage(forKey: url.absoluteString, cacheType: .memory)

        // Clear from disk cache
        SDImageCache.shared.removeImage(forKey: url.absoluteString, cacheType: .disk) { [weak self] in
            DispatchQueue.main.async {
                // Force refresh any cached images by clearing memory cache
                SDImageCache.shared.clearMemory()
            }
        }
    }

    func getUserProfile(setUserDefaults: Bool = false) {
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters = ["user_id": "\(userID)"]

        onApiCall(api.viewUserProfile, parameters: parameters) {
            self.profileModel = $0.data
            if let data = $0.data {
                self.assignValues(value: data)
                if setUserDefaults { self.assignValueForUserDefaults(value: data) }
            }
        }
    }

    func updateUserProfile(completion: @escaping () -> Void) {
        if phoneNumberText.isEmptyOrWhitespace {
            phoneNumberErrorText = "Enter your phone number"
            return
        }
        phoneNumberErrorText = nil
        guard let userID = AppState.userModel?.user.id else { return }
        let dob = selectedDate.toString(outputFormate: "yyyy-MM-dd")
        var parameters: [String: Any] = [
            "user_id": "\(userID)",
            "name": nameText,
            "user_phone": phoneNumberText,
            "email": emailText,
            "date_of_birth": dob,
            "Gender": selectedGenderType?.rawValue.capitalized ?? "",
        ]

        if let image = image?.compressTo(1) {
            parameters["image"] = image as Any
        }

        onApiCall(api.updateUserProfile, parameters: parameters) {
            if $0.success {
                self.getUserProfile(setUserDefaults: true)
                completion()
            }
        }
    }
}

extension UIImage {
    // MARK: - UIImage+Resize

    func compressTo(_ expectedSizeInMb: Int) -> Data? {
        let sizeInBytes = expectedSizeInMb * 1024 * 1024
        var needCompress = true
        var imgData: Data?
        var compressingValue: CGFloat = 1.0
        while needCompress, compressingValue > 0.0 {
            if let data: Data = jpegData(compressionQuality: compressingValue) {
                if data.count < sizeInBytes {
                    needCompress = false
                    imgData = data
                } else {
                    compressingValue -= 0.1
                }
            }
        }

        if let data = imgData {
            if data.count < sizeInBytes {
                return data
            }
        }
        return nil
    }
}

extension String {
    func toDate(inputFormate: String) -> Date {
        // Create Date Formatter
        let dateFormatter = DateFormatter()

        // Set Date Format
        dateFormatter.dateFormat = inputFormate
        // Convert Date to String
        return dateFormatter.date(from: self) ?? Date()
    }

    func timeDateFromDateString(inputFormat: String, outputFormat: String) -> String {
        guard !isEmpty, !inputFormat.isEmpty, !outputFormat.isEmpty else { return "-" }

        let dateFormatter = DateFormatter()

        // step 1
        dateFormatter.dateFormat = inputFormat // input format
        let date = dateFormatter.date(from: self)!

        // step 2
        dateFormatter.dateFormat = outputFormat // output format
        let string = dateFormatter.string(from: date)
        return string
    }
}

extension String {
    var isEmptyOrWhitespace: Bool {
        // Check empty string
        if isEmpty {
            return true
        }
        // Trim and check empty string
        return trimmingCharacters(in: .whitespaces) == ""
    }
}

extension Optional where Wrapped == String {
    var isEmptyOrWhitespace: Bool {
        // Check nil
        guard let this = self else { return true }

        // Check empty string
        if this.isEmpty {
            return true
        }
        // Trim and check empty string
        return this.trimmingCharacters(in: .whitespaces) == ""
    }
}

extension LocalizedStringKey {
    var stringValue: String? {
        let mirror = Mirror(reflecting: self)
        for child in mirror.children {
            if child.label == "key" {
                return child.value as? String
            }
        }
        return nil
    }
}
