//
//  ProfileModel.swift
//  Bookme
//
//  Created by Apple on 28/04/2024.
//

import Foundation

// MARK: - ProfileModel

struct ProfileModel: Codable {
    let name: String
    let userPhone, email: String?
    let dob, gender: String
    let image:String?

    var imageUrl: String { AppConstants.Server.baseURL + (image ?? "") }

    enum CodingKeys: String, CodingKey {
        case name
        case userPhone = "user_phone"
        case email, dob
        case gender = "Gender"
        case image
    }
}

struct ProfileRequest:Encodable{
    let userID,name, userPhone, email, dob, gender:String
    var image:Data?

    enum CodingKeys: String, CodingKey {
        case userID = "user_id"
        case name
        case userPhone = "user_phone"
        case email, image
        case dob = "date_of_birth"
        case gender = "Gender"
    }
}
