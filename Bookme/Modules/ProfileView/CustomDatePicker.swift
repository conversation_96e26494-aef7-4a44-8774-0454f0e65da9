//
//  CustomDatePicker.swift
//  Bookme
//
//  Created by Apple on 23/09/2024.
//

import SwiftUI

enum popUpType {
    case date, startTime, endTime, hide
}

struct CustomDatePicker: View {
    let range: Range
    let changed: (Date) -> Void
    let dismiss: () -> Void
    let displayedComponents:DatePickerComponents
    let label:String
    @Binding var newDate: Date
    @Binding var isShowCalendar: popUpType
    
    init(date: Binding<Date>, range: Range, isShowCalendar: Binding<popUpType>? = nil, changed: @escaping (Date) -> Void, dismiss: @escaping () -> Void = {}, displayedComponents:DatePickerComponents = [.date], label:String = "") {
        self.range = range
        self.changed = changed
        self.dismiss = dismiss
        self._isShowCalendar = isShowCalendar ?? Binding.constant(.hide)
        self._newDate = date
        self.displayedComponents = displayedComponents
        self.label = label
    }
    
    enum Range {
        case from(Date), to(Date), through
    }
    
    var body: some View {
//        ZStack {
           
            VStack {
                switch range {
                
                case .from(let minimum):
                    DatePicker(
                        label,
                        selection: $newDate,
                        in: minimum...,
                        displayedComponents: displayedComponents)
                        .datePickerStyle(.graphical)
                        .padding()
//                        .font(.custom(textStyle: .title3))
                
                case .to(let maximum):
                    DatePicker(
                        label,
                        selection: $newDate,
                        in: ...maximum,
                        displayedComponents: displayedComponents)
                        .datePickerStyle(.graphical)
                        .padding()
                        .font(.system(.title3))
                    
                case .through:
                    DatePicker(
                        label,
                        selection: $newDate,
                        displayedComponents: displayedComponents)
                        .datePickerStyle(.graphical)
                        .padding()
               
                }
                
                HStack {
                    Button {
                        withAnimation {
                            isShowCalendar = .hide
                            dismiss()
                        }
                    } label: {
                        Text("Cancel")
                            .foregroundColor(Color.gray)
                    }

                    Spacer()
                    
                    Button {
                        withAnimation {
                            isShowCalendar = .hide
                            changed(newDate)
                        }
                    } label: {
                        Text("Done")
                            .padding(.horizontal)
                            .padding(.vertical, 10.0)
//                            .background(
//                                ColorConstants.bgGradient
//                            )
//                            .foregroundColor(.white)
//                            .cornerRadius(5)
                    }
                }
                .padding([.leading, .bottom, .trailing])
//                .font(.custom(textStyle: .caption))
            }
            .background(.thinMaterial)
            .cornerRadius(10)
            .shadow(radius: 3)
            .padding()
//        }
//        .ignoresSafeArea()
    }
}
