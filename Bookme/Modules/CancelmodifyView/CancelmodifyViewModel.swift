import Foundation
import SwiftUI

enum PopUpType {
    case date, month, hours
}

class CancelmodifyViewModel: SuperViewModel {
    private static var now = Date() // Cache now
    
    @Published var nextScreen: String? = nil
    @Published var timeSlotErrorMessage: String?
    @Published var vendorTimeslotModelList: [VendorTimeslotModel.TimeSlot] = []
    @Published var selectedTimeSlot: VendorTimeslotModel.TimeSlot?
    @Published var submittedTimeSlot: VendorTimeslotModel.TimeSlot?
    @Published var popUpType: PopUpType?
    @Published var selectedDate: Date = CancelmodifyViewModel.now
    @Published var submittedDate: Date = CancelmodifyViewModel.now
    @Published var isDateChanged: Bool = false
    @Published var bookingHour: String = .init()
    
    let bookingModel: BookingAppointmentModel
    
    init(model: BookingAppointmentModel) {
        self.bookingModel = model
        
        super.init()
        
        
    }
    
    func updatePopUpType(_ value: PopUpType?) { withAnimation(.bouncy) { self.popUpType = value }}
    
    func onCancelBooking(completion: @escaping () -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = [
            "order_id": bookingModel.bookID,
            "user_id": userID,
        ]
        onApiCall(api.cancelBooking, parameters: parameters) {
            if $0.success {
                completion()
            }
        }
    }
    
//    func getVendorTimeSlot() {
//        let parameters: [String: String] = [
//            "selected_date": submittedDate.toString(outputFormate: dateOutputFormat),
//            "vendor_id": "\(bookingModel.vendorID)",
//            "service_id": "\(bookingModel.orderDetails.first?.serviceID ?? 0)",
//        ]
//        
//        onApiCall(api.vendorTimeSlot, parameters: parameters, withLoadingIndicator: false) {
//            self.timeSlotErrorMessage = nil
//            self.vendorTimeslotModelList = $0.data?.timeSlot ?? []
//            
//            if self.selectedTimeSlot == nil {
////                self.selectedTimeSlot = $0.data?.first
//            } else {
//                self.updateTimeSlotWithTimeString(nil)
//            }
//        } onFailure: {
//            self.timeSlotErrorMessage = $0
//        }
//    }
    
    
    func getStaffTimeslot() {
        vendorTimeslotModelList.removeAll()
        
        let dateString = submittedDate.toString(outputFormate: "yyyy-MM-d")
  
        guard let firstService = bookingModel.services?.first else { return }
        guard let staffID = firstService.staffID else { return }
        
        let parameters: [String: String] = [
            "date": dateString,
            "vendor_id": "\(bookingModel.vendorID)",
            "service": "\(firstService.servicesID)",
            "staff": "\(staffID)",
        ]

        onApiCall(api.staffTimeslot, parameters: parameters, withLoadingIndicator: true) {
            self.timeSlotErrorMessage = nil
            self.vendorTimeslotModelList = $0.data?.timeSlot ?? []
            
            let firstAvailableSlot = self.vendorTimeslotModelList.first(where: { $0.isDisabled == false })
              
        
            
            self.updateTimeSlot(firstAvailableSlot)
            self.selectedTimeSlot = firstAvailableSlot
            
//            onCompletion?()

        } onFailure: {
            self.timeSlotErrorMessage = $0
        }
    }
    
    func onSubmit(completion: @escaping () -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let date: String = submittedDate.toString(outputFormate: dateOutputFormat)
        let time: String = submittedTimeSlot?.timeslot ?? ""
        
        let parameters: [String: Any] = [
            "order_id": bookingModel.bookID,
            "user_id": userID,
            "date": date,
            "time": time,
        ]
        onApiCall(api.modifyBooking, parameters: parameters) {
            if $0.success {
                completion()
            }
        }
    }
    
    func updateTimeSlotWithTimeString(_ value: String?) {
        if let value = value {
            let timeSlot = vendorTimeslotModelList.first(where: { $0.timeslot == value })
            updateTimeSlot(timeSlot)
        }
    }
    
    func updateTimeSlot(_ value: VendorTimeslotModel.TimeSlot?) {
        if let value = value {
            withAnimation(.bouncy) {
                selectedTimeSlot = value
            }
        }
    }
    
    func isSelectedTimeSlot(_ value: VendorTimeslotModel.TimeSlot?) -> Bool { value == selectedTimeSlot }
}
