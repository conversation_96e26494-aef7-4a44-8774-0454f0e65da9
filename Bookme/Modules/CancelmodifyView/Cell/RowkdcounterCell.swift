import SwiftUI

struct RowkdcounterCell: View {
    let model: OrderDetail
    
    @State private var checked:Bool = true
    var body: some View {
        HStack {
            HStack {
                CheckBoxView(checked: $checked)
                Text(model.serviceName)
                    .font(FontScheme.kMontserratMedium(size: getRelativeHeight(13.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Black900B2)
                   
                    .multilineTextAlignment(.leading)
                    .frame( height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.leading, getRelativeWidth(7.0))
            }
            .frame(height: getRelativeHeight(18.0),
                   alignment: .leading)
            Spacer()
            
            CurrencyText(model.price)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Black900)
               
                .multilineTextAlignment(.leading)
                .frame( height: getRelativeHeight(18.0),
                       alignment: .leading)
                .padding(.leading, 16.0.relativeWidth)
        }
    }
}


