import SwiftUI
import WrappingHStack

struct CancelModifyView: View {
    @StateObject var viewModel: CancelmodifyViewModel
    @Environment(\.dismiss) var dismiss: DismissAction
    @EnvironmentObject private var appState: AppState
    @Namespace private var animation
    @State private var isEventTriggered: Bool = false
    @State private var isEventAdded: Bool = false
    @State private var showAlert = false // State variable to show or hide the alert

    init(model: BookingAppointmentModel) {
        self._viewModel = StateObject(wrappedValue: CancelmodifyViewModel(model: model))
    }
    
    var getTime: String {
        !viewModel.bookingHour.isEmpty
            ? viewModel.bookingHour
            : (viewModel.isDateChanged && viewModel.submittedTimeSlot == nil)
            ? viewModel.submittedTimeSlot != nil
            ? viewModel.submittedTimeSlot!.timeslot
            : "-"
            : viewModel.bookingModel.serviceTime
    }
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            ZStack(alignment: .bottom) {
                let model = viewModel.bookingModel
                
                MainScrollBody(backButtonWithTitle: "Cancel / Modify") {
                    VStack(alignment: .leading, spacing: 0) {
                        VStack {
                            Group {
                                HStack {
                                    Text("Barber / Vendor")
                                        .font(FontScheme
                                            .kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(88.0),
                                               height: getRelativeHeight(16.0), alignment: .topLeading)
                                        .padding(.bottom, getRelativeHeight(4.0))
                                    Spacer()
                                    Text(model.vendorName)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(18.0), alignment: .topLeading)
                                        .padding(.leading, getRelativeWidth(70.0))
                                }
                                .frame(width: getRelativeWidth(323.0), height: getRelativeHeight(20.0),
                                       alignment: .center)
                                .padding(.top, getRelativeHeight(20.0))
                                .padding(.horizontal, getRelativeWidth(15.0))
                                Divider()
                                    .frame(width: getRelativeWidth(328.0),
                                           height: getRelativeHeight(1.0), alignment: .center)
                                    .background(ColorConstants.Cyan8004c)
                                    .padding(.top, getRelativeHeight(9.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                HStack {
                                    Text("Address")
                                        .font(FontScheme
                                            .kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(16.0), alignment: .topLeading)
                                        .padding(.vertical, getRelativeHeight(2.0))
                                    Spacer()
                                    Text(model.address)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .fixedSize(horizontal: false, vertical: true)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.trailing)
                                        .padding(.leading, getRelativeWidth(64.0))
                                }
                                .frame(width: getRelativeWidth(325.0),
                                       alignment: .top)
                                .padding(.top, getRelativeHeight(10.0))
                                .padding(.horizontal, getRelativeWidth(15.0))
                                Divider()
                                    .frame(width: getRelativeWidth(328.0),
                                           height: getRelativeHeight(1.0), alignment: .center)
                                    .background(ColorConstants.Cyan8004c)
                                    .padding(.top, getRelativeHeight(7.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                HStack {
                                    Text("Name")
                                        .font(FontScheme
                                            .kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(39.0),
                                               height: getRelativeHeight(16.0), alignment: .topLeading)
                                    Spacer()
                                    Text(model.userName)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(18.0), alignment: .topLeading)
                                }
                                .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
                                       alignment: .center)
                                .padding(.top, getRelativeHeight(12.0))
                                .padding(.horizontal, getRelativeWidth(15.0))
                                Divider()
                                    .frame(width: getRelativeWidth(328.0),
                                           height: getRelativeHeight(1.0), alignment: .center)
                                    .background(ColorConstants.Cyan8004c)
                                    .padding(.top, getRelativeHeight(8.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                HStack {
                                    Text("Phone")
                                        .font(FontScheme
                                            .kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(42.0),
                                               height: getRelativeHeight(16.0), alignment: .topLeading)
                                    Spacer()
                                    Text(model.vendorPhone)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(18.0), alignment: .topLeading)
                                }
                                .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(19.0),
                                       alignment: .center)
                                .padding(.top, getRelativeHeight(14.0))
                                .padding(.horizontal, getRelativeWidth(15.0))
                                Divider()
                                    .frame(width: getRelativeWidth(328.0),
                                           height: getRelativeHeight(1.0), alignment: .center)
                                    .background(ColorConstants.Cyan8004c)
                                    .padding(.top, getRelativeHeight(8.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                            }
                            Group {
                                HStack {
                                    Text("Booking Date")
                                        .font(FontScheme.kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(86.0), height: getRelativeHeight(16.0),
                                               alignment: .leading)
                                      
                                    Spacer()
                                    HStack {
                                        let submittedDate = viewModel.submittedDate.toString(outputFormate: "yyyy-MM-dd")
                                        
                                        Text(submittedDate)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .trailing)
                                            .contentTransition(.numericText())
                                            .animation(.bouncy, value: viewModel.submittedDate)
                                        Button(action: {
                                            viewModel.selectedDate = viewModel.submittedDate
                                            self.viewModel.updatePopUpType(.date)
                                            
                                        }, label: {
                                            Image("img_edit")
                                                .frame(width: getRelativeWidth(23.0), height: getRelativeWidth(25.0),
                                                       alignment: .center)
                                                .overlay(Circle()
                                                    .stroke(ColorConstants.Cyan800,
                                                            lineWidth: 1))
                                                .background(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                                           bottomRight: 12.5)
                                                        .fill(Color.clear.opacity(0.7)))
                                                .padding(.leading, getRelativeWidth(9.0))
                                        })
                                    }
                                    .frame(height: getRelativeHeight(25.0),
                                           alignment: .leading)
                                }
                                .frame(width: getRelativeWidth(329.0), alignment: .center)
                                .padding(.top, getRelativeHeight(10.0))
                                .padding(.horizontal, getRelativeWidth(15.0))
                                
                                Divider()
                                    .frame(width: getRelativeWidth(328.0),
                                           height: getRelativeHeight(1.0), alignment: .center)
                                    .background(ColorConstants.Cyan8004c)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                
                                HStack {
                                    Text("Booking Hours")
                                        .font(FontScheme.kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(86.0), height: getRelativeHeight(16.0),
                                               alignment: .leading)
                                    
                                    Spacer()
                                    
                                    HStack {
                                        Text(getTime)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .trailing)
                                        Button(action: {
//                                            viewModel.getVendorTimeSlot()
                                            
                                            viewModel.getStaffTimeslot()
                                            
                                            self.viewModel.updatePopUpType(.hours)
                                        }, label: {
                                            Image("img_edit")
                                                .frame(width: getRelativeWidth(23.0), height: getRelativeWidth(25.0),
                                                       alignment: .center)
                                                .overlay(Circle()
                                                    .stroke(ColorConstants.Cyan800,
                                                            lineWidth: 1))
                                                .background(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                                           bottomRight: 12.5)
                                                        .fill(Color.clear.opacity(0.7)))
                                                .padding(.leading, getRelativeWidth(9.0))
                                        })
                                    }
                                    .frame(height: getRelativeHeight(25.0),
                                           alignment: .leading)
                                }
                                .frame(width: getRelativeWidth(329.0), alignment: .center)
                                .padding(.top, getRelativeHeight(10.0))
                                .padding(.horizontal, getRelativeWidth(15.0))
                                
                                Divider()
                                    .frame(width: getRelativeWidth(328.0),
                                           height: getRelativeHeight(1.0), alignment: .center)
                                    .background(ColorConstants.Cyan8004c)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                HStack {
                                    Text("Specialist")
                                        .font(FontScheme
                                            .kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .multilineTextAlignment(.leading)
                                       
                                    Spacer()
                                    let staffName = model.orderDetails.map { $0.staffName }.uniqued().formatted(.list(type: .and, width: .short))
                                    
                                    Text(staffName)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.leading)
                                }
                                .padding(.vertical, getRelativeHeight(12.0))
                                .padding(.horizontal, getRelativeWidth(15.0))
                                
//                                Divider()
//                                    .frame(width: getRelativeWidth(328.0),
//                                           height: getRelativeHeight(1.0), alignment: .center)
//                                    .background(ColorConstants.Cyan8004c)
//                                    .padding(.top, getRelativeHeight(6.0))
//                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    
                                if viewModel.bookingModel.canBookingCancel {
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(6.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    
                                    HStack {
                                        Text("Cancel appointment")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(16.0), alignment: .topLeading)
                                        Spacer()
                                       
                                        Button {
                                            showAlert.toggle()
                                        } label: {
                                            Image(.trashBin)
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 11.relativeWidth, height: 14.relativeHeight)
                                                .foregroundColor(ColorConstants.Black900)
                                                .padding(6)
                                                .background(Color(hex: "#FF0000"))
                                                .clipShape(Circle())
                                        }
                                    }
                                    .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
                                           alignment: .center)
                                    .padding(.vertical, getRelativeHeight(16.0))
                                    .padding(.bottom, getRelativeHeight(8.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                }
                                
                            
                                
//                                HStack {
//                                    Text("Add to Calendar")
//                                        .font(FontScheme
//                                            .kMontserratMedium(size: getRelativeHeight(13.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Black900B2)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(
//                                            height: getRelativeHeight(16.0), alignment: .topLeading)
//                                    Spacer()
//
//                                    Button {
//                                        isEventTriggered = true
//                                    } label: {
//                                        Text(isEventAdded ? "Added" : "Add")
//                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
//                                            .fontWeight(.bold)
//                                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
//                                    }
//                                    .disableWithOpacity(isEventAdded)
//                                }
//                                .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
//                                       alignment: .center)
//                                .padding(.vertical, getRelativeHeight(12.0))
//
//                                .padding(.horizontal, getRelativeWidth(15.0))
//                                .addToCalendar(isTriggered: $isEventTriggered, event: appState.event, onCompletion: { success, _ in
//                                    if success {
//                                        viewModel.updatePageState(.message(config: .init(title: "Success", text: "Calendar event added successfully")))
//                                        isEventAdded = true
//                                    }
//                                })
                            }
                        }
                      
                        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                bottomRight: 20.0)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                   bottomRight: 20.0)
                                .fill(ColorConstants.WhiteA700))
                        Text("Selected Services")
                            .font(FontScheme.kMontserratMedium(size: getRelativeHeight(16.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.Black900)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(137.0), height: getRelativeHeight(20.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(39.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                        VStack {
                            VStack(spacing: 0) {
                                LazyVStack {
                                    ForEach(model.orderDetails) { details in
                                        VStack {
                                            RowkdcounterCell(model: details)
                                                .disabled(true)
                                            if model.orderDetails.last != details {
                                                Divider()
                                                    .frame(width: getRelativeWidth(328.0),
                                                           height: getRelativeHeight(1.0), alignment: .center)
                                                    .background(ColorConstants.Cyan8004c)
                                                    .padding(.vertical, getRelativeHeight(6.0))
                                                    .padding(.horizontal, getRelativeWidth(15.0))
                                            }
                                        }
                                    }
                                }
                            }
                           
                            .padding(.vertical, getRelativeHeight(19.0))
                            .padding(.horizontal, getRelativeWidth(19.0))
                        }
                        
                        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                bottomRight: 20.0)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                   bottomRight: 20.0)
                                .fill(ColorConstants.WhiteA700))
                        .padding(.top, getRelativeHeight(10.0))
                        
                        let cancellationFee: Int = 45
                        
                        Text("If you Cancel less than 24 hours of your Appointment scheduled time, you will be charged a cancellation fee KWD \(cancellationFee) (or deposit if greater). This includes now show 10 minute after Appointment start time. please contact us if you need a refund. Thanks.")
                            .font(FontScheme.kMontserratSemiBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.semibold)
                            .foregroundColor(ColorConstants.Black900)
                            .multilineTextAlignment(.leading)
                            .padding(.top, getRelativeHeight(18.0))
                            .padding(.horizontal, getRelativeWidth(4.0))
                            .lineSpacing(3)
                            .kerning(1)
                    }
                    .background(ColorConstants.WhiteA700)
                    .padding(.vertical, getRelativeHeight(16.0))
                    .padding(.horizontal, getRelativeWidth(15.0))
                    .background(ColorConstants.WhiteA700)
                }
                .safeAreaInset(edge: .bottom) {
                    Button(action: { viewModel.onSubmit {
                        self.appState.resetBookingAppointmentViewID()
                        self.dismiss()
                    } }, label: {
                        HStack(spacing: 0) {
                            Text("Submit")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.heavy)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(13.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                .background(RoundedCorners(topLeft: 23.81, topRight: 23.81,
                                                           bottomLeft: 23.81, bottomRight: 23.81)
                                        .fill(ColorConstants.Cyan800))
                                .disableWithOpacity(viewModel.submittedTimeSlot == nil)
                                .background(RoundedCorners(topLeft: 23.81, topRight: 23.81,
                                                           bottomLeft: 23.81, bottomRight: 23.81)
                                        .fill(ColorConstants.Cyan800))
                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                        }
                    })
                    .disabled(viewModel.submittedTimeSlot == nil)
                    .background(Capsule()
                        .fill(ColorConstants.Cyan800))
                }
                
                Group {
                    switch viewModel.popUpType {
                    case .date:
                        datePopUpView
                    case .month:
                        monthPopUpView
                    case .hours:
                        timeSlotPopUpView
                    case .none: EmptyView()
                    }
                    
                }.animation(.bouncy, value: self.viewModel.popUpType)
            }
           
            .onLoad {
                viewModel.submittedDate = viewModel.bookingModel.serviceDateFormattedAsDate
            }
        }
//        .if(viewModel.bookingModel.canBookingCancel, transform: {
//            $0.navigationBarItems(trailing: Button(action: {
//                showAlert.toggle()
//            }) {
//                Text("Cancel")
//                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                    .fontWeight(.bold)
//                    .foregroundColor(.black) // Customize the cancel button text color if needed
//            })
//        })
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("Cancel Booking"),
                message: Text("Are you sure you want to cancel the booking?"),
                primaryButton: .destructive(Text("Yes")) {
                    // Handle the action when "Yes" is tapped
                    viewModel.onCancelBooking {
                        self.appState.resetBookingAppointmentViewID()
                        self.dismiss()
                    }
                },
                secondaryButton: .cancel(Text("No")) {
                    // Handle the action when "No" is tapped
                    print("Booking not canceled")
                })
        }
    }
    
    var datePopUpView: some View {
        SlideUpAnimationContainerView {
            self.viewModel.updatePopUpType(nil)
        } content: {
            VStack(spacing: 0.relativeHeight) {
                CalendarSelectionView(selectedDate: $viewModel.selectedDate, disabledDates: .constant([]), onMonth: {
                    self.viewModel.updatePopUpType(.month)
                }, onClose: {
                    self.viewModel.updatePopUpType(nil)
                    
                })
                .padding(.top)
             
                Spacer()
                Button(action: {
                    self.viewModel.updatePopUpType(nil)
                    
                    Utilities.enQueue(after: .now() + 0.3) {
                        self.viewModel.submittedDate = viewModel.selectedDate
                        self.viewModel.isDateChanged = true
                    }
                   
                }, label: {
                    HStack(spacing: 0) {
                        Text("Submit")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5,
                                                       bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.bottom, AppConstants.tabBarHeight.relativeHeight + 16.0.relativeHeight)
                            .padding(.horizontal, getRelativeWidth(21.0))
                    }
                })
            }
            .frame(height: 420.0.relativeHeight, alignment: .top)
            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                .fill(ColorConstants.WhiteA700))
            .clipped()
            .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom),
            removal: .opacity // Use opacity for removal
        ))
    }
    
    var monthPopUpView: some View {
        SlideUpAnimationContainerView {
            self.viewModel.updatePopUpType(.date)
        } content: {
            MonthPopUpView(selectedMonth: Calendar.current.component(.month, from: self.viewModel.selectedDate) - 1, selectedDate: viewModel.selectedDate, onSubmit: { date in
                    
                if let date = date {
                    viewModel.selectedDate = date
                }
                  
            }, onClose: { self.viewModel.updatePopUpType(.date) })
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom),
            removal: .opacity // Use opacity for removal
        ))
    }
    
    var timeSlotPopUpView: some View {
        SlideUpAnimationContainerView {
            self.viewModel.updatePopUpType(nil)
        } content: {
            VStack(spacing: 32.relativeHeight) {
                VStack {
                    VStack(alignment: .leading) {
                        HStack {
                            Text("Select Time")
                                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(16.0)))
                                .fontWeight(.semibold)
                                .foregroundColor(ColorConstants.Black900)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(86.0),
                                       height: getRelativeHeight(22.0), alignment: .topLeading)
                                .padding(.horizontal, getRelativeWidth(10.0))
                            Spacer()
                            Button(action: {
                                self.viewModel.updatePopUpType(nil)
                            }) {
                                Image("img_closeroundlig")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(20.0),
                                           height: getRelativeWidth(20.0), alignment: .center)

                                    .padding(.horizontal, getRelativeWidth(6.0))
                            }
                        }
                        Divider()
                            .frame(
                                height: getRelativeHeight(1.0), alignment: .leading)
                            .background(ColorConstants.Cyan80035)
                            .padding(.horizontal, getRelativeWidth(10.0))
                        
                        HStack {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack {
                                    if viewModel.pageState == .loading() {
                                        ProgressView()
                                                
                                            .padding(.horizontal)
                                        Spacer()
                                    } else {
                                        if let timeSlotErrorMessage = viewModel.timeSlotErrorMessage {
                                            Text(timeSlotErrorMessage)
                                                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                                .fontWeight(.semibold)
                                                .foregroundColor(ColorConstants.Black50)
                                                .multilineTextAlignment(.leading)
                                                .padding(.horizontal, getRelativeWidth(10.0))
                                        } else {
                                            ForEach(Array(viewModel.vendorTimeslotModelList.enumerated()), id: \.element.id) { index, model in
                                                    
                                                Button(action: {
                                                    let bookingListCount = viewModel.bookingModel.orderDetails.count
                                                    let endIndex = viewModel.vendorTimeslotModelList.endIndex
                                                   
                                                    if bookingListCount > 1, index >= (endIndex - (bookingListCount - 1)) {
                                                        viewModel.updatePageState(.failure(error: "Please select a previous time"))
                                                    } else {
                                                        viewModel.updateTimeSlot(model)
                                                    }
                                                    
                                                }, label: {
                                                    Text(model.timeslot)
                                                        .font(FontScheme
                                                            .kNunitoSemiBold(size: getRelativeHeight(11.0)))
                                                        .fontWeight(.semibold)
                                                        .padding(.horizontal, getRelativeWidth(14.0))
                                                        .foregroundColor(
                                                            model.isDisabled ? ColorConstants.Bluegray102 :
                                                                viewModel.isSelectedTimeSlot(model) ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                                                        .multilineTextAlignment(.center)
                                                        .frame(width: 80.0.relativeWidth,
                                                               height: getRelativeHeight(29.0),
                                                               
                                                               alignment: .center)
                                                        .if(model.isDisabled, transform: {
                                                            $0.overlay {
                                                                Divider()
                                                                        
                                                                    .rotationEffect(.degrees(70))
                                                                    .frame(width: 2, height: 80.0.relativeWidth)
                                                            }
                                                        })
                                                        .background(
                                                            model.isDisabled ?
                                                                ColorConstants.Gray5003f1
                                                                .clipShape(.capsule) :
                                                                viewModel.isSelectedTimeSlot(model) ? ColorConstants.Cyan800.clipShape(.capsule) : ColorConstants.Cyan80035.clipShape(.capsule))
                                                        .overlay(Capsule().stroke(
                                                            model.isDisabled ? ColorConstants.Bluegray101 :
                                                                ColorConstants.Cyan800,
                                                            lineWidth: 1))
                                                        .clipShape(.capsule)
                                                            
                                                })
                                                .if(viewModel.isSelectedTimeSlot(model), transform: {
                                                    $0.matchedGeometryEffect(id: "time.slot", in: animation)
                                                })
                                                .disabled(model.isDisabled)
                                                .padding(2)
                                            }
                                            .onChange(of: viewModel.selectedTimeSlot) { _, newValue in
                                                var data = appState.selectedBookingRequestModel
                                                data?.time = newValue?.timeslot
                                                appState.updateBookingRequestModelList(data)
                                            }
                                        }
                                    }
                                }
                            }
//                            .scrollDisabled(true)
                            .scrollClipDisabled()
                            .padding(.top, getRelativeHeight(12.0))
                            .padding(.leading, getRelativeWidth(7.0))
                            .frame(height: 50.0.relativeHeight)
                            
//                            WrappingHStack(viewModel.vendorTimeslotModelList, id: \.self) { time in
//
//                                Button(action: {
//                                    viewModel.updateTimeSlot(time)
//                                }, label: {
//                                    Text(StringConstants.kLbl0900Am)
//                                        .font(FontScheme
//                                            .kNunitoSemiBold(size: getRelativeHeight(11.0)))
//                                        .fontWeight(.semibold)
//                                        .padding(.horizontal, getRelativeWidth(14.0))
//                                        .foregroundColor(
//                                            index == 2 ? ColorConstants.Bluegray102 :
//                                                viewModel.isSelectedTimeSlot(time) ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
//                                        .multilineTextAlignment(.center)
//                                        .frame(width: 80.0.relativeWidth,
//                                               height: getRelativeHeight(29.0),
//
//                                               alignment: .center)
//
//                                        .if(index == 2, transform: {
//                                            $0.overlay {
//                                                Divider()
//
//                                                    .rotationEffect(.degrees(70))
//                                                    .frame(width: 2, height: 80.0.relativeWidth)
//                                            }
//                                        })
//                                        .background(
//                                            index == 2 ?
//                                                ColorConstants.Gray5003f1
//                                                .clipShape(.capsule) :
//                                                viewModel.isSelectedTimeSlot(index) ? ColorConstants.Cyan800.clipShape(.capsule) : ColorConstants.Cyan80035.clipShape(.capsule))
//                                        .overlay(Capsule().stroke(
//                                            index == 2 ? ColorConstants.Bluegray101 :
//                                                ColorConstants.Cyan800,
//                                            lineWidth: 1))
//                                        .clipShape(.capsule)
//
//                                })
//                                .if(viewModel.isSelectedTimeSlot(index), transform: {
//                                    $0.matchedGeometryEffect(id: "time.slot", in: animation)
//                                })
//                                .disabled(index == 2)
//                                .padding(2)
//                            }
                        }
                        
                        .padding(.top, getRelativeHeight(12.0))
                        .padding(.leading, getRelativeWidth(7.0))
                    }

                    .padding(.leading, getRelativeWidth(6.0))
                    .padding(.trailing, getRelativeWidth(10.0))
                }
             
//                        Spacer()
                Button(action: {
                    self.viewModel.updatePopUpType(nil)
                    self.viewModel.submittedTimeSlot = viewModel.selectedTimeSlot
                    
                    self.updateSlots()
                        
                }, label: {
                    HStack(spacing: 0) {
                        Text("Submit")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5,
                                                       bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.bottom, AppConstants.tabBarHeight.relativeHeight + 16.0.relativeHeight)
                            .padding(.horizontal, getRelativeWidth(21.0))
                    }
                })
            }
            .padding([.top])
            .frame(maxWidth: .infinity)
            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                .fill(ColorConstants.WhiteA700))
            .clipped()
            .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom),
            removal: .opacity // Use opacity for removal
        ))
    }
    
    func updateSlots() {
        let tempList = updateTimeSlotWithAutoAssignFollowingSlots(list: viewModel.vendorTimeslotModelList, selected: viewModel.selectedTimeSlot, bookingCount: viewModel.bookingModel.orderDetails.count)
        
//        viewModel.bookingHour = tempList.count > 1 ? "\(tempList.first?.timeslot ?? "") - \(tempList.last?.timeslot ?? "")" : "\(tempList.first?.timeslot ?? "")"
        viewModel.bookingHour = tempList.count > 1 ? "\(tempList.first?.timeslot ?? "")" : "\(tempList.first?.timeslot ?? "")"
    }
}

// struct CancelmodifyView_Previews: PreviewProvider {
//    static var previews: some View {
//        NavigationStack {
//            CancelModifyView().attachAllEnvironmentObjects()
//        }
//    }
// }

func updateTimeSlotWithAutoAssignFollowingSlots(list: [VendorTimeslotModel.TimeSlot], selected: VendorTimeslotModel.TimeSlot?, bookingCount: Int) -> [VendorTimeslotModel.TimeSlot] {
    var tempList: [VendorTimeslotModel.TimeSlot] = []
    if let itemIndex = list.firstIndex(where: { $0 == selected }) {
        for item in list.enumerated() {
            let index = item.offset
            let element = item.element
            if !element.isDisabled, index >= itemIndex, tempList.count < (bookingCount + 1) {
                tempList.append(element)
            }
        }
    }
    return tempList
}
