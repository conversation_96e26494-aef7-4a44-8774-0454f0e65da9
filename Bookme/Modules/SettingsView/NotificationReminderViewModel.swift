
//  NotificationReminderView.swift
//  Bookme
//
//  Created by Apple on 13/01/2025.
//

import SwiftUI

class NotificationReminderViewModel: SuperViewModel {
    @Published var isReminderEnabled: Bool = true
    @Published var reminderHour: Int? = 1
    @Published var reminderNotificationModel: ReminderNotificationModel?
    let notificationRemainderList: [NotificationHourModel] = [
        .init(hour: 1, title: "1 Hour"),
        .init(hour: 2, title: "2 Hours"),
        .init(hour: 3, title: "3 Hours"),
    ]
    
    override init() {
        super.init()
        
        readReminderHour()
    }
    
    func onChangeReminderEnabled(oldValue: Bool, newValue: Bool) {
        reminderHour = newValue ? reminderNotificationModel?.hours : nil
    }
    
    func updateReminderHour(_ value: Int?) {
        reminderHour = value
        onDisappear()
    }
    
    func onDisappear() {
        DispatchQueue.debounce(delay: 1, key: "hour.update") {
            self.updateReminderHour()
        }
    }
    
    func updateReminderHour() {
        guard reminderHour != reminderNotificationModel?.hours || isReminderEnabled != reminderNotificationModel?.isEnabled else { return }
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": userID, "hours": reminderHour ?? reminderNotificationModel?.hours ?? 1, "reminderenable": isReminderEnabled.toInt]
        onApiCall(api.reminderNotificationUpdate, parameters: parameters, withLoadingIndicator: false) { _ in
        }
    }
    
    func readReminderHour() {
        guard let userID = AppState.userModel?.user.id else { return }
        onApiCall(api.reminderNotificationRead, parameters: ["user_id": userID]) {
            self.reminderNotificationModel = $0.data?.first
            self.reminderHour = self.reminderNotificationModel?.hours
            self.isReminderEnabled = self.reminderNotificationModel?.reminderEnable == 1
        }
    }
}

struct NotificationHourModel: Identifiable {
    let id: UUID = .init()
    let hour: Int
    let title: String
}

extension DispatchQueue {
    private static var debounceTracker = [String: DispatchWorkItem]()

    static func debounce(
        delay: TimeInterval,
        key: String = UUID().uuidString,
        action: @escaping () -> Void
    ) {
        debounceTracker[key]?.cancel()
        
        let workItem = DispatchWorkItem(block: action)
        debounceTracker[key] = workItem
        
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
    }
}
