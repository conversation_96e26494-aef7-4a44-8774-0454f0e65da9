import SwiftUI

struct SettingsView: View {
    @StateObject var settingsViewModel = SettingsViewModel()
    @Environment(\.dismiss) var dismiss: DismissAction
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @EnvironmentObject var appState: AppState
    var body: some View {
        SuperView(pageState: $settingsViewModel.pageState) {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Settings") {
                VStack(spacing: 10.0.relativeHeight) {
                  
                    
                    
                    Button(action: {
                        routerManager.push(to: .serviceFor, where: .myAccountRoute)
                    }, label: {
                        HStack {
                            HStack {
                                Image("service.for")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(24.0), height: getRelativeHeight(24.0),
                                           alignment: .center)
                                       
                                    .clipped()
                                Text("Show me service for")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    
                                    .multilineTextAlignment(.leading)
                                    .frame( height: getRelativeHeight(18.0),
                                           alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(17.0))
                            }
                            .padding(.leading, getRelativeWidth(4.0))
                                
                            Spacer()
                            Image("img_arrowright_cyan_800")
                                .resizable()
                                .scaledToFit()
                                .rotateBasedOnLanguage()
                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                       alignment: .center)
                                   
                                .clipped()
                        }
                            
                        .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                        .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                               alignment: .center)
                        .padding(.bottom, getRelativeWidth(6.0))
                            
                    })
                    .overlay(
                        Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                        alignment: .bottom)
                    
                    
                    Button(action: {
                        routerManager.push(to: .notificationSettings, where: .myAccountRoute)
                    }, label: {
                        HStack {
                            HStack {
                                Image("img_usericon_cyan_800")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(16.0), height: getRelativeHeight(24.0),
                                           alignment: .center)
                                       
                                    .clipped()
                                Text("Notification Settings")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    
                                    .multilineTextAlignment(.leading)
                                    .frame( height: getRelativeHeight(18.0),
                                           alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(17.0))
                            }
                            .padding(.leading, getRelativeWidth(4.0))
                                
                            Spacer()
                            Image("img_arrowright_cyan_800")
                                .resizable()
                                .scaledToFit()
                                .rotateBasedOnLanguage()
                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                       alignment: .center)
                                   
                                .clipped()
                        }
                            
                        .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                        .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                               alignment: .center)
                        .padding(.bottom, getRelativeWidth(6.0))
                            
                    })
                    .overlay(
                        Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                        alignment: .bottom)
                        
                    Button(action: {
                        settingsViewModel.updateLanguagePopup()
                    }, label: {
                        HStack {
                            HStack {
                                Image(.languageSettings)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(20.0), height: getRelativeHeight(24.0),
                                           alignment: .center)
                                       
                                    .clipped()
                                Text("Language")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                   
                                    .multilineTextAlignment(.leading)
                                    .frame( height: getRelativeHeight(18.0),
                                           alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(17.0))
                            }
                            .padding(.leading, getRelativeWidth(4.0))
                                
                            Spacer()
                            Image("img_arrowright_cyan_800")
                                .resizable()
                                .scaledToFit()
                                .rotateBasedOnLanguage()
                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                       alignment: .center)
                                   
                                .clipped()
                        }
                            
                        .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                        .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                               alignment: .center)
                        .padding(.bottom, getRelativeWidth(6.0))
                            
                    })
                    .overlay(
                        Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                        alignment: .bottom)
                    Button(action: {
                        routerManager.push(to: .passwordManager, where: .myAccountRoute)
                    }, label: {
                        HStack {
                            HStack {
                                Image("img_password")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(24.0), height: getRelativeHeight(24.0),alignment: .center)
                                    .clipped()
                                Text("Password Manager")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                   
                                    .multilineTextAlignment(.leading)
                                    .frame( height: getRelativeHeight(18.0),
                                           alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(8.0))
                            }
                                
                            .padding(.leading, getRelativeWidth(4.0))
                            Spacer()
                            Image("img_arrowright_cyan_800")
                                .resizable()
                                .scaledToFit()
                                .rotateBasedOnLanguage()
                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                       alignment: .center)
                                   
                                .clipped()
                        }
                        .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                               alignment: .center)
                        .padding(.bottom, getRelativeWidth(6.0))
                    })
                    .overlay(Divider().frame(height: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                    .visibility(AppState.isLoggedIn && !AppState.isPasskeyLoggedIn ? .visible : .gone)
                        
                    Button(action: {
                        settingsViewModel.onDeleteAccount()
                    }, label: {
                        HStack {
                            HStack {
                                Image("img_deleteuser")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(22.0),
                                           alignment: .center)
                                       
                                    .clipped()
                                Text("Delete Account")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    
                                    .multilineTextAlignment(.leading)
                                    .frame( height: getRelativeHeight(18.0),
                                           alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(12.0))
                            }
                                
                            .padding(.leading, getRelativeWidth(4.0))
                            Spacer()
                            Image("img_arrowright_cyan_800")
                                .resizable()
                                .scaledToFit()
                                .rotateBasedOnLanguage()
                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                       alignment: .center)
                                   
                                .clipped()
                        }
                        .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                               alignment: .center)
                        .padding(.bottom, getRelativeWidth(6.0))
                    })
                    .visibility(AppState.isLoggedIn ? .visible : .gone)
                    .alert(isPresented: $settingsViewModel.showAlert) {
                        Alert(title: Text("Warning!"), message: Text("Are you sure you want delete your account permanently?"), primaryButton: .destructive(Text("Yes"), action: {
                            settingsViewModel.deleteAccount {
                                self.appState.onLogout {
                                    appState.onAuthentication(nil)
                                    routerManager.popToRoot(where: .myAccountRoute)
                                    let route = routerManager.mapRouterWithTab(appState: appState)
                                    routerManager.push(to: .signIn(type: route), where: route)
                                }
                            }
                        }), secondaryButton: .default(Text("Cancel")))
                    }
                    //                    .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f).frame(maxHeight: 1), alignment: .bottom)
                }
                .padding(.vertical, getRelativeHeight(17.0))
            }
        }
        .overlay(alignment: .bottom) {
            Group {
                if settingsViewModel.showLanguagePopup {
                    SlideUpAnimationContainerView {} content: {
                        LanguageView(onSubmit: { value in
                            
                            settingsViewModel.updateLanguagePopup()
                            
                            if let value = value {
                                appState.updateSelectedTab(.home)
                                routerManager.popToAllRoot()
                                appState.updateLocale(value,reset: true)
                                settingsViewModel.updateLanguage(value.rawValue)
                            } else {
                                
                            }
                        })
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
                        .transition(.move(edge: .bottom))
                        .ignoresSafeArea(.container, edges: .bottom)
                        //                        .padding(AppConstants.tabBarHeight)
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
            .animation(.easeOut, value: settingsViewModel.showLanguagePopup)
        }
      
        .background(ColorConstants.bgGradient)
    }
}

struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            SettingsView()
                .attachAllEnvironmentObjects()
        }
    }
}
