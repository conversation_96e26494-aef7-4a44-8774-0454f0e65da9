//
//  UserNotificationSettingsView.swift
//  Bookme
//
//  Created by Apple on 28/10/2024.
//

import SwiftUI

struct UserNotificationSettingsView: View {
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    
    @StateObject private var viewModel: SettingsViewModel = .init()
    
    @State private var showConfirmationAlert: Bool = false
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {} content: {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Notification Settings") {
                VStack {
                    if viewModel.pageState == .loading(true) {
                        UserNotificationSettingsShimmerView()
                            .shimmerize()
                    } else {
                        VStack {
                            Button(action: {
                                viewModel.isPushNotificationEnabled.toggle()
                            }, label: {
                                HStack {
                                    HStack {
                                        Image(.pushNotification)
                                            //                                .renderingMode(.template)
                                            .resizable()
                                            .scaledToFit()
                                            //                                .foregroundStyle(isNotificationEnabled ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                                            .foregroundStyle(ColorConstants.Cyan900)
                                            .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(25.0),
                                                   alignment: .center)
                                               
                                            .clipped()
                                        Text("Push Notification")
                                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                            .fontWeight(.regular)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.leading, getRelativeWidth(5.0))
                                    }
                                    .padding(.leading, getRelativeWidth(4.0))
                                        
                                    Spacer()
                                        
                                    Toggle(isOn: $viewModel.isPushNotificationEnabled) {
                                        EmptyView()
                                    }
                                    .tint(ColorConstants.Cyan900)
                                    .labelsHidden()
                                }
                                    
                                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                                       alignment: .center)
                                .padding(.bottom, getRelativeWidth(6.0))
                                    
                            })
                            .overlay(
                                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                                alignment: .bottom)
                            .visibility(AppState.isLoggedIn ? .visible : .gone)
                                
                            Button(action: {
                                viewModel.isEmailNotificationEnabled.toggle()
                            }, label: {
                                HStack {
                                    HStack {
                                        Image(.emailNotification)
                                            //                                .renderingMode(.template)
                                            .resizable()
                                            .scaledToFit()
                                            //                                .foregroundStyle(isNotificationEnabled ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                                            .foregroundStyle(ColorConstants.Cyan900)
                                            .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(25.0),
                                                   alignment: .center)
                                               
                                            .clipped()
                                        Text("Email Notification")
                                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                            .fontWeight(.regular)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.leading, getRelativeWidth(5.0))
                                    }
                                    .padding(.leading, getRelativeWidth(4.0))
                                        
                                    Spacer()
                                        
                                    Toggle(isOn: $viewModel.isEmailNotificationEnabled) {
                                        EmptyView()
                                    }
                                    .tint(ColorConstants.Cyan900)
                                    .labelsHidden()
                                }
                                    
                                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                                       alignment: .center)
                                .padding(.bottom, getRelativeWidth(6.0))
                                    
                            })
                            .overlay(
                                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                                alignment: .bottom)
                            .visibility(AppState.isLoggedIn ? .visible : .gone)
                                
                            Button(action: {
                                viewModel.isSMSNotificationEnabled.toggle()
                            }, label: {
                                HStack {
                                    HStack {
                                        Image(.smsNotification)
                                            //                                .renderingMode(.template)
                                            .resizable()
                                            .scaledToFit()
                                            //                                .foregroundStyle(isNotificationEnabled ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                                            .foregroundStyle(ColorConstants.Cyan900)
                                            .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(22.0),
                                                   alignment: .center)
                                               
                                            .clipped()
                                        Text("SMS Notification")
                                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                            .fontWeight(.regular)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.leading, getRelativeWidth(7.0))
                                    }
                                    .padding(.leading, getRelativeWidth(4.0))
                                        
                                    Spacer()
                                        
                                    Toggle(isOn: $viewModel.isSMSNotificationEnabled) {
                                        EmptyView()
                                    }
                                    .tint(ColorConstants.Cyan900)
                                    .labelsHidden()
                                }
                                    
                                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                                       alignment: .center)
                                .padding(.bottom, getRelativeWidth(6.0))
                                    
                            })
                            .overlay(
                                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                                alignment: .bottom)
                            .visibility(AppState.isLoggedIn ? .visible : .gone)
                            
                            
                            Button(action: {
                                let routesType: RoutesType = routerManager.mapRouterWithTab(appState: appState)
                                routerManager.push(to: .notificationReminder, where: routesType)
                            }, label: {
                                HStack {
                                    HStack {
                                        Image("reminder")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: getRelativeWidth(24.0), height: getRelativeHeight(24.0),
                                                   alignment: .center)
                                               
                                            .clipped()
                                        Text("Notification Reminder")
                                            
                                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                            .fontWeight(.regular)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.leading, getRelativeWidth(13.0))
                                    }
                                    .padding(.leading, getRelativeWidth(4.0))
                                        
                                    Spacer()
                                    Image("img_arrowright_cyan_800")
                                        .resizable()
                                        .scaledToFit()
                                        .rotateBasedOnLanguage()
                                        .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                               alignment: .center)
                                           
                                        .clipped()
                                }
                                    
                                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                                       alignment: .center)
                                .padding(.bottom, getRelativeWidth(6.0))
                                    
                            })
                            .overlay(
                                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                                alignment: .bottom)
                            .visibility(AppState.isLoggedIn ? .visible : .gone)
                                
                            Button(action: {
                                showConfirmationAlert.toggle()
                            }, label: {
                                HStack {
                                    HStack {
                                        Image("img_subtract")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: getRelativeWidth(16.0), height: getRelativeHeight(24.0),
                                                   alignment: .center)
                                               
                                            .clipped()
                                        Text("System Settings")
                                            
                                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                            .fontWeight(.regular)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.leading, getRelativeWidth(13.0))
                                    }
                                    .padding(.leading, getRelativeWidth(4.0))
                                        
                                    Spacer()
                                    Image("img_arrowright_cyan_800")
                                        .resizable()
                                        .scaledToFit()
                                        .rotateBasedOnLanguage()
                                        .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                               alignment: .center)
                                           
                                        .clipped()
                                }
                                    
                                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                                       alignment: .center)
                                .padding(.bottom, getRelativeWidth(6.0))
                                    
                            })
                            .overlay(
                                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                                alignment: .bottom)
                        }
                    }
                }
                .padding(.vertical, getRelativeHeight(17.0))
                .onChange(of: viewModel.isPushNotificationEnabled, viewModel.onChangeNotificationData)
                .onChange(of: viewModel.isEmailNotificationEnabled) { oldValue, newValue in
                    if
                        let email = AppState.userModel?.user.email,
                        !email.isEmpty
                    {
                        viewModel.onChangeNotificationData(oldValue, newValue)
                            
                    } else {
                        if newValue {
                            viewModel.isEmailNotificationEnabled = false
                            viewModel.updatePageState(.message(config: .init(title: "Error!", text: "Your account is not registered with Email Address. Please update your email address in \"Profile Screen\" to enable Email notifications.")))
                        }
                    }
                }
                    
                .onChange(of: viewModel.isSMSNotificationEnabled) { oldValue, newValue in
                        
                    if
                        let phone = AppState.userModel?.user.userPhone,
                        !phone.isEmpty
                            
                    {
                        viewModel.onChangeNotificationData(oldValue, newValue)
                            
                    } else {
                        if newValue {
                            viewModel.isSMSNotificationEnabled = false
                            viewModel.updatePageState(.message(config: .init(title: "Error!", text: "Your account is not registered with Mobile number. Please update your mobile number in \"Profile Screen\" to enable SMS notifications.")))
                        }
                    }
                }
                   
                .onLoad(perform: viewModel.getNotificationData)
                    
                //            .onChange(of: viewModel.isPushNotificationEnabled) { _, newValue in
                //                handleNotificationToggleChange(newValue: newValue)
                //            }
                .alert("System Settings", isPresented: $showConfirmationAlert) {
                    Button("Cancel", role: .cancel) {}
                    Button("Settings") {
                        openAppSettings()
                    }
                } message: {
                    Text("To manage your application notifications from the system settings, please go to Settings > BookMe > Notifications")
                }
            }
        }
        
        .background(ColorConstants.bgGradient)
    }
    
    private func openAppSettings() {
        if let appSettings = URL(string: UIApplication.openSettingsURLString) {
            if UIApplication.shared.canOpenURL(appSettings) {
                UIApplication.shared.open(appSettings, options: [:], completionHandler: nil)
            }
        }
    }
    
    // Function to handle changes in notification setting
    private func handleNotificationToggleChange(newValue: Bool) {
        if newValue {
            enableNotifications()
        } else {
            disableNotifications()
        }
    }
    
    // Enable notifications by requesting authorization
    private func enableNotifications() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, _ in
            if granted {
                Utilities.enQueue {
                    UIApplication.shared.registerForRemoteNotifications()
                }
                
            } else {
                print("Push notification authorization was not granted.")
                viewModel.isPushNotificationEnabled = false
            }
        }
    }
        
    // Disable notifications by unregistering for remote notifications
    private func disableNotifications() {
        Utilities.enQueue {
            UIApplication.shared.unregisterForRemoteNotifications()
        }
    }
}

#Preview {
    NavigationStack {
        UserNotificationSettingsView().attachAllEnvironmentObjects()
    }
}

struct UserNotificationSettingsShimmerView: View {
    var body: some View {
        VStack {
            Button(action: {}, label: {
                HStack {
                    HStack {
                        Image(.pushNotification)
//                                .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
//                                .foregroundStyle(isNotificationEnabled ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                            .foregroundStyle(ColorConstants.Cyan900)
                            .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(25.0),
                                   alignment: .center)
                           
                            .clipped()
                        Text("Push Notification")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .frame(height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(5.0))
                    }
                    .padding(.leading, getRelativeWidth(4.0))
                    
                    Spacer()
                    
                    Toggle(isOn: .constant(false)) {
                        EmptyView()
                    }
                    .tint(ColorConstants.Cyan900)
                    .labelsHidden()
                }
                
                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                       alignment: .center)
                .padding(.bottom, getRelativeWidth(6.0))
                
            })
            .overlay(
                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                alignment: .bottom)
            .visibility(AppState.isLoggedIn ? .visible : .gone)
            
            Button(action: {}, label: {
                HStack {
                    HStack {
                        Image(.emailNotification)
//                                .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
//                                .foregroundStyle(isNotificationEnabled ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                            .foregroundStyle(ColorConstants.Cyan900)
                            .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(25.0),
                                   alignment: .center)
                           
                            .clipped()
                        Text("Email Notification")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .frame(height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(5.0))
                    }
                    .padding(.leading, getRelativeWidth(4.0))
                    
                    Spacer()
                    
                    Toggle(isOn: .constant(false)) {
                        EmptyView()
                    }
                    .tint(ColorConstants.Cyan900)
                    .labelsHidden()
                }
                
                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                       alignment: .center)
                .padding(.bottom, getRelativeWidth(6.0))
                
            })
            .overlay(
                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                alignment: .bottom)
            .visibility(AppState.isLoggedIn ? .visible : .gone)
            
            Button(action: {}, label: {
                HStack {
                    HStack {
                        Image(.smsNotification)
//                                .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
//                                .foregroundStyle(isNotificationEnabled ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                            .foregroundStyle(ColorConstants.Cyan900)
                            .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(22.0),
                                   alignment: .center)
                           
                            .clipped()
                        Text("SMS Notification")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .frame(height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(7.0))
                    }
                    .padding(.leading, getRelativeWidth(4.0))
                    
                    Spacer()
                    
                    Toggle(isOn: .constant(false)) {
                        EmptyView()
                    }
                    .tint(ColorConstants.Cyan900)
                    .labelsHidden()
                }
                
                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                       alignment: .center)
                .padding(.bottom, getRelativeWidth(6.0))
                
            })
            .overlay(
                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                alignment: .bottom)
            .visibility(AppState.isLoggedIn ? .visible : .gone)
            
            Button(action: {}, label: {
                HStack {
                    HStack {
                        Image("img_subtract")
                            .resizable()
                            .scaledToFit()
                            .frame(width: getRelativeWidth(16.0), height: getRelativeHeight(24.0),
                                   alignment: .center)
                           
                            .clipped()
                        Text("System Settings")
                        
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .frame(height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(13.0))
                    }
                    .padding(.leading, getRelativeWidth(4.0))
                    
                    Spacer()
                    Image("img_arrowright_cyan_800")
                        .resizable()
                        .scaledToFit()
                        .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                               alignment: .center)
                       
                        .clipped()
                }
                
                .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                       alignment: .center)
                .padding(.bottom, getRelativeWidth(6.0))
                
            })
            .overlay(
                Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                alignment: .bottom)
        }
        .padding(.vertical, getRelativeHeight(17.0))
    }
}

extension UIScreen {
    static var screenWidth: CGFloat {
        return UIScreen.main.bounds.width
    }

    static var screenHeight: CGFloat {
        return UIScreen.main.bounds.height
    }

    static var screenSize: CGSize {
        return UIScreen.main.bounds.size
    }
}
