//
//  ReminderNotificationModel.swift
//  Bookme
//
//  Created by Apple on 14/01/2025.
//

import Foundation

// MARK: - ReminderNotificationModel
struct ReminderNotificationModel: Codable {
    let reminderEnable, hours: Int
    
    
    var isEnabled: Bool {
        reminderEnable == 1
    }
    
    enum CodingKeys: String, CodingKey {
        case reminderEnable = "reminderenable"
        case hours
    }
}


