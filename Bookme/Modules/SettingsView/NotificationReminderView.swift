//
//  NotificationReminderView.swift
//  Bookme
//
//  Created by Apple on 13/01/2025.
//

import SwiftUI

struct NotificationReminderView: View {
    @StateObject var viewModel: NotificationReminderViewModel = .init()
    var body: some View {
        
        SuperView(pageState: $viewModel.pageState) {} content: {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Notification Reminder") {
                VStack(spacing: 0) {
                    Button(action: {
                        viewModel.isReminderEnabled.toggle()
                    }, label: {
                        HStack {
                            HStack {
                                Image("reminder")
                                    //                                .renderingMode(.template)
                                    .resizable()
                                    .scaledToFit()
                                    //                                .foregroundStyle(isNotificationEnabled ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                                    .foregroundStyle(ColorConstants.Cyan900)
                                    .frame(width: getRelativeWidth(24.0), height: getRelativeHeight(24.0),
                                           alignment: .center)
                                       
                                    .clipped()
                                Text("Push Notification")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .multilineTextAlignment(.leading)
                                    .frame(height: getRelativeHeight(18.0),
                                           alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(5.0))
                            }
                            .padding(.leading, getRelativeWidth(4.0))
                                
                            Spacer()
                                
                            Toggle(isOn: $viewModel.isReminderEnabled) {
                                EmptyView()
                            }
                            .tint(ColorConstants.Cyan900)
                            .labelsHidden()
                        }
                            
                        .overlay(Divider().frame(maxHeight: 1).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)), alignment: .bottom)
                        .frame(width: getRelativeWidth(357.0), height: getRelativeHeight(46.0),
                               alignment: .center)
                        .padding(.bottom, getRelativeWidth(6.0))
                            
                    })
                    .overlay(
                        Divider().frame(maxHeight: 0.5).overlay(ColorConstants.Cyan8007f.frame(maxHeight: 1)),
                        alignment: .bottom)
                    .onChange(of: viewModel.isReminderEnabled, viewModel.onChangeReminderEnabled)
                    
                    VStack(alignment: .leading, spacing: 0) {
                        ForEach(viewModel.notificationRemainderList) { index in
                            
                            let selectedReminderHour = index.hour == viewModel.reminderHour
                            
                            Button {
                                viewModel.updateReminderHour(index.hour)
                            } label: {
                                HStack(spacing: 18.relativeWidth) {
                                    ZStack {}
                                        .hideNavigationBar()
                                        .frame(width: getRelativeWidth(13.0),
                                               height: getRelativeWidth(13.0),
                                               alignment: .center)
                                        .background(Circle().fill(selectedReminderHour ? ColorConstants.Cyan800 : .clear))
                                        .padding(.all, getRelativeWidth(3.0))
                                        .frame(width: getRelativeWidth(21.0),
                                               height: getRelativeWidth(21.0), alignment: .center)
                                        .overlay(Circle()
                                            .stroke(ColorConstants.Cyan800,
                                                    lineWidth: 1))
                                        .background(Circle()
                                            .fill(Color.clear.opacity(0.7)))
                                        .padding(.leading, getRelativeWidth(9.0))
                                    
                                    Text(LocalizedStringKey(index.title))
                                   
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(15.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                        .multilineTextAlignment(.leading)
                                        .padding(.top, getRelativeHeight(20.24))
                                        .padding(.bottom, getRelativeHeight(18.76))
                                }
                                
                                .frame(width: getRelativeWidth(361.0),
                                       height: getRelativeHeight(56.0), alignment: .leading)
//                                    
                            }.frame(maxWidth: .infinity)
//                            
                            Divider()
                                .padding(.horizontal, -16)
                                .background(ColorConstants.Cyan8007f)
                        }
                    }
                    
                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                               bottomRight: 13.0).fill(.clear))
                    .padding(.horizontal, getRelativeWidth(21.0))
                    .disableWithOpacity(!viewModel.isReminderEnabled)
                }
                .padding()
            }
            .shimmerize(viewModel.pageState == .loading(true))
        }
       
        .background(ColorConstants.bgGradient)
        
       
    }
        
}

#Preview {
    NavigationStack {
        NotificationReminderView()
            .attachAllEnvironmentObjects()
    }
}
