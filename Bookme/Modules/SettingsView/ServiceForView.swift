//
//  ServiceForView.swift
//  Bookme
//
//  Created by Apple on 14/01/2025.
//

import SwiftUI

struct ServiceForView: View {
    @EnvironmentObject private var appState: AppState
    @State private var serviceType: [ServiceType] = []
    
    var body: some View {
        MainScrollBody(invertColor: false, backButtonWithTitle: "Show me service for") {
            VStack(alignment: .leading, spacing: 0) {
                ForEach(ServiceType.allCases) { type in
                    let selectedServiceType = self.serviceType.contains(type)
                    Button {
                        if serviceType.contains(type) {
                            if serviceType.count > 1 {serviceType.removeAll { $0 == type }}
                        }else{
                            serviceType.append(type)
                        }
                    } label: {
                         HStack(spacing: 18.relativeWidth) {
                            ZStack {}
                                .hideNavigationBar()
                                .frame(width: getRelativeWidth(13.0),
                                       height: getRelativeWidth(13.0),
                                       alignment: .center)
                                .background(Circle().fill(selectedServiceType ? ColorConstants.Cyan800 : .clear))
                                .padding(.all, getRelativeWidth(3.0))
                                .frame(width: getRelativeWidth(21.0),
                                       height: getRelativeWidth(21.0), alignment: .center)
                                .overlay(Circle()
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                                .background(Circle()
                                    .fill(Color.clear.opacity(0.7)))
                                .padding(.leading, getRelativeWidth(9.0))
                                
                            Text(LocalizedStringKey(type.title))
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(15.0)))
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.leading)
                                .padding(.top, getRelativeHeight(20.24))
                                .padding(.bottom, getRelativeHeight(18.76))
                        }
                         .frame(width: getRelativeWidth(361.0), height: getRelativeHeight(56.0), alignment: .leading)
                    }.frame(maxWidth: .infinity)
                         Divider()
                        .padding(.horizontal, -16)
                        .background(ColorConstants.Cyan8007f)
                }
            }
                
            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                       bottomRight: 13.0).fill(.clear))
            .padding(.horizontal, getRelativeWidth(21.0))
            .padding()
        }
        .background(ColorConstants.bgGradient)
        .onAppear {
            self.serviceType = appState.serviceType
        }
        .onDisappear {
            guard appState.serviceType != serviceType else { return }
            
            appState.updateServiceType(serviceType, reset: true)
        }
    }
}
