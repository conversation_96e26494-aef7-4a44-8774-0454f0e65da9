//
//  LanguageView.swift
//  Bookme
//
//  Created by Apple on 11/12/2024.
//

import SwiftUI

struct LanguageView: View {
    let onSubmit: (_ language: AppLanguageType?) -> Void

    @Namespace private var animation

    @EnvironmentObject private var appState: AppState
    @State private var selectedAppLanguage: AppLanguageType?
    
    
    var body: some View {
        VStack(alignment: .center, spacing: 0) {
            headerView
            firstPageView
        }
        .frame(maxWidth: .infinity)
        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0).fill(ColorConstants.WhiteA700))
        .clipped()
        .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
//        .padding(.bottom, appState.isCustomBottomBarHidden ? 0 : AppConstants.tabBarHeightWithPadding * 1.9)
        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0).fill(ColorConstants.WhiteA700))
    }

    var headerView: some View {
        Group {
            HStack {
                Text("Choose your Language")
                    .font(Font.custom("Nunito", size: 16).weight(.bold))
                    .multilineTextAlignment(.center)
                    .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                    .frame(maxWidth: .infinity)
            }

            .padding(.vertical)
            .padding(.horizontal, getRelativeWidth(16.0))

            Divider()
                .frame(height: getRelativeHeight(1.0),
                       alignment: .center)
                .background(ColorConstants.Cyan8003f)
                .padding(.bottom, getRelativeWidth(8.0))
                .padding(.horizontal, getRelativeWidth(21.0))
        }
    }

    var firstPageView: some View {
        VStack(alignment: .trailing, spacing: 0) {
            VStack(alignment: .leading, spacing: 0) {
                ForEach(AppLanguageType.allCases) { type in

                    Button {
//                        appState.updateLocale(type)
                        selectedAppLanguage = type
                    } label: {
                        
                        
                        HStack(spacing: 18.relativeWidth){
                            
                            Text(type.title.capitalized.capitalized)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(15.0)))
                                .fontWeight(.bold)
                                .foregroundColor(.black)
                                .multilineTextAlignment(.leading)
                                .padding(.top, getRelativeHeight(20.24))
                                .padding(.bottom, getRelativeHeight(18.76))
                                
                            
                            
                            VStack {
                                ZStack {}
                                    .hideNavigationBar()
                                    .frame(width: getRelativeWidth(13.0),
                                           height: getRelativeWidth(13.0),
                                           alignment: .center)
                                    .background(Circle().fill(selectedAppLanguage == type ? ColorConstants.Cyan800 : ColorConstants.Cyan8003f))
                                    .padding(.all, getRelativeWidth(4.0))
                            }
                            .frame(width: getRelativeWidth(21.0),
                                   height: getRelativeWidth(21.0), alignment: .center)
                            .overlay(Circle()
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                            .background(Circle()
                                .fill(Color.clear.opacity(0.7)))
                            .padding(.leading, getRelativeWidth(9.0))
                        }
                        
                        
                            .frame(width: getRelativeWidth(361.0),
                                   height: getRelativeHeight(56.0), alignment: .trailing)
                            

                    }.frame(maxWidth: .infinity)
                    
                    Divider()
                        .padding(.trailing, -16)
                        .background(Color(red: 0.85, green: 0.85, blue: 0.85).opacity(1))
                }
            }
            .frame(width: getRelativeWidth(343.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                       bottomRight: 13.0).fill(.clear))
           
            .padding(.horizontal, getRelativeWidth(21.0))
            HStack {
                Button(action: {
                    onSubmit(nil)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Cancel")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .overlay(
                                Capsule()
                                    .inset(by: 0.5)
                                    .stroke(ColorConstants.Cyan800, lineWidth: 1)
                            )
                    }
                })

                Spacer()

                Button(action: {
                    onSubmit(selectedAppLanguage)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Done")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .background(Capsule()
                                .fill(ColorConstants.Cyan800))
                    }
                })
            }
            .frame(width: getRelativeWidth(347.0), height: getRelativeHeight(35.0),
                   alignment: .center)
            .padding(.vertical, getRelativeHeight(32.0))
            .padding(.top, getRelativeHeight(16.0))
            .padding(.horizontal, getRelativeWidth(21.0))
        }
        .onLoad {
            selectedAppLanguage = appState.appLanguage
        }
        .transition(.backslide)
    }
}

#Preview {
    LanguageView(onSubmit: { _ in })
        .attachAllEnvironmentObjects()
}



extension String {
    var localize:LocalizedStringKey { LocalizedStringKey(self) }
}
