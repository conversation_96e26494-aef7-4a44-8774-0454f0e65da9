import Foundation
import SwiftUI

class SettingsViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var showAlert: Bool = false
    @Published var notificationSettingsModel: NotificationSettingsModel?
    @Published var isPushNotificationEnabled: Bool = false
    @Published var isEmailNotificationEnabled: Bool = false
    @Published var isSMSNotificationEnabled: Bool = false
    @Published var showLanguagePopup: Bool = false
    
    override init() {
//        isSMSNotificationEnabled = true
        super.init()
        
       
    }

    func onDeleteAccount() {
        showAlert.toggle()
    }
    
    
    
    func updateLanguagePopup() {
        showLanguagePopup.toggle()
    }
    
    
    func updateLanguage(_ language: String) {
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: String] = ["user_id": "\(userID)", "language": "\(language)"]
        onApiCall(api.updateLanguage, parameters: parameters, withStateChange: false,withLoadingIndicator: false) { _ in }
    }

    func deleteAccount(completion: @escaping () -> Void) {
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": userID]
        onApiCall(api.deleteAccount, parameters: parameters) {
            if $0.success {
                completion()
            }
        }
    }

    func getNotificationData() {
//        updatePageState(.loading(true))

        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": userID]
        onApiCall(api.notificationSettingsRead, parameters: parameters) {
            self.notificationSettingsModel = $0.data?.first

            if let notificationSettingsModel = self.notificationSettingsModel {
                self.isPushNotificationEnabled = notificationSettingsModel.isPushNotificationEnabled
                self.isEmailNotificationEnabled = notificationSettingsModel.isEmailNotificationEnabled
                self.isSMSNotificationEnabled = notificationSettingsModel.isSmsNotificationEnabled
            }
        }
    }

    func onChangeNotificationData(_ oldValue: Bool, _ newValue: Bool) {
        DispatchQueue.debounce(id: "notificationDataChange", delay: 0.5) {
            guard oldValue != newValue else { return }
            self.updateNotificationSettings()
        }
    }

    func updateNotificationSettings() {

        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": userID, "emailnotification": isEmailNotificationEnabled.toInt, "smsnotification": isSMSNotificationEnabled.toInt, "pushnotification": isPushNotificationEnabled.toInt]

        onApiCall(api.notificationSettingsUpdate, parameters: parameters, withLoadingIndicator: false) { _ in
        }
    }
}

extension Bool {
    var toInt: Int {
        return self ? 1 : 0
    }
}

extension DispatchQueue {
    private static var workItems: [String: DispatchWorkItem] = [:]

    static func debounce(id: String, delay: TimeInterval, action: @escaping () -> Void) {
        // Cancel the previous task if it's still pending
        workItems[id]?.cancel()

        // Create a new task
        let task = DispatchWorkItem { action() }
        workItems[id] = task

        // Schedule the task after the specified delay
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: task)
    }
}
