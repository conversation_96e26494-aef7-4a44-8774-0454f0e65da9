import SwiftUI

struct HelpCenterView: View {
    @StateObject var viewModel = HelpCenterViewModel()
    @Environment(\.dismiss) var dismiss: DismissAction
    @Namespace private var animation
    var body: some View {
        MainScrollBody(invertColor: false,backButtonWithTitle: "Help Center")  {
            VStack(alignment: .leading, spacing: 0) {
                VStack(spacing: 0) {
                    HStack(alignment: .top, spacing: 0.0.relativeWidth) {
                        ForEach(viewModel.tabCategoryList, id: \.self) { item in
                            VStack {
                                Button(action: {
                                    viewModel.updateHeaderList(item)
                                }, label: {
                                    HStack(alignment: .top, spacing: 2) {
                                        //                                                Spacer()
                                        VStack(spacing: 2) {
                                            Text(item)
                                                .font(FontScheme
                                                    .kNunitoMedium(size: 14.0.relativeFontSize))
                                                .fontWeight(.bold)
                                                .foregroundColor(viewModel.checkHeader(item) ? ColorConstants.Cyan800 : ColorConstants.Cyan800.opacity(0.51))
                                                .multilineTextAlignment(.center)
                                                .frame(maxWidth: .infinity)
                                               
                                            if viewModel.checkHeader(item) {
                                                Divider()
                                                    .frame(
                                                        height: getRelativeHeight(2.0), alignment: .center)
                                                    .background(ColorConstants.Cyan800)
                                                    .padding(.horizontal, 22.0.relativeWidth)
                                                    .matchedGeometryEffect(id: "header.divider", in: animation)
                                            }
                                        }
                                        //                                                Spacer()
                                    }
                                })
                            }
                           
                            .frame(maxWidth: .infinity, alignment: .topLeading)
                        }
                    }
                    .frame(maxWidth: .infinity)
                       
                    Divider()
                        .frame(
                            height: getRelativeHeight(2.0), alignment: .leading)
                        .background(ColorConstants.Cyan800)
                }
               
                TabView(selection: $viewModel.selectedCategory.animation()) {
                    FAQView()
                        .tag(viewModel.tabCategoryList.first)

                    ContactUsView()
                        .tag(viewModel.tabCategoryList.last)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .multilineTextAlignment(.center)
                .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height * 0.86)
            }
        }
        
        .background(ColorConstants.bgGradient)
        .scrollDisabled(true)
    }
}



