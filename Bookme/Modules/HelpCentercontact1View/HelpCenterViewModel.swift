import Foundation
import SwiftUI



class HelpCenterViewModel: ObservableObject {
    @Published var nextScreen: String? = nil
    @Published var tabCategoryList: [String] = ["FAQ", "Contact Us"]
    @Published var selectedCategory: String?
    
    
    init(){
        selectedCategory = tabCategoryList.first
    }
    
    func checkHeader(_ value: String) -> Bool { selectedCategory == value  }
    
    func updateHeaderList(_ value: String){ withAnimation(.bouncy) {
        selectedCategory = value
    } }
}
