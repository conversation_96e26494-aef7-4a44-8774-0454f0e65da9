import SwiftUI

struct Artistitem1Cell: View {
    let model: PopularArtistModel
    var onSelect: ((PopularArtistModel) -> Void)?
   
    @EnvironmentObject private var appState:AppState
    var body: some View {
        
        Button {
           onSelect?(model)
        } label: {
            VStack {
                NetworkImageView(path: model.staffImageUrl)
                   
                    .frame(width: getRelativeWidth(104.0), height: getRelativeWidth(106.0),
                           alignment: .leading)
                    .scaledToFit()
                    .clipShape(Circle())
                   
                Text(model.staffName)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                
                    .multilineTextAlignment(.center)
                
                    .padding(.top, getRelativeHeight(6.0))
                    .padding(.horizontal, getRelativeWidth(4.0))
                Text(model.designation ?? "")
                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(11.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Cyan800B2)
                    .lineLimit(2,reservesSpace: true)
                    .multilineTextAlignment(.center)
                    
                    .padding(.horizontal, getRelativeWidth(17.0))
            }
//            .frame(width: getRelativeWidth(104.0), alignment: .leading)
            .hideNavigationBar()
            
        }
        .disabled(onSelect == nil)
        

    }
}


struct Artistitem1ShimmerCell: View {
  
   
   
    var body: some View {
        
        Button {
           
        } label: {
            VStack {
                NetworkImageView(path: nil)
                   
                    .frame(width: getRelativeWidth(104.0), height: getRelativeWidth(106.0),
                           alignment: .leading)
                    .scaledToFit()
                    .clipShape(Circle())
                   
                Text("model.staffName")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                
                    .multilineTextAlignment(.center)
                
                    .padding(.top, getRelativeHeight(6.0))
                    .padding(.horizontal, getRelativeWidth(4.0))
                Text("model.designation ?? ")
                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(11.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Cyan800B2)
                   
                    .multilineTextAlignment(.center)
                    .frame(height: getRelativeHeight(16.0),
                           alignment: .center)
                    .padding(.horizontal, getRelativeWidth(17.0))
            }
            .frame(width: getRelativeWidth(104.0), alignment: .leading)
            .hideNavigationBar()
        }

        

    }
}

//struct Artistitem1Cell_Previews: PreviewProvider {
//    static var previews: some View {
//        Artistitem1Cell()
//    }
//}
