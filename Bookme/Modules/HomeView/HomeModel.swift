//
//  HomeModel.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//

import Foundation

struct TutorialItem: Identifiable {
    let id: UUID = .init()
    let index: Int
}

// MARK: - ServicesCategoryModel

struct ServicesCategoryModel: Codable, Identifiable, Equatable, Hashable {
    
    let catID: CatID
    let catName: String
    let icon:String?
    let ordering: Int?

    var catIDInt: Int {
        switch catID {
        case .integer(let int):
            return int
        case .string(_):
            return 0
        }
    }
    
    var id: Int { catIDInt }

    var imageUrl: String { AppConstants.Server.baseURL + (icon ?? "") }

    enum CodingKeys: String, CodingKey {
        case catID = "Cat_ID"
        case catName = "Cat_Name"
        case icon = "Icon"
        case ordering = "Ordering"
    }
}

enum CatID: Codable, Equatable, Hashable {
    case integer(Int)
    case string(String)

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let x = try? container.decode(Int.self) {
            self = .integer(x)
            return
        }
        if let x = try? container.decode(String.self) {
            self = .string(x)
            return
        }
        throw DecodingError.typeMismatch(CatID.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Wrong type for CatID"))
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch self {
        case .integer(let x):
            try container.encode(x)
        case .string(let x):
            try container.encode(x)
        }
    }
}

// MARK: - ServicesCategoryModel

struct PopularArtistModel: Codable, Identifiable {
    let vendorID: Int
    let vendorName, vendorLogo: String
    let deliveryRange: Int
    let owner: String?
    let staffID: Int
    let staffName, staffImage: String
    let designation: String?
    let distance: Double?
    let id: UUID = .init()

    var staffImageUrl: String { AppConstants.Server.baseURL + staffImage }

    enum CodingKeys: String, CodingKey {
        case vendorID = "vendor_id"
        case vendorName = "vendor_name"
        case vendorLogo = "vendor_logo"
        case deliveryRange = "delivery_range"
        case owner
        case staffID = "staff_id"
        case staffName = "staff_name"
        case staffImage = "staff_image"
        case designation, distance
    }
}

// MARK: - PromotionBannerModel

struct PromotionBannerModel: Codable, Identifiable {
    let id: UUID = .init()
    let promoImage, title, subtitle, name: String

    var promoImageUrl: String { AppConstants.Server.baseURL + promoImage }

    enum CodingKeys: String, CodingKey {
        case promoImage = "PromoImage"
        case title, subtitle
        case name = "Name"
    }
}

// MARK: - NearestVendorModel

struct NearestVendorModel: Codable, Identifiable, Equatable, Hashable {
    let id: UUID = .init()
    let vendorName: String
    let vendorID: Int
    let vendorEmail, vendorPhone, vendorLogo, address: String
    let lat, lng, openingTime, closingTime: String
    let type: Int
    var distance, rating: String

    var vendorLogoUrl: String { AppConstants.Server.baseURL + vendorLogo }

    enum CodingKeys: String, CodingKey {
        case vendorName = "vendor_name"
        case vendorID = "vendor_id"
        case vendorEmail = "vendor_email"
        case vendorPhone = "vendor_phone"
        case vendorLogo = "vendor_logo"
        case address, lat, lng
        case openingTime = "opening_time"
        case closingTime = "closing_time"
        case type, distance, rating
    }

    // Parameterized initializer
    init(
        vendorName: String,
        vendorID: Int,
        vendorEmail: String,
        vendorPhone: String,
        vendorLogo: String,
        address: String,
        lat: String,
        lng: String,
        openingTime: String,
        closingTime: String,
        type: Int,
        distance: String,
        rating: String
    ) {
        self.vendorName = vendorName
        self.vendorID = vendorID
        self.vendorEmail = vendorEmail
        self.vendorPhone = vendorPhone
        self.vendorLogo = vendorLogo
        self.address = address
        self.lat = lat
        self.lng = lng
        self.openingTime = openingTime
        self.closingTime = closingTime
        self.type = type
        self.distance = distance
        self.rating = rating
    }

    init(from decoder: any Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.vendorName = try container.decode(String.self, forKey: .vendorName)
        self.vendorID = try container.decode(Int.self, forKey: .vendorID)
        self.vendorEmail = try container.decode(String.self, forKey: .vendorEmail)
        self.vendorPhone = try container.decode(String.self, forKey: .vendorPhone)
        self.vendorLogo = try container.decode(String.self, forKey: .vendorLogo)
        self.address = try container.decode(String.self, forKey: .address)
        self.lat = try container.decode(String.self, forKey: .lat)
        self.lng = try container.decode(String.self, forKey: .lng)
        self.openingTime = try container.decode(String.self, forKey: .openingTime)
        self.closingTime = try container.decode(String.self, forKey: .closingTime)
        self.type = try container.decode(Int.self, forKey: .type)

        do {
            let ratingValue = try container.decode(Double.self, forKey: .rating)
            self.rating = String(ratingValue)

            let distanceValue = try container.decode(Int.self, forKey: .distance)
            self.distance = String(distanceValue)

        } catch DecodingError.typeMismatch {
            self.rating = try container.decode(String.self, forKey: .rating)
            self.distance = try container.decode(String.self, forKey: .distance)
        }
    }
}

struct PopularArtistRequest: Encodable {
    let lat, lng: String
    var searchstring: String?
}
