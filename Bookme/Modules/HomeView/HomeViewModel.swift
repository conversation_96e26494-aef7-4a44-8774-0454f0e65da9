import CoreLocation
import Foundation
import SwiftUI

class HomeViewModel: SuperViewModel {
    @Published var popularArtistList: [PopularArtistModel] = []
    @Published var promotionBannerList: [PromotionBannerModel] = []
    @Published var servicesList: [ServicesCategoryModel] = []
    @Published var nearestVendorList: [NearestVendorModel] = []
    @Published var nextScreen: String? = nil
    @Published var searchText: String = ""
    @Published var sliderfortyCurrentPage: Int = 0
    @Published var sliderData: [TutorialItem] = [
        TutorialItem(index: 0), TutorialItem(index: 1), TutorialItem(index: 2)
    ]
    
    
    var appState: EnvironmentObject<AppState>?
    var routerManager: Environment<RouterManager>?
    
    override init() {
        super.init()
       
        getPromotionBanners()
    }
    
    func onArtistSelect(model: PopularArtistModel) {
        if let appState = appState?.wrappedValue,
           let routerManager = routerManager?.wrappedValue {
            let routesType = routerManager.mapRouterWithTab(appState: appState)
            routerManager.push(to: .shopDetails(type: routesType, vendorID: model.vendorID), where: routesType)
        }
    }
    
    func updateEnvironmentVariables(appState: EnvironmentObject<AppState>, routerManager: Environment<RouterManager>) {
        self.appState = appState
        
        self.routerManager = routerManager
    }
    
    func getPopularArtist(coordinate: CLLocationCoordinate2D) {
//        let artistRequest: PopularArtistRequest = .init(lat: "\(coordinate.latitude)", lng: "\(coordinate.longitude)", searchstring: "")
        let genderList = AppState.serviceType.map({ $0.uppercased() }).joined(separator: ",")
        let params: [String: Any] = ["lat": coordinate.latitude, "lng": coordinate.longitude, "gender": genderList]
        onApiCall(api.popularArtists, parameters: params) { self.popularArtistList = $0.data ?? [] }
    }
    
    func getServiceCategory() { onApiCall(api.servicesCategory, parameters: emptyDictionary) { self.servicesList = $0.data ?? [] }}
    
    func getPromotionBanners() {
        onApiCall(api.promotionBanners, parameters: emptyDictionary) {
            self.promotionBannerList = $0.data ?? []
        }
    }
    
    func getNearestVendors(coordinate: CLLocationCoordinate2D) {
        let genderList = AppState.serviceType.map({ $0.uppercased() }).joined(separator: ",")
        let params: [String: Any] = ["lat": coordinate.latitude, "lng": coordinate.longitude, "gender": genderList]
        onApiCall(api.nearbySalons, parameters: params) { self.nearestVendorList = $0.data ?? [] }
    }
    
    func onMainScreenAppear(_ appState: EnvironmentObject<AppState>) {
        if !AppState.showHomeSplash {
            AppState.showHomeSplash = true
            
            appState.wrappedValue.updateTabBarHidden(false)
           
        }
    }
    
    
}
