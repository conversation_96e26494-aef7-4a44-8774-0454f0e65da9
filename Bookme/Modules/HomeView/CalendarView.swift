//
//  CalendarView.swift
//  Bookme
//
//  Created by Apple on 31/01/2024.
//

import Foundation
import SwiftUI

struct CalendarSelectionView: View {
    private let calendar: Calendar = .init(identifier: .gregorian)
    @Binding private var disabledDates: [String] // Staff off-days
    private let maximumAllowedDate: Date? // Vendor's general booking window limit
    private let onClose: (() -> Void)?
    private let onMonth: () -> Void
    private let monthFormatter: DateFormatter
    private let dayFormatter: DateFormatter
    private let weekDayFormatter: DateFormatter
    private let fullFormatter: DateFormatter

    @Binding var selectedDate: Date

    @Namespace private var animation
    init(selectedDate: Binding<Date>, disabledDates: Binding<[String]>, maximumAllowedDate: Date? = nil, onMonth: @escaping (() -> Void), onClose: (() -> Void)? = nil) {
        self._selectedDate = selectedDate
        self._disabledDates = disabledDates
        self.maximumAllowedDate = maximumAllowedDate
        self.onClose = onClose
        self.onMonth = onMonth
        self.monthFormatter = DateFormatter(dateFormat: "MMMM YYYY", calendar: calendar)
        self.dayFormatter = DateFormatter(dateFormat: "d", calendar: calendar)
        self.weekDayFormatter = DateFormatter(dateFormat: "EEE", calendar: calendar)
        self.fullFormatter = DateFormatter(dateFormat: "MMMM dd, yyyy", calendar: calendar)
    }

    func isDisabledDate(_ date: Date) -> Bool {
        let dateString = date.toString(outputFormate: "yyyy-MM-d")
        let isStaffOffDay = disabledDates.contains(dateString)

        var isBeyondVendorSchedule = false
        if let maxDate = maximumAllowedDate {
            if calendar.startOfDay(for: date) > calendar.startOfDay(for: maxDate) {
                isBeyondVendorSchedule = true
            }
        }
        return date.isPastDate || isStaffOffDay || isBeyondVendorSchedule
    }

    var body: some View {
        VStack {
            CalendarView(
                calendar: calendar,
                date: $selectedDate,
                content: { date in
                    Button(action: { withAnimation(.bouncy) {
                        selectedDate = date
                    }}) {
                        Text(dayFormatter.string(from: date))
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                            .fontWeight(.regular)
                            .padding(.horizontal, getRelativeWidth(11.0))
                            .padding(.vertical, getRelativeHeight(6.0))
                            .foregroundColor(isDisabledDate(date) ? ColorConstants.Bluegray101 : calendar.isDate(date, inSameDayAs: selectedDate) ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                            .fixedSize()
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(30.0),
                                   height: getRelativeWidth(30.0), alignment: .center)
                            .overlay(Circle()
                                .stroke(isDisabledDate(date) ? ColorConstants.Bluegray101 : ColorConstants.Cyan800,
                                        lineWidth: 1))
                            .if(isDisabledDate(date), transform: {
                                $0.overlay {
                                    Divider()

                                        .rotationEffect(.degrees(50))
                                        .frame(width: 2)
                                }
                            })
                            .background(
                                isDisabledDate(date) ? ColorConstants.Gray5003f1.clipShape(.circle) :
                                    calendar.isDate(date, inSameDayAs: selectedDate)
                                    ? ColorConstants.Cyan800.clipShape(.circle) :
                                    ColorConstants.Cyan8003f.clipShape(.circle)
                            )

                            .if(calendar.isDate(date, inSameDayAs: selectedDate), transform: {
                                $0.matchedGeometryEffect(id: "calendar.selection", in: animation)
                            })
                    }
                    .disabled(isDisabledDate(date))
                },
                trailing: { date in
                    Text(dayFormatter.string(from: date))
//                        .foregroundColor(.secondary)
                        .foregroundColor(.clear)
                },
                header: { date in
                    Text(weekDayFormatter.string(from: date).uppercased())
//                    Text(StringConstants.kLblFri)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900Bf)
                },
                title: { date in
                    VStack(spacing: 0) {
                        HStack {
                            Text(monthFormatter.string(from: date).uppercased())
                                //                        Text(StringConstants.kLblNovember2023)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(15.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Black900)

                            Spacer()

                            HStack(spacing: 0) {
                                Button {
                                    //                            withAnimation {
                                    // Calculate the start of the target previous month
                                    guard let targetPreviousMonthStartDate = calendar.date(byAdding: .month, value: -1, to: selectedDate.startOfMonth(using: calendar)) else {
                                        return
                                    }

                                    // Check if the target previous month has any enabled days
                                    if !isEntireMonthDisabled(monthStartDate: targetPreviousMonthStartDate) {
                                        let currentDayComponent = calendar.component(.day, from: selectedDate)
                                        var dateComponentsForTargetMonth = calendar.dateComponents([.year, .month], from: targetPreviousMonthStartDate)
                                        dateComponentsForTargetMonth.day = currentDayComponent
                                        
                                        if let potentialNewDate = calendar.date(from: dateComponentsForTargetMonth),
                                           calendar.isDate(potentialNewDate, equalTo: targetPreviousMonthStartDate, toGranularity: .month), // Ensure it's in the target month
                                           !isDisabledDate(potentialNewDate) { // And it's not disabled
                                            selectedDate = potentialNewDate
                                        } else {
                                            // Fallback: find the first enabled day of the target previous month
                                            if let firstEnabledDate = findFirstEnabledDate(inMonthOf: targetPreviousMonthStartDate) {
                                                selectedDate = firstEnabledDate
                                            }
                                            // If no enabled date is found, selectedDate remains unchanged.
                                        }
                                    }
                                    // If isEntireMonthDisabled is true, do nothing to selectedDate.
                                    //                            }
                                } label: {
                                    Label(
                                        title: { Text("Previous") },
                                        icon: { Image(systemName: "chevron.left") }
                                    )
                                    .labelStyle(IconOnlyLabelStyle())
                                    .padding(.horizontal)
                                    .frame(maxHeight: .infinity)
                                }.disableWithOpacity(date.isPastDate)

                                Button(action: onMonth, label: {
                                    HStack {
                                        Text("Show Month")
                                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(11.0)))
                                            .fontWeight(.medium)
                                            .fixedSize()
                                            .foregroundColor(ColorConstants.Cyan900)
                                        Image(systemName: "chevron.down")
                                            .resizable()
                                            .frame(width: 8, height: 4, alignment: .center)
                                    }
                                })

                                Button {
                                    //                            withAnimation {
                                    // Calculate the start of the target next month
                                    guard let targetNextMonthStartDate = calendar.date(byAdding: .month, value: 1, to: selectedDate.startOfMonth(using: calendar)) else {
                                        return
                                    }

                                    // Check if the target next month has any enabled days
                                    if !isEntireMonthDisabled(monthStartDate: targetNextMonthStartDate) {
                                        let currentDayComponent = calendar.component(.day, from: selectedDate)
                                        var dateComponentsForTargetMonth = calendar.dateComponents([.year, .month], from: targetNextMonthStartDate)
                                        dateComponentsForTargetMonth.day = currentDayComponent

                                        if let potentialNewDate = calendar.date(from: dateComponentsForTargetMonth),
                                           calendar.isDate(potentialNewDate, equalTo: targetNextMonthStartDate, toGranularity: .month), // Ensure it's in the target month
                                           !isDisabledDate(potentialNewDate) { // And it's not disabled
                                            selectedDate = potentialNewDate
                                        } else {
                                            // Fallback: find the first enabled day of the target next month
                                            if let firstEnabledDate = findFirstEnabledDate(inMonthOf: targetNextMonthStartDate) {
                                                selectedDate = firstEnabledDate
                                            }
                                            // If no enabled date is found, selectedDate remains unchanged.
                                        }
                                    }
                                    // If isEntireMonthDisabled is true, do nothing to selectedDate.
                                    //                            }
                                } label: {
                                    Label(
                                        title: { Text("Next") },
                                        icon: { Image(systemName: "chevron.right") }
                                    )
                                    .labelStyle(IconOnlyLabelStyle())
                                    .padding(.horizontal)
                                    .frame(maxHeight: .infinity)
                                }
                                .disableWithOpacity(isNextMonthUnavailable(currentDisplayMonth: date)) // 'date' here is start of current month
                            }
                            .foregroundStyle(ColorConstants.Cyan800)
                            .frame(height: 26)
                            .overlay(Capsule()
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                            .background(ColorConstants.Cyan8003f.clipShape(.capsule))

                            if let onClose = onClose {
                                Button(action: onClose) {
                                    Image("img_closeroundlig")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: getRelativeWidth(20.0),
                                               height: getRelativeWidth(20.0), alignment: .center)

                                        .padding(.horizontal, getRelativeWidth(6.0))
                                }
                            }
                        }

                        if let _ = onClose {
                            Divider()
                                .frame(
                                    height: getRelativeHeight(1.0), alignment: .center
                                )
                                .background(ColorConstants.Cyan8003f)
                                .padding(.trailing, getRelativeWidth(4.0))
                                .padding(.top, 8)
                        }
                    }
                    .padding(.bottom)
                }
            )
            .equatable()
        }
        .padding(.horizontal)
    }
}

// MARK: - Component

public struct CalendarView<Day: View, Header: View, Title: View, Trailing: View>: View {
    // Injected dependencies
    private var calendar: Calendar
    @Binding private var date: Date
    private let content: (Date) -> Day
    private let trailing: (Date) -> Trailing
    private let header: (Date) -> Header
    private let title: (Date) -> Title

    // Constants
    private let daysInWeek = 7

    public init(
        calendar: Calendar,
        date: Binding<Date>,
        @ViewBuilder content: @escaping (Date) -> Day,
        @ViewBuilder trailing: @escaping (Date) -> Trailing,
        @ViewBuilder header: @escaping (Date) -> Header,
        @ViewBuilder title: @escaping (Date) -> Title
    ) {
        self.calendar = calendar
        self._date = date
        self.content = content
        self.trailing = trailing
        self.header = header
        self.title = title
    }

    public var body: some View {
        let month = date.startOfMonth(using: calendar)
        let days = makeDays()

        return LazyVGrid(columns: Array(repeating: GridItem(), count: daysInWeek)) {
            Section(header: title(month)) {
                ForEach(days.prefix(daysInWeek), id: \.self, content: header)

                ForEach(days, id: \.self) { date in

                    let isNotFromPreviousMonth: Bool = date.isNotFromPreviousMonth()

                    if calendar.isDate(date, equalTo: month, toGranularity: .month) && isNotFromPreviousMonth {
                        content(date)

                    } else {
                        trailing(date)
                    }
                }
            }
        }
    }
}

// MARK: - Conformances

extension CalendarView: Equatable {
    public static func == (lhs: CalendarView<Day, Header, Title, Trailing>, rhs: CalendarView<Day, Header, Title, Trailing>) -> Bool {
        lhs.calendar == rhs.calendar && lhs.date == rhs.date
    }
}

// MARK: - Helpers

private extension CalendarView {
    func makeDays() -> [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: date),
              let monthFirstWeek = calendar.dateInterval(of: .weekOfMonth, for: monthInterval.start),
              let monthLastWeek = calendar.dateInterval(of: .weekOfMonth, for: monthInterval.end - 1)
        else {
            return []
        }

        let dateInterval = DateInterval(start: monthFirstWeek.start, end: monthLastWeek.end)
        return calendar.generateDays(for: dateInterval)
    }
}

extension Calendar { // Removed 'private'
    func generateDates(
        for dateInterval: DateInterval,
        matching components: DateComponents
    ) -> [Date] {
        var dates = [dateInterval.start]

        enumerateDates(
            startingAfter: dateInterval.start,
            matching: components,
            matchingPolicy: .nextTime
        ) { date, _, stop in
            guard let date = date else { return }

            guard date < dateInterval.end else {
                stop = true
                return
            }

            dates.append(date)
        }

        return dates
    }

    func generateDays(for dateInterval: DateInterval) -> [Date] {
        generateDates(
            for: dateInterval,
            matching: dateComponents([.hour, .minute, .second], from: dateInterval.start)
        )
    }
}

// MARK: - Previews

// #if DEBUG
struct CalendarView_Previews: PreviewProvider {
    static var previews: some View {
        CalendarSelectionView(selectedDate: .constant(.now), disabledDates: .constant([]), maximumAllowedDate: Calendar.current.date(byAdding: .day, value: 14, to: .now), onMonth: {})
    }
}

// #endif

private extension CalendarSelectionView {
    // Renamed and logic adjusted slightly if needed, but primary use is to check any given month.
    func isEntireMonthDisabled(monthStartDate: Date) -> Bool {
        guard let monthInterval = calendar.dateInterval(of: .month, for: monthStartDate) else {
            return true // Cannot determine month interval, assume disabled
        }
        let daysInMonth = calendar.generateDays(for: monthInterval)
        if daysInMonth.isEmpty { return true }

        for dayInMonth in daysInMonth {
            if !isDisabledDate(dayInMonth) { // Uses the existing isDisabledDate logic
                return false // Found at least one enabled day
            }
        }
        return true // All days in this month are disabled
    }
    
    func findFirstEnabledDate(inMonthOf monthStartDate: Date) -> Date? {
        guard let monthInterval = calendar.dateInterval(of: .month, for: monthStartDate) else {
            return nil
        }
        let daysInMonth = calendar.generateDays(for: monthInterval)
        for dayInMonth in daysInMonth {
            if !isDisabledDate(dayInMonth) {
                return dayInMonth
            }
        }
        return nil // No enabled date found in this month
    }

    // This function is still used for disabling the "Next" button itself based on the *currently displayed* month's next month.
    func isNextMonthUnavailable(currentDisplayMonth: Date) -> Bool {
        guard let nextMonthStartDate = calendar.date(byAdding: .month, value: 1, to: currentDisplayMonth) else {
            return true
        }
        return isEntireMonthDisabled(monthStartDate: nextMonthStartDate)
    }
}
