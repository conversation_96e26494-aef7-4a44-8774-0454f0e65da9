import SwiftUI

struct HomeView: View {
    @StateObject var viewModel: HomeViewModel = .init()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager
    @Namespace private var animation
    @EnvironmentObject private var locationManager: LocationManager
    @State private var offset: CGFloat = 0
    var body: some View {
        if appState.showSplash {
            SplashView(isHomeSplash: true, animation: animation)
                .onLoad {
                    appState.onSplashAppear(_appState)
                }
        } else {
            SuperView(pageState: $viewModel.pageState) {} content: {
                
                VStack {
                    VStack(spacing: -16) {
                        CustomNavigationBar(animation: animation)
                        
                        SearchHeaderView(routesType: .homeRoute, hideFilter: true, disableSearch: true, content: mapSectionButton, filterIndicate: .constant(true), searchText: $viewModel.searchText, onSearch: appState.onSearch)
                    }
                    
                    if viewModel.pageState == .loading(true) {
                        HomeShimmerView()
                            .shimmerize()
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(ColorConstants.WhiteA700)
                    } else {
                        VStack(alignment: .leading, spacing: 0) {
                            let filteredData = viewModel.servicesList.filter { $0.catIDInt != 0 }
                            
                            VStack {
                                VStack(alignment: .leading, spacing: 0) {
                                    HStack {
                                        Text("Categories")
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(87.0),
                                                   height: getRelativeHeight(25.0), alignment: .topLeading)
                                        Spacer()
                                        
                                        Button {
                                            routerManager.push(to: .mainCategory(category: filteredData), where: .homeRoute)
                                        } label: {
                                            Text("See All")
                                                .font(FontScheme
                                                    .kNunitoMedium(size: getRelativeHeight(13.0)))
                                                .fontWeight(.medium)
                                                .foregroundColor(ColorConstants.Cyan800B2)
                                                .minimumScaleFactor(0.5)
                                                .multilineTextAlignment(.leading)
                                                .frame(width: getRelativeWidth(41.0),
                                                       height: getRelativeHeight(18.0),
                                                       alignment: .topLeading)
                                              
                                                .padding(.leading, getRelativeWidth(186.0))
                                        }
                                    }
                                    
                                    ScrollView(.horizontal, showsIndicators: false) {
                                        HStack(spacing: 20.0.relativeWidth) {
                                            ForEach(filteredData) { item in
                                            
                                                Button(action: {
                                                    appState.selectedHeader = item
                                                    appState.updateSelectedTab(.explore)
                                                }, label: {
                                                    VStack {
                                                        NetworkImageView(path: item.imageUrl, contentMode: .fit)
                                                            .scaleEffect(0.7)
                                                            .frame(width: getRelativeWidth(69.0),
                                                                   height: getRelativeWidth(69.0), alignment: .center)
                                                   
                                                            .overlay(Circle()
                                                                .stroke(ColorConstants.Cyan800,
                                                                        lineWidth: 1))
                                                            .background(Circle()
                                                                .fill(ColorConstants.Cyan80038))
                                                        Text(item.catName)
                                                            .font(FontScheme
                                                                .kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                                            .fontWeight(.semibold)
                                                            .multilineTextAlignment(.center)
                                                            .foregroundColor(ColorConstants.WhiteA700)
                                                            .lineLimit(1, reservesSpace: true)
                                                            .fixedSize()
                                                            .multilineTextAlignment(.leading)
                                                            .padding(.top, getRelativeHeight(6.0))
                                                            .padding(.horizontal, getRelativeWidth(4.0))
                                                    }
                                                    .frame(width: getRelativeWidth(69.0),
                                                           alignment: .center)

                                                })
                                            }
                                        }
                                        .padding(.top, getRelativeHeight(17.0))
                                    }
                                    
                                    .scrollClipDisabled()
                                }
                                .visibility(offset > -10 ? .visible : .gone) // Show when scrolled
                                
                                .padding(.horizontal)
                                .padding(.top)
                                .transition(.move(edge: .top))
                                
                            }
                            .padding(.bottom)
                            .clipped()
                           
                            
                            ScrollViewWithOffset(.vertical, showsIndicators: false) { self.offset = $0.y }
                                content: {
                                    VStack(alignment: .leading, spacing: 0) {
                                        if viewModel.popularArtistList.count > 0 {
                                            VStack(alignment: .leading, spacing: 0) {
                                                HStack {
                                                    Text("Popular Artist")
                                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                                        .fontWeight(.bold)
                                                        .foregroundColor(ColorConstants.Black900)
                                                        .minimumScaleFactor(0.5)
                                                        .multilineTextAlignment(.leading)
                                                        .frame(width: getRelativeWidth(124.0),
                                                               height: getRelativeHeight(25.0),
                                                               alignment: .topLeading)
                                                    //                                                Spacer()
                                                    //                                                Text(StringConstants.kLblSeeAll)
                                                    //                                                    .font(FontScheme
                                                    //                                                        .kNunitoMedium(size: getRelativeHeight(13.0)))
                                                    //                                                    .fontWeight(.medium)
                                                    //                                                    .foregroundColor(ColorConstants.Cyan800B2)
                                                    //                                                    .minimumScaleFactor(0.5)
                                                    //                                                    .multilineTextAlignment(.leading)
                                                    //                                                    .frame(width: getRelativeWidth(41.0),
                                                    //                                                           height: getRelativeHeight(18.0),
                                                    //                                                           alignment: .topLeading)
                                                    //                                                    .padding(.bottom, getRelativeHeight(7.0))
                                                    //                                                    .padding(.leading, getRelativeWidth(186.0))
                                                }
                                                .padding(.horizontal, getRelativeWidth(16.0))
                                           
                                                HStack(spacing: 0) {
                                                    ScrollView(.horizontal, showsIndicators: false) {
                                                        LazyHStack(spacing: 14.0.relativeWidth) {
                                                            ForEach(viewModel.popularArtistList) {
                                                                Artistitem1Cell(model: $0, onSelect: viewModel.onArtistSelect)
                                                            }
                                                        }.padding(.horizontal, getRelativeWidth(16.0))
                                                    }.scrollClipDisabled()
                                                }
                                               
                                                .padding(.top, getRelativeHeight(20.0))
                                                .padding(.trailing, getRelativeWidth(10.0))
                                            }

                                            .padding(.top, getRelativeHeight(25.0))
                                        }
                                   
                                        VStack {
                                            TabView(selection: $viewModel.sliderfortyCurrentPage.animation(.bouncy)) {
                                                if !viewModel.promotionBannerList.isEmpty { ForEach(Array(viewModel.promotionBannerList.enumerated()), id: \.element.id) { index, item in
                                                    HStack(alignment: .center, spacing: 0) {
                                                        VStack(alignment: .leading) {
                                                            Text(item.title)
                                                                .font(FontScheme
                                                                    .kNunitoBold(size: getRelativeHeight(20.0)))
                                                                .fontWeight(.bold)
                                                                .foregroundColor(ColorConstants.Cyan800)
                                                                .minimumScaleFactor(0.5)
                                                                .multilineTextAlignment(.leading)
                                                                .frame(width: getRelativeWidth(51.0),
                                                                       height: getRelativeHeight(28.0),
                                                                       alignment: .topLeading)
                                                            
                                                            Text(item.subtitle)
                                                                .font(FontScheme
                                                                    .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                                                                .fontWeight(.semibold)
                                                                .foregroundColor(ColorConstants.Cyan800)
                                                                .minimumScaleFactor(0.5)
                                                                .multilineTextAlignment(.leading)
                                                                .frame(width: getRelativeWidth(146.0),
                                                                       height: getRelativeHeight(43.0),
                                                                       alignment: .topLeading)
                                                                .padding(.vertical, getRelativeHeight(10.0))
                                                           
                                                            Button(action: {}, label: {
                                                                HStack(spacing: 0) {
                                                                    Text("Book Now")
                                                                        .font(FontScheme
                                                                            .kNunitoExtraBold(size: getRelativeHeight(13.0)))
                                                                        .fontWeight(.heavy)
                                                                        .padding(.horizontal, getRelativeWidth(12.0))
                                                                        .padding(.vertical, getRelativeHeight(6.0))
                                                                        .foregroundColor(ColorConstants.WhiteA700)
                                                                        .minimumScaleFactor(0.5)
                                                                        .multilineTextAlignment(.leading)
                                                                        .frame(width: getRelativeWidth(86.0),
                                                                               height: getRelativeHeight(31.0),
                                                                               alignment: .topLeading)
                                                                        .background(RoundedCorners(topLeft: 15.5,
                                                                                                   topRight: 15.5,
                                                                                                   bottomLeft: 15.5,
                                                                                                   bottomRight: 15.5)
                                                                                .fill(ColorConstants.Cyan800))
                                                                        .padding(.trailing, getRelativeWidth(10.0))
                                                                }
                                                            })
                                                        }
                                                        .padding()
                                                        .frame(height: 158.0.relativeHeight)
                                                        .frame(maxWidth: .infinity)
                                                        .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.1))
                                                        .clipped()
                                                        //                        .shadow(color: .black.opacity(0.25), radius: 2.5, x: -1, y: 0)
                                                        NetworkImageView(path: item.promoImageUrl, contentMode: .fill)
                                                            .frame(width: 166.0.relativeWidth, height: 158.0.relativeHeight)
                                                            .shadow(color: .black.opacity(0.25), radius: 2.5, x: -1, y: 0)
                                                    }
                                                    .tag(index)
                                                    .frame(height: getRelativeHeight(158.0))
                                                    .clipShape(RoundedCorners(topLeft: 23.0, topRight: 23.0, bottomLeft: 23.0, bottomRight: 23.0))
                                                    .padding(.horizontal)
                                                    //                                            .clipped()
                                                    //                                            .shadow(color: .black.opacity(0.25), radius: 2, x: 1, y: 1)
                                                }}
                                            }
                                            .scrollClipDisabled()
                                            .frame(height: getRelativeHeight(168.0))
                                            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                                            .multilineTextAlignment(.center)
                                        
                                            PageIndicator(numPages: viewModel.promotionBannerList.count,
                                                          currentPage: $viewModel
                                                              .sliderfortyCurrentPage,
                                                          selectedColor: ColorConstants.Cyan800,
                                                          unSelectedColor: ColorConstants.Cyan8003f,
                                                          spacing: 8.0, diameter: 8, isCircular: false)
                                        }
                                        .padding(.top, getRelativeHeight(26.0))
                                    
                                        HStack {
                                            Text("Nearest Vendor")
                                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                                .fontWeight(.bold)
                                                .foregroundColor(ColorConstants.Black900)
                                                .multilineTextAlignment(.leading)
                                           
                                            Spacer()
                                        
                                            Button {
                                                appState.selectedHeader = appState.servicesList.first
                                                appState.updateSelectedTab(.explore)
                                            } label: {
                                                Text("See All")
                                                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                                    .fontWeight(.medium)
                                                    .foregroundColor(ColorConstants.Cyan800B2)
                                                    .minimumScaleFactor(0.5)
                                                    .multilineTextAlignment(.leading)
                                                    .frame(width: getRelativeWidth(41.0),
                                                           height: getRelativeHeight(18.0), alignment: .topLeading)
                                                    .padding(.vertical, getRelativeHeight(1.0))
                                            }
                                        }
                                   
                                        .padding(.top, getRelativeHeight(30.0))
                                        .padding(.horizontal, getRelativeWidth(16.0))
                                    
                                        VStack(spacing: 0) {
                                            if viewModel.nearestVendorList.isEmpty {
                                                CustomPlaceholder(placeholderType: .noService, title: nil, subTitle: "No Services found near your location")
                                                    .frame(maxWidth: .infinity)
                                            } else {
                                                LazyVStack {
                                                    ForEach(viewModel.nearestVendorList) { item in
                                                        VStack {
                                                            SaloonCardCell(model: item, routesType: .homeRoute)
                                                           
                                                            if item != viewModel.nearestVendorList.last {
                                                                Divider()
                                                                    .padding(.horizontal)
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                  
                                        .padding(.vertical, getRelativeHeight(18.0))
                                        .padding(.horizontal, getRelativeWidth(10.0))
                                    }
                                    .background(ColorConstants.WhiteA700)
                                }
                                .background(ColorConstants.WhiteA700)
                        }
                        .animation(.easeInOut, value: offset)
                    }
                }
            }
            
            .onLoad {
                print("loading started")
                viewModel.onMainScreenAppear(_appState)
                locationManager.requestUserCurrentLocation { coordinate, _ in
                    print("onAppear", coordinate)
                    viewModel.getPopularArtist(coordinate: coordinate)
                    viewModel.getNearestVendors(coordinate: coordinate)
                    viewModel.getServiceCategory()
                }
            }
            .onLoad {
                viewModel.updateEnvironmentVariables(appState: _appState, routerManager: _routerManager)
            }
            .sync($appState.nearestVendorList, with: $viewModel.nearestVendorList)
            .sync($appState.servicesList, with: $viewModel.servicesList)
            .onChange(of: locationManager.currentLocation, initial: false) { _, newValue in
                if let newValue = newValue {
                    viewModel.getPopularArtist(coordinate: newValue)
                    viewModel.getNearestVendors(coordinate: newValue)
                    viewModel.getServiceCategory()
                }
            }
            .onChange(of: routerManager.homeRouteList) { _, newValue in
                
                Utilities.enQueue {
                    appState.isTabBarHidden = !newValue.isEmpty
                }
            }
            .handleDeepLinkNavigation(routesType: .homeRoute)
        }
    }
    
    @ViewBuilder
    func mapSectionButton() -> some View {
        Button(action: {
            routerManager.push(to: .saloonMap, where: .homeRoute)
        }, label: {
            HStack(spacing: 9.0.relativeWidth) {
                Image("img_vector_cyan_800")
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(20.0),
                           height: getRelativeWidth(20.0), alignment: .center)
            }
            .padding(.horizontal, getRelativeWidth(15.0))
            
            //                            .frame(width: 89.0.relativeWidth, height: 35.0.relativeHeight)
            .frame(height: getRelativeHeight(45.0),
                   alignment: .center)
            .overlay(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                    bottomRight: 10.0)
                    .stroke(ColorConstants.Cyan901,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 10.0, topRight: 10.0,
                                       bottomLeft: 10.0, bottomRight: 10.0)
                    .fill(ColorConstants.Teal90093))
        })
    }
}

struct HomeView_Previews: PreviewProvider {
    static var previews: some View {
        HomeView().attachAllEnvironmentObjects()
            .background(ColorConstants.bgGradient)
    }
}

struct SearchHeaderView<Content: View>: View {
    let routesType: RoutesType
    let hideFilter: Bool
    let disableSearch: Bool
    
    @ViewBuilder let content: Content
    @Binding var filterIndicate: Bool
    @Binding var searchText: String
    
    @FocusState var isFocused
    var onSearch: (() -> Void)? = nil
    var onFilter: (() -> Void)? = nil
    @Environment(RouterManager.self) private var routerManager
    
    var body: some View {
        HStack(spacing: 0.0.relativeWidth) {
            ZStack {
                HStack(spacing: 16.0.relativeWidth) {
                    //                                Spacer()
                    Image("img_search")
                        .resizable()
                        .frame(width: getRelativeWidth(18.0),
                               height: getRelativeWidth(18.0), alignment: .center)
                        .scaledToFit()
                        .clipped()
                        .padding(.top, getRelativeHeight(13.0))
                        .padding(.bottom, getRelativeHeight(14.0))
                        .padding(.leading, getRelativeWidth(9.0))
                    TextField("Search for Services",
                              text: $searchText, prompt: Text("Search for Services").foregroundColor(ColorConstants.Cyan800.opacity(0.57)))
                        .focused($isFocused)
                        .font(FontScheme
                            .kMontserratMedium(size: getRelativeHeight(13.0)))
                        .foregroundColor(ColorConstants.Cyan800)
                        .disabled(disableSearch)
                    //                                        .padding()
                }
              
                .frame(height: getRelativeHeight(45.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                        bottomRight: 10.0)
                        .stroke(ColorConstants.Cyan901,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 10.0, topRight: 10.0,
                                           bottomLeft: 10.0, bottomRight: 10.0)
                        .fill(ColorConstants.Teal90093))
                
                if let onSearch = onSearch {
                    // Overlay a button that intercepts the tap
                    Button(action: {
                        print("TextField tapped!")
                        onSearch()
                        // Handle tap event here
                    }) {
                        Color.clear // Transparent color to make the button invisible
                    }
                }
            }
            
            Spacer()
            HStack {
                content

                if !hideFilter {
                    Button(action: {
                        if let onFilter = onFilter {
                            onFilter()
                        } else {
//                            routerManager.push(to: .filter, where: routesType)
                        }
                        
                    }, label: {
                        HStack(spacing: 9.0.relativeWidth) {
                            Image("img_filter")
                                .renderingMode(.template)
                                .resizable()
                                .scaledToFit()
                                .foregroundStyle(ColorConstants.Cyan800)
                                .frame(width: getRelativeWidth(20.0),
                                       height: getRelativeWidth(20.0), alignment: .center)
                        }
                        .padding(.horizontal, getRelativeWidth(15.0))
                        
                        // .frame(width: 89.0.relativeWidth, height: 35.0.relativeHeight)
                        .frame(height: getRelativeHeight(45.0),
                               alignment: .center)
                        .overlay(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                                bottomRight: 10.0)
                                .stroke(ColorConstants.Cyan901,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 10.0, topRight: 10.0,
                                                   bottomLeft: 10.0, bottomRight: 10.0)
                                .fill(ColorConstants.Teal90093))
                        .if(filterIndicate, transform: {
                            $0.overlay(alignment: .topTrailing) {
                                Circle()
                                    .fill(ColorConstants.Cyan800)
                                    .frame(width: 8, height: 8)
                                    .padding(-2)
                            }
                        })
                        
                    })
                }
            }
        }
        .frame(width: getRelativeWidth(356.0), height: getRelativeHeight(45.0),
               alignment: .center)
        .padding(.top, getRelativeHeight(20.0))
        .padding(.trailing, getRelativeWidth(7.0))
    }
}

struct CustomNavigationBar: View {
    var hideMenu: Bool = false
    var animation: Namespace.ID?
    
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager
    
    var body: some View {
        let routeType = mapRouterWithTab(appState: _appState)
        HStack {
            Button(action: {
                appState.showSideMenu.toggle()
            }, label: {
                Image("img_vector")
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(18.0),
                           height: getRelativeHeight(18.0), alignment: .center)
                       
                    .clipped()
                    .padding(.vertical, getRelativeHeight(14.0))
                    .padding(.trailing, getRelativeWidth(12.0))
            })
            
            .visibility(hideMenu ? .invisible : .visible)
            
            Spacer()
            Image("img_logocolor1")
                .resizable()
                .scaledToFit()
                .if(animation != nil, transform: {
                    $0.matchedGeometryEffect(id: "splash.logo", in: animation!)
                })
                
                .frame(width: getRelativeWidth(30.0),
                       height: getRelativeHeight(30.0), alignment: .center)
                
            Spacer()
            
            Button(action: {
                routerManager.push(to: .notification, where: routeType)
            }, label: {
                Image("img_notification")
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(20.0),
                           height: getRelativeHeight(20.0), alignment: .center)
                   
                    .clipped()
                    .padding(.top, getRelativeHeight(13.0))
            }).padding(.leading, getRelativeWidth(12.0))
           
        }.padding(.horizontal, getRelativeWidth(16.0))
    }
}

import CoreLocation
import CoreLocationUI

struct HomeShimmerView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            VStack {
                VStack(alignment: .leading, spacing: 0) {
                    HStack {
                        Text(StringConstants.kLblCategories)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(87.0),
                                   height: getRelativeHeight(25.0), alignment: .topLeading)
                        Spacer()
                    }
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 20.0.relativeWidth) {
                            ForEach(0...8, id: \.self) { _ in
                            
                                Button(action: {}, label: {
                                    VStack {
                                        NetworkImageView(path: nil, contentMode: .fit)
                                            .scaleEffect(0.7)
                                            .frame(width: getRelativeWidth(69.0),
                                                   height: getRelativeWidth(69.0), alignment: .center)
                                   
                                            .overlay(Circle()
                                                .stroke(ColorConstants.Cyan800,
                                                        lineWidth: 1))
                                            .background(Circle()
                                                .fill(ColorConstants.Cyan80038))
                                        Text("item.catName")
                                            .font(FontScheme
                                                .kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                            .fontWeight(.semibold)
                                            .multilineTextAlignment(.center)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .lineLimit(1, reservesSpace: true)
                                            .fixedSize()
                                            .multilineTextAlignment(.leading)
                                            .padding(.top, getRelativeHeight(6.0))
                                            .padding(.horizontal, getRelativeWidth(4.0))
                                    }
                                    .frame(width: getRelativeWidth(69.0),
                                           alignment: .center)

                                })
                            }
                        }
                        .padding(.vertical, getRelativeHeight(17.0))
                    
                    }.scrollClipDisabled()
                }
                .padding(.horizontal)
                .padding(.top)
            }
            
            ScrollView(.vertical, showsIndicators: false) {
                VStack(alignment: .leading, spacing: 0) {
                    VStack(alignment: .leading, spacing: 0) {
                        HStack {
                            Text(StringConstants.kLblPopularArtist)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Black900)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(124.0),
                                       height: getRelativeHeight(25.0),
                                       alignment: .topLeading)
//                                                Spacer()
//                                                Text(StringConstants.kLblSeeAll)
//                                                    .font(FontScheme
//                                                        .kNunitoMedium(size: getRelativeHeight(13.0)))
//                                                    .fontWeight(.medium)
//                                                    .foregroundColor(ColorConstants.Cyan800B2)
//                                                    .minimumScaleFactor(0.5)
//                                                    .multilineTextAlignment(.leading)
//                                                    .frame(width: getRelativeWidth(41.0),
//                                                           height: getRelativeHeight(18.0),
//                                                           alignment: .topLeading)
//                                                    .padding(.bottom, getRelativeHeight(7.0))
//                                                    .padding(.leading, getRelativeWidth(186.0))
                        }
                        .padding(.horizontal, getRelativeWidth(16.0))
                           
                        HStack(spacing: 0) {
                            ScrollView(.horizontal, showsIndicators: false) {
                                LazyHStack(spacing: 14.0.relativeWidth) {
                                    ForEach(0...8, id: \.self) { _ in
                                        Artistitem1ShimmerCell()
                                    }
                                }.padding(.horizontal, getRelativeWidth(16.0))
                            }.scrollClipDisabled()
                        }
                               
                        .padding(.top, getRelativeHeight(20.0))
                        .padding(.trailing, getRelativeWidth(10.0))
                    }

                    .padding(.top, getRelativeHeight(25.0))
                    
                    VStack {
                        HStack(alignment: .center, spacing: 0) {
                            VStack(alignment: .leading) {
                                Text("item.title")
                                    .font(FontScheme
                                        .kNunitoBold(size: getRelativeHeight(20.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(51.0),
                                           height: getRelativeHeight(28.0),
                                           alignment: .topLeading)
                                    
                                Text("item.subtitle item.subtitle item.subtitle")
                                    .font(FontScheme
                                        .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(146.0),
                                           height: getRelativeHeight(43.0),
                                           alignment: .topLeading)
                                    .padding(.vertical, getRelativeHeight(10.0))
                                   
                                Button(action: {}, label: {
                                    HStack(spacing: 0) {
                                        Text(StringConstants.kLblBookNow)
                                            .font(FontScheme
                                                .kNunitoExtraBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.heavy)
                                            .padding(.horizontal, getRelativeWidth(12.0))
                                            .padding(.vertical, getRelativeHeight(6.0))
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(86.0),
                                                   height: getRelativeHeight(31.0),
                                                   alignment: .topLeading)
                                            .background(RoundedCorners(topLeft: 15.5,
                                                                       topRight: 15.5,
                                                                       bottomLeft: 15.5,
                                                                       bottomRight: 15.5)
                                                    .fill(ColorConstants.Cyan800))
                                            .padding(.trailing, getRelativeWidth(10.0))
                                    }
                                })
                            }
                            .padding()
                            .frame(height: 158.0.relativeHeight)
                            .frame(maxWidth: .infinity)
                            .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.1))
                            .clipped()
                            //                        .shadow(color: .black.opacity(0.25), radius: 2.5, x: -1, y: 0)
                            NetworkImageView(path: nil, contentMode: .fill)
                                .frame(width: 166.0.relativeWidth, height: 158.0.relativeHeight)
                                .shadow(color: .black.opacity(0.25), radius: 2.5, x: -1, y: 0)
                        }
                       
                        .frame(height: getRelativeHeight(158.0))
                        .clipShape(RoundedCorners(topLeft: 23.0, topRight: 23.0, bottomLeft: 23.0, bottomRight: 23.0))
                        .padding(.horizontal)
                     
                        PageIndicator(numPages: 3,
                                      currentPage: .constant(0),
                                      selectedColor: ColorConstants.Cyan800,
                                      unSelectedColor: ColorConstants.Cyan8003f,
                                      spacing: 8.0, diameter: 8, isCircular: false)
                    }
                    .padding(.top, getRelativeHeight(26.0))
                    
                    HStack {
                        Text(StringConstants.kLblNearestSalon)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Black900)
                            .multilineTextAlignment(.leading)
                           
                        Spacer()
                        
                        Button {} label: {
                            Text(StringConstants.kLblSeeAll)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Cyan800B2)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(41.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                                .padding(.vertical, getRelativeHeight(1.0))
                        }
                    }
                   
                    .padding(.top, getRelativeHeight(30.0))
                    .padding(.horizontal, getRelativeWidth(16.0))
                    
                    VStack(spacing: 0) {
                        ScrollView(.vertical, showsIndicators: false) {
                            LazyVStack {
                                ForEach(0...8, id: \.self) { _ in
                                    VStack {
                                        SaloonCardShimmerCell()
                                           
                                        Divider()
                                            .padding(.horizontal)
                                    }
                                }
                            }
                        }
                    }
                  
                    .padding(.vertical, getRelativeHeight(18.0))
                    .padding(.horizontal, getRelativeWidth(10.0))
                }
                .background(ColorConstants.WhiteA700)
            }
            .background(ColorConstants.WhiteA700)
        }
    }
}
