//
//  LocationManager.swift
//  Bookme
//
//  Created by Apple on 23/04/2024.
//

import Contacts
import CoreLocation
import Foundation

class LocationManager: NSObject, ObservableObject, @preconcurrency CLLocationManagerDelegate {
    let manager = CLLocationManager()

    @Published var currentLocation: CLLocationCoordinate2D?
    @Published var isPermissionDenied: Bool = false
    override init() {
        super.init()
        manager.delegate = self
    }

    func requestLocation() {
        manager.requestLocation()
    }

    @MainActor func requestUserCurrentLocation(completion: @escaping (CLLocationCoordinate2D, CLPlacemark?) -> Void) {
        if let location = AppState.userLocation {
            location.geocode(completion: { placemark, error in
                if let error = error { print("CLError:", error); completion(location, nil); return }
                else if let placemark = placemark?.first {
                    completion(location, placemark)
                }
            })
        } else {
            manager.requestWhenInUseAuthorization()
        }
    }

    @MainActor func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        currentLocation = locations.first?.coordinate
        if let location = currentLocation { AppState.userLocation = location }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: any Error) {
        print(error.localizedDescription)
    }

    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        switch manager.authorizationStatus {
        case .notDetermined:
            print("notDetermined")
        case .restricted:
            print("restricted")
        case .denied:
            print("denied")
            isPermissionDenied = true // Set the flag
        case .authorizedAlways:
            print("authorizedAlways")
            isPermissionDenied = false // Set the flag
            manager.requestLocation()
        case .authorizedWhenInUse:
            print("authorizedWhenInUse")
            isPermissionDenied = false // Set the flag
            manager.requestLocation()
        case .authorized:
            print("authorized")
            isPermissionDenied = false // Set the flag
            manager.requestLocation()
        @unknown default:
            fatalError()
        }
    }
}

extension CLLocationCoordinate2D: Equatable {
    func geocode(completion: @escaping (_ placemark: [CLPlacemark]?, _ error: Error?) -> Void) {
        CLGeocoder().reverseGeocodeLocation(.init(latitude: latitude, longitude: longitude), completionHandler: completion)
    }
}

extension Formatter {
    static let mailingAddress: CNPostalAddressFormatter = {
        let formatter = CNPostalAddressFormatter()
        formatter.style = .mailingAddress
        return formatter
    }()
}

extension CLPlacemark {
    var mailingAddress: String? {
        postalAddress?.mailingAddress
    }
}

extension CNPostalAddress {
    var mailingAddress: String {
        Formatter.mailingAddress.string(from: self)
    }
}

public func ==(lhs: CLLocationCoordinate2D, rhs: CLLocationCoordinate2D) -> Bool {
    return lhs.latitude == rhs.latitude && lhs.longitude == rhs.longitude
}

extension CLLocationCoordinate2D {
    private static let Lat = "lat"
    private static let Lon = "lon"

    typealias CLLocationDictionary = [String: CLLocationDegrees]

    var asDictionary: CLLocationDictionary {
        return [CLLocationCoordinate2D.Lat: latitude,
                CLLocationCoordinate2D.Lon: longitude]
    }

    init(dict: CLLocationDictionary) {
        self.init(latitude: dict[CLLocationCoordinate2D.Lat]!,
                  longitude: dict[CLLocationCoordinate2D.Lon]!)
    }
}

class LocationManager1: NSObject, ObservableObject, CLLocationManagerDelegate {
    private let locationManager = CLLocationManager()
    @Published var locationStatus: CLAuthorizationStatus?
    @Published var lastLocation: CLLocation?

    override init() {
        super.init()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.requestWhenInUseAuthorization()
        locationManager.startUpdatingLocation()
    }

    var statusString: String {
        guard let status = locationStatus else {
            return "unknown"
        }

        switch status {
        case .notDetermined: return "notDetermined"
        case .authorizedWhenInUse: return "authorizedWhenInUse"
        case .authorizedAlways: return "authorizedAlways"
        case .restricted: return "restricted"
        case .denied: return "denied"
        default: return "unknown"
        }
    }

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        locationStatus = status
        print(#function, statusString)
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        lastLocation = location
        print(#function, location)
    }
}
