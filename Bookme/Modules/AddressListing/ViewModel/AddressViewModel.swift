//
//  AddressViewModel.swift
//  Bookme
//
//  Created by Apple on 06/05/2024.
//

import Foundation

class AddressViewModel: SuperViewModel {
    @Published var addressList: [AddressModel] = []
//    @Published var selectedAddressModel: AddressModel?
    
    var onDone: EmptyCallBack { .init(onBack: getAddressList) }
    
    override init() {
        super.init()
        getAddressList()
    }
    
    func onDeleteAddress(id: Int) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let params: [String: Any] = ["user_id": userID, "Address_ID": id]
        onApiCall(api.deleteAddress, parameters: params) {
            if $0.success {
                self.getAddressList()
            }
        }
    }
    
    func makeAddressDefault(id: Int) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let params: [String: Any] = ["user_id": userID, "Address_ID": id]
        onApiCall(api.defaultAddress, parameters: params) {
            if $0.success {
                self.getAddressList()
            }
        }
    }
    
    func getAddressList() {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let parameters = ["user_id": "\(userID)"]
        onApiCall(api.viewAddresses, parameters: parameters) {
            self.addressList = $0.data ?? []
//            self.selectedAddressModel = $0.data?.first
        }
    }
}
