//
//  AddressCellView.swift
//  Bookme
//
//  Created by Apple on 05/05/2024.
//

import SwiftUI

struct AddressCellView: View {
    let model: AddressModel
    @Binding var checked:Bool
    var body: some View {
        HStack(alignment:.top){
            VStack(alignment:.leading, spacing: 8.0.relativeHeight){
                Text(model.name)
                    .font(FontScheme.kNunitoMedium(size: 15.0.relativeFontSize))
                    .fontWeight(.bold)
                
                
                Text(String(repeating: model.address, count: 1))
                    
                Text(model.phoneNumber)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .font(FontScheme.kNunitoMedium(size: 12.0.relativeFontSize))
            .fontWeight(.medium)
            .foregroundColor(Color(hex: "#008F96"))
            .multilineTextAlignment(.leading)
            
            Spacer()
            CheckBoxView(checked: $checked)
                .disabled(true)
               
                
            
        }
        .padding(.horizontal, 15.0.relativeWidth)
        .padding(.vertical, 13.0.relativeWidth)
        .frame(width: 351.0.relativeWidth, alignment: .topTrailing)
        .frame(minHeight: 137.0.relativeHeight, alignment: .topTrailing)
        .background(ColorConstants.bgGradient.opacity(0.001))
        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                bottomRight: 13.0)
                .stroke(ColorConstants.Cyan800,
                        lineWidth: 1))
    }
}

//#Preview {
//    AddressCellView(checked: .constant(true))
//        .frame(maxWidth: .infinity, maxHeight: .infinity)
//        .background(ColorConstants.bgGradient)
//}
