//
//  AddressListingShimmerView.swift
//  Bookme
//
//  Created by Apple on 31/08/2024.
//

import SwiftUI

struct AddressListingShimmerView: View {
    var body: some View {

       ScrollView {
            VStack(spacing: 10.0.relativeHeight) {
                ForEach(0...9, id: \.self) { index in
                    
                    Button(action: {
                      
                    }, label: {
                        AddressCellShimmerView()
                            .clipShape(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                      bottomRight: 13.0))
                          
                    })
                }
            }
            .padding(.top)
        }
        .background(ColorConstants.bgGradient)
        .safeAreaInset(edge: .bottom) {
            Button(action: {
               
                    
            }, label: {
                HStack(spacing: 0) {
                    Text(StringConstants.kLblAddAddress)
                        .font(FontScheme
                            .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.heavy)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(15.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                       
                        .multilineTextAlignment(.leading)
                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                        .background(RoundedCorners(topLeft: 25.0,
                                                   topRight: 25.0,
                                                   bottomLeft: 25.0,
                                                   bottomRight: 25.0)
                                .fill(ColorConstants.Cyan800))
                        .shadow(color: ColorConstants.Black90044, radius: 2.5,
                                x: 0, y: 1)
                }
            })
                
            .padding(.top, 32.0.relativeHeight)
        }
        .shimmerize()
    }
}



struct AddressCellShimmerView: View {
  
    var body: some View {
        HStack(alignment:.top){
            VStack(alignment:.leading, spacing: 8.0.relativeHeight){
                Text("model.name")
                    .font(FontScheme.kNunitoMedium(size: 15.0.relativeFontSize))
                    .fontWeight(.bold)
                
                
                Text(String(repeating: "model.address", count: 4))
                    
                Text("model.phoneNumber")
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .font(FontScheme.kNunitoMedium(size: 12.0.relativeFontSize))
            .fontWeight(.medium)
            .foregroundColor(Color(hex: "#008F96"))
            .multilineTextAlignment(.leading)
            
            Spacer()
            CheckBoxView(checked: .constant(false))
                .disabled(true)
               
                
            
        }
        .padding(.horizontal, 15.0.relativeWidth)
        .padding(.vertical, 13.0.relativeWidth)
        .frame(width: 351.0.relativeWidth, alignment: .topTrailing)
        .frame(minHeight: 137.0.relativeHeight, alignment: .topTrailing)
        .background(ColorConstants.bgGradient.opacity(0.001))
        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                bottomRight: 13.0)
                .stroke(ColorConstants.Cyan800,
                        lineWidth: 1))
    }
}

#Preview {
    NavigationStack {
        AddressListingShimmerView().attachAllEnvironmentObjects()
    }
}
