//
//  AddressView.swift
//  Bookme
//
//  Created by Apple on 05/05/2024.
//

import SwiftUI

struct AddressListingView: View {
    
    
    @Environment(RouterManager.self) private var routerManager
    @EnvironmentObject private var appState: AppState
    @StateObject private var viewModel:AddressViewModel = AddressViewModel()
    var body: some View {
        
        
        SuperView(pageState: $viewModel.pageState, loadingView: {
            AddressListingShimmerView()
                .background(ColorConstants.bgGradient)
        }, content: {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Address") {
                VStack(spacing:10.0.relativeHeight){
                    
                    
                    if viewModel.addressList.isEmpty {
                        CustomPlaceholder(placeholderType: .noData,title: "You don’t have an address listed", subTitle: "", image: .noAddress, size: .init(width: 78.relativeFontSize, height: 78.relativeFontSize), titleColor: .white)
                            .padding(.top, 32.relativeHeight)
                    }else{
                        ForEach(viewModel.addressList){ model in
                        
                            Button(action: {
                                
                                if !model.isDefault {
                                    viewModel.makeAddressDefault(id: model.addressID)
                                }
                                
                                
                            }, label: {
                                AddressCellView(model: model,checked: .constant(model.isDefault))
                                    .clipShape(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                              bottomRight: 13.0))
                                    .onSlide(editAction: {
                                        routerManager.push(to: .addAddress(model: model, onDone: viewModel.onDone), where: .myAccountRoute)
                                    }, deleteAction: {
                                        viewModel.onDeleteAddress(id: model.addressID)
                                    })
                            })
                        }
                    }
                    
                    
                   
                }
                .padding(.top)
            }
            .background(ColorConstants.bgGradient)
            .if(viewModel.pageState != .loading(true), transform: {
                $0.safeAreaInset(edge: .bottom) {
                    Button(action: {
                        
                        
                        
                        routerManager.push(to: .addAddress(model: nil, onDone: viewModel.onDone),where: .myAccountRoute)
                        
                    }, label: {
                        HStack(spacing: 0) {
                            Text("Add Address")
                                .font(FontScheme
                                    .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.heavy)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(15.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                               
                                .multilineTextAlignment(.leading)
//                                .frame(width: getRelativeWidth(320.0),
//                                       height: getRelativeHeight(50.0),
//                                       alignment: .center)
                                .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                .background(RoundedCorners(topLeft: 25.0,
                                                           topRight: 25.0,
                                                           bottomLeft: 25.0,
                                                           bottomRight: 25.0)
                                        .fill(ColorConstants.Cyan800))
                                .shadow(color: ColorConstants.Black90044, radius: 2.5,
                                        x: 0, y: 1)
                        }
                    })
                    .padding(.top, 16.0.relativeHeight)
                    
                }
            })
            
        })
       
    }
}

#Preview {
    NavigationStack{
        AddressListingView().attachAllEnvironmentObjects()
    }
}
