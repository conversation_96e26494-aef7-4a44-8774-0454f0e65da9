//
//  AddressModel.swift
//  Bookme
//
//  Created by Apple on 06/05/2024.
//

import Foundation



// MARK: - AddressModel
struct AddressModel: Codable, Identifiable, Equatable, Hashable {
    let id:UUID = .init()
    let addressID, customerID: Int
    let name, phoneNumber , area: String
    let eMail, avenue: String?
    let block, street, houseNo, defaultAddress: String
    
    
    var isDefault:Bool{
        defaultAddress.lowercased() == "yes"
    }
    
    var address:String{
        var value:String = .init()
        
        if !houseNo.isEmpty {
            value += "\(houseNo), "
        }
        
        if let avenue = avenue, !avenue.isEmpty {
            value += "\(avenue), "
        }
        
        if !street.isEmpty {
            value += "\(street), "
        }
        
        if !block.isEmpty {
            value += "\(block), "
        }
        
        if !area.isEmpty {
            value += "\(area)"
        }
        
        
        return value
    }
    
//    "4th Floor, Al Zumorrodah Tower Ahmed Al Jaber st, Kuwait"

    enum CodingKeys: String, CodingKey {
        case addressID = "Address_ID"
        case customerID = "Customer_ID"
        case name = "Name"
        case phoneNumber = "Phone_Number"
        case eMail = "E_mail"
        case area = "Area"
        case block = "Block"
        case street = "Street"
        case houseNo = "HouseNo"
        case avenue = "Avenue"
        case defaultAddress = "defaultaddress"
    }
}





