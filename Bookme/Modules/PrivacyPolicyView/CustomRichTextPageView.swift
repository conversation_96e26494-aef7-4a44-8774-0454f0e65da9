import SwiftUI

struct CustomRichTextPageView: View {
    let customRichTextPageType: CustomRichTextPageType
    @StateObject var viewModel: CustomRichTextPageViewModel
    @Environment(\.dismiss) var dismiss: DismissAction
    
    init(customRichTextPageType: CustomRichTextPageType) {
        self.customRichTextPageType = customRichTextPageType
        self._viewModel = StateObject(wrappedValue: CustomRichTextPageViewModel(customRichTextPageType: customRichTextPageType))
    }
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {} content: {
            MainScrollBody(invertColor: true, backButtonWithTitle: viewModel.privacyPolicyModel?.title.localize ?? "") {
                VStack(alignment: .leading, spacing: 0) {
                    if viewModel.pageState == .loading(true) {
                        let sampleHtml: String = "Book Me App Guarantees that reviews with the “Verified Book Me User” tag have been added by registered Book Me Useres who have had anappoinment with the provider. A registered Book Me user has the opportunity to add a review only after the service has been provides to them"
                        
                        Text(String(repeating: sampleHtml, count: 5))
                            .font(.custom("Nunito-Regular", size: 16))
                            .padding(.horizontal, getRelativeWidth(20.0))
                            .padding(.top, getRelativeHeight(16.0))
                            .shimmerize()
                        
                    } else {
                        if
                            let privacyPolicyModel = viewModel.privacyPolicyModel,
                            !privacyPolicyModel.description.isEmpty
                        
                        {
                            CustomRichText(html: privacyPolicyModel.description, fontFamily: "Nunito-Regular", fontSrc: "NunitoRegular.ttf", sizeAdjust: "80", fontColor: ColorConstants.Black900, lineHeight: 110.0.relativeFontSize)
                                .padding(.horizontal, getRelativeWidth(20.0))
                                .padding(.top, getRelativeHeight(16.0))
                        }
                    }
                }
            }
                
        }.background(ColorConstants.WhiteA700)
    }
}

struct PrivacyPolicyView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            CustomRichTextPageView(customRichTextPageType: .privacy).attachAllEnvironmentObjects()
        }
    }
}
