//
//  PrivacyPolicyModel.swift
//  Bookme
//
//  Created by Apple on 07/05/2024.
//

import Foundation


enum CustomRichTextPageType: Equatable, Hashable {
    case privacy, terms, paymentAndCancellation(String)
}

// MARK: - PrivacyPolicyModel
struct RichTextPageModel: Codable {
//    let privacyID: Int
    let title, description: String

    enum CodingKeys: String, CodingKey {
//        case privacyID = "Privacy_ID"
        case title = "Title"
        case description = "Description"
    }
}



