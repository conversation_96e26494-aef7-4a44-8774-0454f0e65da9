import Foundation
import SwiftUI

class CustomRichTextPageViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    
    @Published var privacyPolicyModel: RichTextPageModel?
    
    let customRichTextPageType: CustomRichTextPageType
    
    init(customRichTextPageType: CustomRichTextPageType) {
        self.customRichTextPageType = customRichTextPageType
         
        super.init()
        getRichTextPageData(customRichTextPageType)
    }
    
    func getRichTextPageData(_ customRichTextPageType: CustomRichTextPageType) {
        switch customRichTextPageType {
        case .privacy:
            onApiCall(api.privacyPolicy, parameters: emptyDictionary) {
                self.privacyPolicyModel = $0.data
            }
        case .terms:
            onApiCall(api.termsConditions, parameters: emptyDictionary) {
                self.privacyPolicyModel = $0.data
            }
        case let .paymentAndCancellation(data):
            privacyPolicyModel = .init(title: "Payment & Cancellation policy", description: data)
        }
    }
}
