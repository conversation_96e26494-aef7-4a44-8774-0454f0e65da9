//
//  CustomRichTextView.swift
//  Bookme
//
//  Created by Apple on 07/05/2024.
//

import SwiftUI
import RichText

struct CustomRichText: View {
    let html, fontFamily, fontSrc, sizeAdjust: String
    let fontColor:Color
    var lineHeight:CGFloat = 100
    var body: some View {
        RichText(html: html)
            .lineHeight(lineHeight)
            .colorScheme(.auto)
            .imageRadius(12)
            .fontType(.customName(fontFamily))
            .foregroundColor(light: fontColor, dark: fontColor)
            .linkColor(light: fontColor, dark: fontColor)
            .colorPreference(forceColor: .onlyLinks)
            .linkOpenType(.SFSafariView())
            .customCSS("""
             @font-face {
                    font-family: '\(fontFamily)';
                    src: url("\(fontSrc)") format('truetype');
                    size-adjust: \(sizeAdjust)%;
                    }
            """)
            .placeholder {
                Text("loading")
            }
            .transition(.easeOut)
    }
}
