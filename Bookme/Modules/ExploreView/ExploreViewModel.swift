import Combine
import SwiftUI

class ExploreViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var searchText: String = .init()
    @Published var languageText: String = ""
    @Published var selectedHeader: ServicesCategoryModel?
    @Published var filtersRequestModel: FiltersRequest?
    @Published var vendorsList: [NearestVendorModel] = []
    @Published var servicesList: [ServicesCategoryModel] = []
    @Published var showFilterIndicator: Bool = false
    
    @Published var state: SearchResultState = .good {
        didSet {
            print("state changed to: \(state)")
            print("page number: \(pageCount)")
        }
    }
    
    var pageCount: Int = 1
    let pageLimit: Int = 20
    var totalCount = 0
    
    var isFilterApplied: Bool { filtersRequestModel != nil }
    
    var serviceCategorySubscriptions = Set<AnyCancellable>()
    
    override init() {
        super.init()
        
        initialiseAll()
        getServiceCategory()
    }
    
    func getServiceCategory() {
        onApiCall(api.servicesCategory, parameters: emptyDictionary) { self.servicesList = $0.data ?? [] }
    }
    
    func initialiseAll() {
        $searchText
            .removeDuplicates()
            .dropFirst()
            .debounce(for: .seconds(0.5), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.state = .good
                self?.resetSearchProperties()
                self?.getSearchVendors()
            }.store(in: &serviceCategorySubscriptions)
        
        $selectedHeader
            .removeDuplicates()
            .dropFirst()
            .debounce(for: .seconds(0.5), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                
                if !(self?.isFilterApplied ?? false) {
                    //   self?.filtersRequestModel = nil
                    self?.resetSearchProperties()
                    self?.getVendorsWithParams()
                }
               
            }.store(in: &serviceCategorySubscriptions)
    }
    
    func checkHeader(_ value: ServicesCategoryModel) -> Bool {
        value.catID == selectedHeader?.catID
    }
    
    func updateHeaderList(_ value: ServicesCategoryModel?) { selectedHeader = value }
    
    func onChangeFiltersRequest(oldValue: FiltersRequest?, newValue: FiltersRequest?) {
//        showFilterIndicator = newValue != nil
//
//        if newValue != nil {
//            getVendorsWithParams()
//        }
        getVendorsWithParams()
    }
    
    func getSearchVendors() {
        guard state == .good else { return }
        state = .isLoading
        getVendorsWithParams(showLoading: false)
    }
    
    func resetSearchProperties() {
        vendorsList.removeAll()
        pageCount = 1
    }
    
    func loadMore() {
        if !vendorsList.isEmpty {
            guard state == .good else { return }
            state = .isLoading
            getVendorsWithParams(showLoading: false)
        }
    }
    
    func getVendorsWithParams(showLoading: Bool = true) {
        var parameters: [String: Any] = [:]
        let genderList = AppState.serviceType.map { $0.uppercased() }.joined(separator: ",")
        if let request = filtersRequestModel {
            if let category = request.category {
                selectedHeader = servicesList.first(where: { $0.catIDInt == Int(category) })
            }
            parameters.updateValue(request.gender ?? genderList, forKey: "gender")
            if let service = request.service {
                parameters.updateValue(service, forKey: "service")
            } else {
                if let category = request.category {
                    parameters.updateValue(category, forKey: "service")
                }
            }
            if let times = request.times { parameters.updateValue(times, forKey: "times") }
            if let rating = request.rating { parameters.updateValue(rating, forKey: "ratings") }
            if let distance = request.distance { parameters.updateValue(distance, forKey: "distance") }
            if let latitude = request.latitude { parameters.updateValue(latitude, forKey: "lat") }
            if let longitude = request.longitude { parameters.updateValue(longitude, forKey: "lng") }
           
        } else {
            guard let coordination = AppState.userLocation else { return }
            parameters.updateValue(genderList, forKey: "gender")
            
            let catIDInt = selectedHeader?.catIDInt ?? 0
            let requestService = catIDInt == 0 ? "All" : catIDInt.toString
            parameters.updateValue(requestService, forKey: "service")
            let requestSearchString = searchText
            parameters.updateValue(requestSearchString, forKey: "searchstring")
            parameters.updateValue(coordination.latitude, forKey: "lat")
            parameters.updateValue(coordination.longitude, forKey: "lng")
        }
        
        // ✅ Add this block here (applies to both cases above)
//        parameters["page"] = pageCount
//        parameters["limit"] = pageLimit
        
        getVendors(parameters, showLoading: showLoading)
    }
    
    func getVendors(_ parameters: [String: Any], showLoading: Bool) {
        vendorsList = []
        
        onApiCall(api.vendorExplore, parameters: parameters, withStateChange: showLoading, withLoadingIndicator: showLoading) {
            let data = $0.data ?? []
            self.vendorsList.append(contentsOf: data)
            self.vendorsList.removeDuplicates(by: \.vendorID)
            self.pageCount += 1
            self.totalCount = data.count
//            let isAllLoaded = (self.vendorsList.count == data.count)
            let isAllLoaded = data.count < self.pageLimit
            self.state = isAllLoaded ? .loadedAll : .good
            self.updatePageState(.stable)
        } onFailure: { _ in
            self.state = .error("")
            self.updatePageState(.stable)
        }
    }
}

extension Int {
    var toString: String { String(self) }
}

extension Array where Element: Any {
    mutating func removeDuplicates<T: Hashable>(by keyPath: KeyPath<Element, T>) {
        var seen = Set<T>()
        self = filter { seen.insert($0[keyPath: keyPath]).inserted }
    }
}
