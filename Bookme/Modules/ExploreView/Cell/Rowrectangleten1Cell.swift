import SwiftUI

struct Rowrectangleten1Cell: View {
    var body: some View {
        HStack {
            ZStack(alignment: .bottomTrailing) {
                Image("img_rectangle10")
                    .resizable()
                    .frame(width: getRelativeWidth(142.0), height: getRelativeHeight(149.0),
                           alignment: .leading)
                    .scaledToFit()
                    .background(RoundedCorners(topLeft: 72.33, topRight: 72.33, bottomLeft: 72.33,
                                               bottomRight: 72.33))
                VStack {
                    Image("img_vector_yellow_a700")
                        .resizable()
                        .frame(width: getRelativeWidth(7.0), height: getRelativeWidth(9.0),
                               alignment: .leading)
                        .scaledToFit()
                        .padding(.top, getRelativeHeight(5.0))
                        .padding(.horizontal, getRelativeWidth(12.0))
                    Text(StringConstants.kLbl43)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(15.0), height: getRelativeWidth(17.0),
                               alignment: .leading)
                        .padding(.horizontal, getRelativeWidth(8.0))
                }
                .frame(width: getRelativeWidth(31.0), height: getRelativeHeight(35.0),
                       alignment: .leading)
                .background(RoundedCorners(topLeft: 16.99, topRight: 16.99, bottomLeft: 16.99,
                                           bottomRight: 16.99)
                        .fill(ColorConstants.Cyan800))
                .padding(.top, getRelativeHeight(109.0))
                .padding(.leading, getRelativeWidth(111.71))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(143.0), height: getRelativeHeight(149.0),
                   alignment: .leading)
            VStack(alignment: .leading, spacing: 0) {
                Text(StringConstants.kMsgBroadwayBarber)
                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(15.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(124.0), height: getRelativeHeight(41.0),
                           alignment: .leading)
                    .padding(.trailing)
                Text(StringConstants.kMsg4thFloorAlZ)
                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Gray800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(169.0), height: getRelativeHeight(33.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(14.0))
                HStack {
                    Image("img_map")
                        .resizable()
                        .frame(width: getRelativeWidth(7.0), height: getRelativeHeight(11.0),
                               alignment: .leading)
                        .scaledToFit()
                    Text(StringConstants.kLbl6Km)
                        .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Black900B2)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(26.0), height: getRelativeHeight(17.0),
                               alignment: .leading)
                        .padding(.leading, getRelativeWidth(7.0))
                }
                .frame(width: getRelativeWidth(43.0), height: getRelativeHeight(17.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(16.0))
                .padding(.trailing, getRelativeWidth(10.0))
            }
            .frame(width: getRelativeWidth(170.0), height: getRelativeHeight(123.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(24.0))
            .padding(.trailing, getRelativeWidth(17.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners().stroke(ColorConstants.Cyan80033, lineWidth: 1))
        .background(RoundedCorners().fill(Color.clear.opacity(0.7)))
        .hideNavigationBar()
    }
}

/* struct Rowrectangleten1Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Rowrectangleten1Cell()
 }
 } */
