import SwiftUI

struct ExploreView: View {
    @StateObject private var viewModel = ExploreViewModel()
    @Environment(\.dismiss) var presentationMode: DismissAction
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager
    @Namespace private var animation
    @FocusState var isFocused: Bool
    var body: some View {
        SuperView(pageState: $viewModel.pageState, loadingView: {}, content: {
            VStack {
                VStack(spacing: -16) {
                    CustomNavigationBar()
                    SearchHeaderView(routesType: .exploreRoute, hideFilter: false, disableSearch: false, content: {
                        Button(action: {
                            routerManager.push(to: .saloonMap, where: .exploreRoute)
                        }, label: {
                            HStack(spacing: 9.0.relativeWidth) {
                                Image("img_vector_cyan_800")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(20.0),
                                           height: getRelativeWidth(20.0), alignment: .center)
                            }
                            .padding(.horizontal, getRelativeWidth(15.0))
                            
                            //                            .frame(width: 89.0.relativeWidth, height: 35.0.relativeHeight)
                            .frame(height: getRelativeHeight(45.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                                    bottomRight: 10.0)
                                    .stroke(ColorConstants.Cyan901,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 10.0, topRight: 10.0,
                                                       bottomLeft: 10.0, bottomRight: 10.0)
                                    .fill(ColorConstants.Teal90093))
                        })
                        
                    }, filterIndicate: $viewModel.showFilterIndicator, searchText: $viewModel.searchText, isFocused: _isFocused, onFilter: {
                        var filter = viewModel.filtersRequestModel ?? .init()
                        
                        if let categoryID = viewModel.selectedHeader?.catIDInt {
                            filter.category = String(categoryID)
                        }
                        
                        routerManager.push(to: .filter(model: filter, onDone: .init(onBack: { request in
                            viewModel.filtersRequestModel = request
                            
                        })), where: .exploreRoute)
                    })
                }
                .onChange(of: viewModel.filtersRequestModel, viewModel.onChangeFiltersRequest)
                
                VStack(alignment: .leading, spacing: 0) {
                    VStack(spacing: 0) {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(alignment: .top, spacing: 24.0.relativeWidth) {
                                ForEach(viewModel.servicesList) { item in
                                    Button(action: {
                                        viewModel.updateHeaderList(item)
                                    }, label: {
                                        VStack(alignment: .center, spacing: 2) {
                                            Text(LocalizedStringKey(item.catName))
                                                .font(FontScheme
                                                    .kNunitoMedium(size: 14.0.relativeFontSize))
                                                .fontWeight(.medium)
//                                                .foregroundColor(appState.checkHeader(item) ? ColorConstants.Cyan800 : ColorConstants.Cyan800.opacity(0.51))
                                                .foregroundColor(viewModel.checkHeader(item) ? Color.white : Color(hex: "#6D7273"))
                                                .scaleEffect(viewModel.checkHeader(item) ? 1.1 : 1.0, anchor: .bottom)
                                             
                                            if viewModel.checkHeader(item) {
                                                Divider()
                                                    .frame(
                                                        height: getRelativeHeight(2.0), alignment: .center)
                                                    .background(ColorConstants.Cyan800)
                                                    .matchedGeometryEffect(id: "header.divider", in: animation)
                                            }
                                        }
                                    })
                                }
                            }
                            .animation(.bouncy, value: viewModel.selectedHeader)
                            .sync($viewModel.selectedHeader, with: $appState.selectedHeader)
                            .onLoad {
//                                self.viewModel.servicesList = self.appState.servicesList
                            }
                            
                            .padding(.horizontal)
                        }
                       
                        .padding(.top)
                        Divider()
                            .frame(
                                height: getRelativeHeight(2.0), alignment: .leading)
                            .background(ColorConstants.Cyan800)
                    }
//                    .background(ColorConstants.bgGradient)
                    
                    if viewModel.pageState == .loading(true) {
                        ExploreShimmerView()
                            .background(.white)
                    } else {
                        ScrollView(.vertical, showsIndicators: false) {
                            VStack(alignment: .leading, spacing: 0) {
                                VStack(spacing: 0) {
                                    ScrollView(.vertical, showsIndicators: false) {
                                        LazyVStack {
                                            Section {
                                                ForEach(viewModel.vendorsList) { item in
                                                    VStack {
                                                        SaloonCardCell(model: item, routesType: .exploreRoute)
                                                        if item != appState.nearestVendorList.last {
                                                            Divider()
                                                                .padding(.horizontal)
                                                        }
                                                    }
                                                }
                                            }
                                            footer: {
                                                if viewModel.vendorsList.isEmpty && viewModel.state != .isLoading {
                                                    if viewModel.searchText.isEmpty {
//                                                            ContentUnavailableView("No Result", systemImage: "magnifyingglass", description: Text("No result found for your selection"))
                                                            
                                                        CustomPlaceholder(placeholderType: .noService, title: nil, subTitle: "No Services found near your location")
                                                            
                                                    } else {
                                                        ContentUnavailableView.search(text: viewModel.searchText)
                                                    }
                                                }
                                                
                                                //                                            Group {
                                                //                                                switch viewModel.state {
                                                //                                                case .good:
                                                //                                                    Color.clear
                                                //                                                        .onAppear {
                                                //                                                            viewModel.loadMore()
                                                //                                                        }
                                                //                                                case .isLoading:
                                                //                                                    ProgressView()
                                                //                                                        .frame(maxWidth: .infinity)
                                                //                                                case .loadedAll:
                                                //                                                    EmptyView()
                                                //                                                case .error(let message):
                                                //                                                    Text(message)
                                                //                                                        .foregroundColor(.red)
                                                //                                                }
                                                //                                            }.padding(.bottom)
                                            }
                                        }
                                    }
                                }
                                .frame(width: getRelativeWidth(361.0), alignment: .center)
                                .padding(.vertical, getRelativeHeight(30.0))
                                .padding(.leading, getRelativeWidth(17.0))
                                .padding(.trailing, getRelativeWidth(12.0))
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(ColorConstants.WhiteA700)
                        }
                        .scrollDismissesKeyboard(.immediately)
                        .background(ColorConstants.WhiteA700)
                    }
                }
            }
            .onLoad {
                viewModel.selectedHeader = appState.selectedHeader ?? appState.servicesList.first
            }
            .sync($appState.selectedHeader, with: $viewModel.selectedHeader)
            .onDisappear {
                appState.updateIsSearchFocused(false)
            }
            
            .onAppear {
                Utilities.enQueue(after: .now() + 0.2) {
                    self.isFocused = appState.isSearchFocused
                    print("isFocused: \(self.isFocused)", "appState.isSearchFocused: \(appState.isSearchFocused)")
                    print("Block ends")
                }
            }
        })
        .onChange(of: routerManager.exploreRouteList) { _, newValue in
            
            appState.updateTabBarHidden(!newValue.isEmpty)
        }
        .handleDeepLinkNavigation(routesType: .exploreRoute)
        .id(appState.rootViewId)
    }
}

struct ExploreView_Previews: PreviewProvider {
    static var previews: some View {
        ExploreView().attachAllEnvironmentObjects()
            .background(ColorConstants.bgGradient)
    }
}
