//
//  FirstResponderTextField.swift
//  Bookme
//
//  Created by Apple on 06/10/2024.
//

import SwiftUI
import UIKit

// Step 1: Create a UIViewRepresentable for UITextField
struct FirstResponderTextField: UIViewRepresentable {
    let placeholder:String
    @Binding var text: String
    @Binding var isFirstResponder: Bool
    
    class Coordinator: NSObject, UITextFieldDelegate {
        let placeholder:String
        @Binding var text: String
        @Binding var isFirstResponder: Bool
        
        init(placeholder:String, text: Binding<String>, isFirstResponder: Binding<Bool>) {
            self.placeholder = placeholder
            _text = text
            _isFirstResponder = isFirstResponder
        }

        // Update the text binding when the text field changes
        func textFieldDidChangeSelection(_ textField: UITextField) {
            self.text = textField.text ?? ""
        }
    }
    
    func makeCoordinator() -> Coordinator {
        return Coordinator(placeholder: placeholder ,text: $text, isFirstResponder: $isFirstResponder)
    }
    
    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField(frame: .zero)
        textField.delegate = context.coordinator
        textField.text = text
        textField.placeholder = placeholder
        return textField
    }
    
    func updateUIView(_ uiView: UITextField, context: Context) {
        uiView.text = text
        
        if isFirstResponder {
            uiView.becomeFirstResponder() // Make the UITextField the first responder (focus)
        } else {
            uiView.resignFirstResponder() // Resign focus if needed
        }
    }
}
