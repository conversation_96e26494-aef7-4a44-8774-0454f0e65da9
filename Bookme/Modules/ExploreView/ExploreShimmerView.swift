//
//  ExploreShimmerView.swift
//  Bookme
//
//  Created by Apple on 28/08/2024.
//

import SwiftUI


struct ExploreShimmerView: View {
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            VStack(alignment: .leading, spacing: 0) {
                VStack(spacing: 0) {
                    ScrollView(.vertical, showsIndicators: false) {
                        LazyVStack {
                            
                            Section {
                                ForEach(0...7, id: \.self) { index in
                                    VStack {
                                        SaloonCardShimmerCell()
                                            Divider()
                                                .padding(.horizontal)
                                        
                                    }
                                }
                            }
                           
                        }
                    }
                }
                .frame(width: getRelativeWidth(361.0), alignment: .center)
                .padding(.vertical, getRelativeHeight(30.0))
                .padding(.leading, getRelativeWidth(17.0))
                .padding(.trailing, getRelativeWidth(12.0))
            }
            .background(ColorConstants.WhiteA700)
        }
        .background(ColorConstants.WhiteA700)
        .shimmerize()
    }
}

struct SaloonCardShimmerCell: View {
    var body: some View {
        Button {
           
        } label: {
            HStack {
                ZStack(alignment: .bottomTrailing) {
                    NetworkImageView(path: nil, contentMode: .fill)
                        .frame(width: getRelativeWidth(147.0), height: getRelativeWidth(149.0),
                               alignment: .center)
                        .scaledToFit()
                        .clipShape(Circle())
                       
                    VStack(spacing: 4) {
                        Image("img_vector_yellow_a700")
                            .resizable()
                            .scaledToFit()
                            .frame(width: getRelativeWidth(9.0), height: getRelativeWidth(9.0),
                                   alignment: .leading)
                           
                            //                        .padding(.top, getRelativeHeight(5.0))
                            .padding(.horizontal, getRelativeWidth(13.0))
                        Text("model.rating")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(16.0), height: getRelativeHeight(14.0),
                                   alignment: .center)
                            .padding(.horizontal, getRelativeWidth(9.0))
                    }
                    .frame(width: getRelativeWidth(33.0), height: getRelativeWidth(35.0),
                           alignment: .center)
                    .background(Circle()
                        .fill(ColorConstants.Cyan800))
                    .padding(.top, getRelativeHeight(109.0))
                    .padding(.leading, getRelativeWidth(113.0))
                }
                .hideNavigationBar()
                .frame(width: getRelativeWidth(147.0), height: getRelativeWidth(149.0),
                       alignment: .leading)
                VStack(alignment: .leading, spacing: 0) {
                    Text("model.vendorName")
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(15.0)))
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.Cyan800)
                        .fixedSize(horizontal: false, vertical: true)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                        .frame(width: getRelativeWidth(154.0),
                               alignment: .leading)
                    Text("model.addressmodel.address model.addressmodel.address")
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Gray900B2)
                        .lineLimit(4)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(169.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(14.0))
                    HStack {
                        Image("img_vector_cyan_800")
                            .resizable()
                            .scaledToFit()
                            .frame(width: getRelativeWidth(11.0), height: getRelativeHeight(11.0),
                                   alignment: .leading)
                            
                        Text("\(0) KM")
                            .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.semibold)
                            .foregroundColor(ColorConstants.Black900B2)
                            
                            .multilineTextAlignment(.leading)
                            .frame( height: getRelativeHeight(17.0),
                                   alignment: .leading)
                    }
                    .frame( height: getRelativeHeight(17.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(16.0))
                    .padding(.trailing, getRelativeWidth(10.0))
                }
                .frame(width: getRelativeWidth(170.0), height: getRelativeHeight(123.0),
                       alignment: .leading)
                .padding(.leading, getRelativeWidth(26.0))
                .padding(.trailing, getRelativeWidth(12.0))
            }
            .frame(width: getRelativeWidth(358.0), alignment: .leading)
            .background(RoundedCorners().fill(Color.clear.opacity(0.7)))
            .hideNavigationBar()
        }
    }
}
