import Foundation
import SwiftUI

class PasswordManagerViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var currentPasswordText: String = ""
    @Published var newPasswordText: String = ""
    @Published var confirmPasswordText: String = ""
    @Published var invalidTextFiledText: [String] = Array(repeating: "", count: 3)
    
    let currentPasswordIndex: Int = 0
    let newPasswordIndex: Int = 1
    let confirmPasswordIndex: Int = 2
    
    
    var disableSubmitButton: Bool {
       !Utilities.isValidInputs(inputs: [currentPasswordText, newPasswordText, confirmPasswordText])
    }
    
    func onChangePassword(completion: @escaping () -> Void) {
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": "\(userID)", "password": "\(newPasswordText)", "current_password": currentPasswordText]
        onApiCall(api.changePassword, parameters: parameters) {
            if $0.success {
                completion()
            }
        }
    }
}
