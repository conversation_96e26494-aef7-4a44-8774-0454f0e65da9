import SwiftUI

struct PasswordManagerView: View {
    @StateObject var viewModel = PasswordManagerViewModel()
    @Environment(RouterManager.self) var routerManager: RouterManager
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Password Manager") {
                VStack(alignment: .center) {
                  
                   
                    CustomTextField(title:"Current Password", placeholder: StringConstants.kMsg, text: self.$viewModel.currentPasswordText, invalidText: self.viewModel.invalidTextFiledText[viewModel.currentPasswordIndex], isSecure: true)
                        .keyboardType(.default)
                        .textContentType(.oneTimeCode)
                        .frame(width: getRelativeWidth(321.0), alignment: .center)
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        .onChange(of: self.viewModel.currentPasswordText) { _, newValue in
                            withAnimation(.bouncy) {
                                self.viewModel.invalidTextFiledText[viewModel.currentPasswordIndex] = newValue.isEmptyOrWhitespace
                                    ? "Current Password field can't be empty"
                                    : ""
                            }
                        }
                   
                    VStack(alignment: .trailing, spacing: 0) {
                        Button(action:  {
                            routerManager.push(to: .forgotPassword(type: .myAccountRoute), where: .myAccountRoute)
                        }, label: {
                            Text("Forgot Password?")
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(114.0), height: getRelativeHeight(18.0),
                                       alignment: .topLeading)
                        })
                    }
                    .frame(maxWidth: .infinity, alignment: .trailing)
                    .padding(.top, getRelativeHeight(14.0))
                    .padding(.horizontal, getRelativeWidth(22.0))
                    
                    
                    
                   
                    CustomTextField(title: "New Password", placeholder: StringConstants.kMsg, text: self.$viewModel.newPasswordText, invalidText: self.viewModel.invalidTextFiledText[viewModel.newPasswordIndex], isSecure: true)
                        .keyboardType(.default)
                        .textContentType(.oneTimeCode)
                        .frame(width: getRelativeWidth(321.0), alignment: .center)
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        .onChange(of: self.viewModel.newPasswordText) { _, newValue in
                            withAnimation(.bouncy) {
                                self.viewModel.invalidTextFiledText[viewModel.newPasswordIndex] = newValue.isEmptyOrWhitespace
                                    ? "New Password field can't be empty"
                                    : ""
                            }
                        }
                    
                    
                    
                    CustomTextField(title: "Confirm new password", placeholder: StringConstants.kMsg, text: self.$viewModel.confirmPasswordText, invalidText: self.viewModel.invalidTextFiledText[viewModel.confirmPasswordIndex], isSecure: true)
                        .keyboardType(.default)
                        .textContentType(.oneTimeCode)
                        .frame(width: getRelativeWidth(321.0), alignment: .center)
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        .onChange(of: self.viewModel.confirmPasswordText) { _, newValue in
                            withAnimation(.bouncy) {
                                self.viewModel.invalidTextFiledText[viewModel.confirmPasswordIndex] = newValue.isEmptyOrWhitespace
                                    ? "Confirm Password field can't be empty"
                                    : ""
                            }
                        }
                    
                   
                  
                }
               
            }.background(ColorConstants.bgGradient)
                .safeAreaInset(edge: .bottom) {
                    Button(action: {
                        viewModel.onChangePassword {
                            routerManager.goBack(where: .myAccountRoute)
                            routerManager.push(to: .changePasswordSuccess(type: .myAccountRoute, fromForgotPassword: false), where: .myAccountRoute)
                        }
                        
                    }, label: {
                        HStack(spacing: 0) {
                            Text("Change password")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.heavy)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(15.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                               
                                .multilineTextAlignment(.leading)
                                .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                           bottomLeft: 25.0, bottomRight: 25.0)
                                        .fill(ColorConstants.Cyan800))
                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                .padding(.leading, getRelativeWidth(13.0))
                        }
                    })
    //                .padding(.bottom, (AppConstants.tabBarHeight + 16).relativeHeight)
                    .disableWithOpacity(viewModel.disableSubmitButton)
                }
        }
      
    }
}

struct PasswordManagerView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            PasswordManagerView().attachAllEnvironmentObjects()
        }
    }
}
