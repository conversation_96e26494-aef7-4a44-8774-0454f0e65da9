import SwiftUI

struct FaqitemCell: View {
    var body: some View {
        HStack {
            HStack {
                Image("img_globelight")
                    .resizable()
                    .frame(width: getRelativeWidth(19.0), height: getRelativeWidth(21.0),
                           alignment: .leading)
                    .scaledToFit()
                Text("Website")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(52.0), height: getRelativeHeight(20.0),
                           alignment: .leading)
                    .padding(.leading, getRelativeWidth(13.0))
            }
            .frame(width: getRelativeWidth(86.0), height: getRelativeHeight(22.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(21.0))
            Image("img_arrowup")
                .resizable()
                .frame(width: getRelativeWidth(10.0), height: getRelativeHeight(6.0),
                       alignment: .leading)
                .scaledToFit()
                .padding(.leading, getRelativeWidth(210.0))
                .padding(.trailing, getRelativeWidth(22.0))
        }
        .frame(width: getRelativeWidth(351.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0, bottomRight: 15.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
                                   bottomRight: 15.0)
                .fill(ColorConstants.Cyan8003f))
        .hideNavigationBar()
    }
}

/* struct FaqitemCell_Previews: PreviewProvider {

 static var previews: some View {
 			FaqitemCell()
 }
 } */
