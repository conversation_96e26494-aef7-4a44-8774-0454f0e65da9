//
//  ContactUsModel.swift
//  Bookme
//
//  Created by Apple on 07/05/2024.
//

import Foundation

// MARK: - ContactUsModel
struct ContactUsModel: Codable, Identifiable {
    let id:UUID = .init()
    let contactID: Int
    let customerService, whatsapp, x: String
    let website, facebook, instagram: String

    enum CodingKeys: String, CodingKey {
        case contactID = "Contact_ID"
        case customerService = "customer_service"
        case x = "X"
        case whatsapp, website, facebook, instagram
    }
}

