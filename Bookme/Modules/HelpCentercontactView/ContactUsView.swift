import SwiftUI

struct ContactUsView: View {
    @StateObject var viewModel = ContactUsViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            if viewModel.pageState == .loading(true) {
                FAQShimmerView()
                    .shimmerize()
                        
            } else {
                ScrollView(.vertical, showsIndicators: false) {
                    if let contactUsModel = viewModel.contactUsModel {
                        VStack(alignment: .leading, spacing: 20.0.relativeHeight) {
                            ForEach(Array(viewModel.faqList.enumerated()), id: \.element.id) { index, item in
                                
                                VStack(spacing: 0) {
                                    But<PERSON>(action: {
                                        withAnimation(.bouncy) {
                                            viewModel.faqList[index].isExpanded.toggle()
                                        }
                                        
                                    }, label: {
                                        HStack(alignment: .center) {
                                            HStack(alignment: .center) {
                                                if let image = item.image {
                                                    Image(image)
                                                        .renderingMode(.template)
                                                        .resizable()
                                                        .scaledToFit()
                                                        .padding(index > 4 ? 2 : 0)
                                                        .frame(width: getRelativeWidth(21.0), height: getRelativeWidth(21.0),
                                                               alignment: .center)
                                                        
                                                        .clipped()
                                                }
                                                
                                                Text(item.question)
                                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                                    .fontWeight(.bold)
                                                    .multilineTextAlignment(.leading)
                                                    .padding(.leading, getRelativeWidth(12.0))
                                            }.foregroundColor(ColorConstants.Cyan800)
                                            
                                            Spacer()
                                            Image("img_arrowup")
                                                .resizable()
                                                .scaledToFit()
                                                .rotationEffect(.degrees(item.isExpanded ? 180 : 0))
                                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                                       alignment: .center)
                                            
                                                .clipped()
                                        }
                                        
                                        .padding(.vertical, getRelativeHeight(16.0))
                                        .padding(.horizontal, getRelativeWidth(16.0))
                                        .clipped()
                                        
                                    })
                                    
                                    VStack {
                                        VStack {
                                            Divider()
                                                .frame(width: getRelativeWidth(321.0), height: getRelativeHeight(1.0),
                                                       alignment: .center)
                                                .background(ColorConstants.Cyan8007f)
                                                .padding(.horizontal, getRelativeWidth(16.0))
                                            
                                            let answer = switch index {
                                            case 0: contactUsModel.customerService
                                            case 1: contactUsModel.whatsapp
                                            case 2: contactUsModel.website
                                            case 3: contactUsModel.facebook
                                            case 4: contactUsModel.instagram
                                            case 5: contactUsModel.x
                                            default:
                                                ""
                                            }
                                            
                                            VStack {
                                                CustomRichText(html: answer, fontFamily: "Nunito-Regular", fontSrc: "NunitoRegular.ttf", sizeAdjust: "80", fontColor: ColorConstants.Cyan800, lineHeight: 110.0.relativeFontSize)
                                                    .clipped()
                                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                                    .fontWeight(.regular)
                                                    .foregroundColor(ColorConstants.Cyan800B2)
                                                    .multilineTextAlignment(.leading)
                                                    .padding(getRelativeWidth(16.0))
                                                //                                                .disabled(true)
                                            }.frame(maxWidth: .infinity)
                                        }
                                        
                                        //                            .transition(.move(edge: .top))
                                        //                            .visibility(item.isExpanded ? .visible : .gone)
                                    }
                                    .frame(height: item.isExpanded ? nil : 0)
                                    .clipped()
                                }
                                
                                .clipped()
                                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
                                                        bottomRight: 15.0)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
                                                           bottomRight: 15.0)
                                        .fill(ColorConstants.Cyan8003f))
                            }
                        }
                        .padding(.vertical, 16.0.relativeHeight)
                        .padding(.horizontal, 16.0.relativeHeight)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
    }
}

struct HelpCentercontactView_Previews: PreviewProvider {
    static var previews: some View {
        ContactUsView()
            .background(ColorConstants.bgGradient)
    }
}
