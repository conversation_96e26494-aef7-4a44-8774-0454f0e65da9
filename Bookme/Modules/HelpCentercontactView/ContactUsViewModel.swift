import Foundation
import SwiftUI

class ContactUsViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    
    @Published var contactUsModel:ContactUsModel?
    
    @Published var faqList:[FAQSampleModel] = [
        .init(question: "Customer Service", answer: "+965 9539187941",image: "img_headphonesfill"),
        .init(question: "WhatsApp", answer: "+965 9539187941", image: "img_vector_cyan_800_20x19"),
        .init(question: "Website", answer: "www.bookme.com", image: "img_globelight"),
        .init(question: "Facebook", answer: "www.facebook/user/bookme", image: "img_itemsign_white_a700_65x65"),
        .init(question: "Instagram", answer: "www.instagram/user/bookme", image: "img_insta"),
        .init(question: "Twitter", answer: "www.x.com/user/bookme", image: "twitter"),
        
    ]

    
    override init() {
        super.init()
        getContactUsData()
    }
    
    
    func getContactUsData(){
        
        
        onApiCall(api.contactUs, parameters: emptyDictionary) {
            self.contactUsModel = $0.data
        }
    }
}
