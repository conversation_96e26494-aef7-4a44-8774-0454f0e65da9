//
//  DashboardViewModel.swift
//  Bookme
//
//  Created by Apple on 01/02/2024.
//

import SwiftUI

extension DashboardView {
    var selectionBinding: Binding<Tab> { Binding(
        get: {
            self.appState.selectedTab
        },
        set: {
            if $0 != self.appState.selectedTab {
                self.appState.selectedTab = $0
            } else {
//                if $0 == .shop() {
//                    appState.resetShopTabID()
//                }
//                appState.restRootViewID()
                routerManager.popToRoot(where: routerManager.mapTabTypeToRoutesType(from: self.appState.selectedTab))
            }
        }
    ) }
    
    
    
    func getRouterManagerPath(_ type: Tab) -> Binding<[Route]> {
        
        @Bindable var routerManager = routerManager
        
        switch type {
        case .home:
            return $routerManager.homeRouteList
        case .explore:
            return $routerManager.exploreRouteList

        case .appointment:
            return $routerManager.appointmentRouteList

        case .myAccount:
            return $routerManager.myAccountRouteList

      }
    }
    
}

extension DashboardView {
    final class ViewModel {
        func getRouterManagerPath(_ type: Tab, routerManager: Binding<RouterManager>) -> Binding<[Route]> {
            switch type {
            case .home:
                return routerManager.homeRouteList
            case .explore:
                return routerManager.exploreRouteList
            case .appointment:
                return routerManager.appointmentRouteList
            case .myAccount:
                return routerManager.myAccountRouteList
          
            }
        }
    }
}
