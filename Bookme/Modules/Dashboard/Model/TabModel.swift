//
//  TabModel.swift
//  Bookme
//
//  Created by Apple on 01/02/2024.
//

import SwiftUI

enum Tab: Identifiable, CaseIterable, Hashable {
    case home, explore, appointment, myAccount

    var id: Self { self }

    var image: String {
        switch self {
        case .home: "img_vector_white_a700"
        case .explore: "img_group_white_a700"
        case .appointment: "img_daterange_white_a700"
        case .myAccount: "img_usericon"
        }
    }

    var title: LocalizedStringKey {
        switch self {
        case .home: return "Home"
        case .explore: return "Explore"
        case .appointment: return "Appointments"
        case .myAccount: return "Account"
        }
    }

    var index: Int {
        return Tab.allCases.firstIndex(of: self) ?? 0
    }
}

enum MenuTab: String, Identifiable, CaseIterable, Hashable {
    case home, notification, search, booking, profile

    var id: UUID {
        switch self {
        case .home,
             .notification,
             .search,
             .booking,
             .profile: return .init()
        }
    }

    var title: String {
        switch self {
        case .home:
            return "Home"
        case .notification:
            return "Notification"
        case .search:
            return "Search Products"
        case .booking:
            return "Booking"
        case .profile:
            return "Profile"
        }
    }

    var image: String {
        switch self {
        case .home:
            return "img_home2_indigo_900"
        case .notification:
            return "img_vector_indigo_900_21x20"
        case .search:
            return "img_vector_indigo_900_18x18"
        case .booking:
            return "img_group_indigo_900"
        case .profile:
            return "img_vector_indigo_900_21x17"
        }
    }
}

extension Tab: Equatable {
    static func == (lhs: Tab, rhs: Tab) -> Bool {
        switch (lhs, rhs) {
        case (.home, .home),
             (.appointment, .appointment),
             (.myAccount, .myAccount),
             (.explore, .explore)
             : return true
        default:
            return false
        }
    }
}
