//
//  DashboardView.swift
//  Bookme
//
//  Created by Apple on 01/02/2024.
//

import SwiftUI

struct DashboardView: View {
    @Environment(RouterManager.self) var routerManager
    @EnvironmentObject var appState: AppState
    @EnvironmentObject private var deepLinkManager: DeepLinkManager

    var body: some View {
        SuperView(pageState: $appState.pageState) {
            ZStack {
                VStack(spacing: -AppConstants.tabBarHeight.relativeHeight) {
                    TabView(selection: selectionBinding) {
                        ForEach(Tab.allCases, id: \.self) { type in
                            NavigationStack(path: getRouterManagerPath(type)) {
                                type.view.navigationDestination(for: Route.self) { $0 }
//                                    .id(appState.rootViewId)
                                    .safeAreaPadding(.bottom, AppConstants.tabBarHeight.relativeHeight)
                                    .background(ColorConstants.bgGradient)
                            }
                            .tag(type)
                        }
                    }

                    if !appState.isTabBarHidden {
                        CustomTabBar()
                            .transition(.offset(x: 0, y: 100.relativeHeight))
                    }
                }
                SideMenu(content: AnyView(MyAccountView(isFromMenu: true)))
            }

            .ignoresSafeArea(.keyboard)
            .overlay(alignment: .bottom) {
                if appState.showLogoutPopupView {
                    Group {
                        ZStack {}
                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
                            .background(ColorConstants.Black90066)
                            .transition(.opacity)
                            .onTapGesture {
                                appState.updatePopUpView(false)
                            }
                        LogoutPopupView(showPopUp: $appState.showLogoutPopupView) {
                            self.appState.onLogout {
                                appState.onAuthentication(nil)
                                let route = routerManager.mapRouterWithTab(appState: appState)
                                routerManager.push(to: .signIn(type: route), where: route)
                            }
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
                        .transition(.move(edge: .bottom))
                        .padding(.bottom, -32.0.relativeHeight)
                    }
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .notificationTapped)) { notification in
            if let userInfo = notification.userInfo,
               let type = userInfo["type"] as? String {
                
            
                
                switch type {
                case "order":
                    guard let orderID = userInfo["order_id"] as? String
                    else {  print("Invalid or missing productID"); return}
                    // Handle product navigation logic
                    print("Navigate to product with ID: \(orderID)")
//                    routerManager.push(to: .productInfo( productID: Int(productID),selected: .init(onBack: { _ in })), where: routesType)
                    self.appState.updateSelectedTab(.appointment)
                    
                default:
                    print("Unhandled notification type: \(type)")
                }
            }
        }
    }

    /// Custom Tab Bar
    @ViewBuilder
    func CustomTabBar() -> some View {
        TabShape(midPoint: -100)
            .fill(Color(red: 0, green: 0.56, blue: 0.59))
            .clipShape(UnevenRoundedRectangle(topLeadingRadius: 40.relativeFontSize, topTrailingRadius: 40.relativeFontSize))
            .edgesIgnoringSafeArea(.bottom)
            .overlay {
                HStack(alignment: .top, spacing: 1) {
                    ForEach(Tab.allCases) {
                        TabItem(tab: $0, activeTab: selectionBinding)
                            .if(true, transform: {
                                $0.padding(.top)
                            })
                    }
                }
            }
            .frame(width: UIScreen.main.bounds.width, height: AppConstants.tabBarHeight.relativeHeight,
                   alignment: .leading)
//            .padding(.top, -AppConstants.tabBarTopPadding.relativeHeight)
    }
}

/// Tab Bar Item
struct TabItem: View {
    var tab: Tab
    @Binding var activeTab: Tab
//    @EnvironmentObject var cartManager: CartManager
    @Namespace private var animation

    var currentlyActiveTab: Bool { tab == activeTab }

    var body: some View {
        VStack(spacing: 2) {
            Image(tab.image)
                .renderingMode(.template)
                .animation(.bouncy, value: activeTab)
                .transition(.scale)
                .padding([.horizontal, .top],4)
                .frame(width: 30.0.relativeFontSize, height: 30.0.relativeFontSize)
                .clipped()

            Text(tab.title)
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(10.0)).weight(.semibold))
                .animation(.bouncy, value: activeTab)
                .transition(.flipFromBottom)
        }
        .foregroundStyle(activeTab == tab ? ColorConstants.WhiteA700 : ColorConstants.Black50)
        .animation(.easeInOut, value: activeTab)
        .frame(maxWidth: /*@START_MENU_TOKEN@*/ .infinity/*@END_MENU_TOKEN@*/)
        .contentShape(.rect)
        .onTapGesture {
            activeTab = tab
        }
//        .background(.orange)
    }
}

struct TabShape: Shape {
    var midPoint: CGFloat

    func path(in rect: CGRect) -> Path {
        return Path { path in
            path.addPath(Rectangle().path(in: rect))

            path.move(to: .init(x: midPoint - 60, y: 0))

            let to = CGPoint(x: midPoint, y: -25)
            let control1 = CGPoint(x: midPoint - 28, y: 0)
            let control2 = CGPoint(x: midPoint - 28, y: -25)

            path.addCurve(to: to, control1: control1, control2: control2)

            let to1 = CGPoint(x: midPoint + 60, y: 0)
            let control3 = CGPoint(x: midPoint + 28, y: -25)
            let control4 = CGPoint(x: midPoint + 28, y: 0)

            path.addCurve(to: to1, control1: control3, control2: control4)
        }
    }
}

#Preview {
    DashboardView().attachAllEnvironmentObjects()
}
