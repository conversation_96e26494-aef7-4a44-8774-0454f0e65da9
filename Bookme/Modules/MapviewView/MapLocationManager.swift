//
//  MapLocationManager.swift
//  Bookme
//
//  Created by Apple on 14/10/2024.
//

import CoreLocation
import SwiftUI

@Observable
class MapLocationManager: NSObject, CLLocationManagerDelegate {
    @ObservationIgnored let manager = CLLocationManager()
    
    var userLocation: CLLocation? {
        didSet {
            saveLocationToUserDefaults() // Save the updated location to UserDefaults
        }
    }

    var isAuthorized = false
    
    override init() {
        super.init()
        manager.delegate = self
        
        // Try loading the location from UserDefaults, if not available, start services
        if !loadLocationFromUserDefaults() {
            startLocationServices()
        }
    }
    
    func startLocationServices() {
        if manager.authorizationStatus == .authorizedAlways || manager.authorizationStatus == .authorizedWhenInUse {
            manager.startUpdatingLocation()
            isAuthorized = true
        } else {
            isAuthorized = false
            manager.requestWhenInUseAuthorization()
        }
    }
    
    func stopLocationServices() {
        manager.stopUpdatingLocation() // Stop updating location when no longer needed
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        userLocation = locations.last
    }
    
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        switch manager.authorizationStatus {
        case .authorizedAlways, .authorizedWhenInUse:
            isAuthorized = true
            manager.requestLocation()
        case .notDetermined:
            isAuthorized = false
            manager.requestWhenInUseAuthorization()
        case .denied:
            isAuthorized = false
            print("Access denied")
        default:
            isAuthorized = false
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print(error.localizedDescription)
    }
    
    // Store location in UserDefaults
    private func saveLocationToUserDefaults() {
        guard let location = userLocation else { return }
        // Correct latitude and longitude saving
        AppState.userLocation = .init(latitude: location.coordinate.latitude, longitude: location.coordinate.longitude)
    }
    
    // Load location from UserDefaults
    private func loadLocationFromUserDefaults() -> Bool {
        if let locationData = AppState.userLocation {
            isAuthorized = true
            userLocation = CLLocation(latitude: locationData.latitude, longitude: locationData.longitude)
            return true
        }
        return false
    }
}
