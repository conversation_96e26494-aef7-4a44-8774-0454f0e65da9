import MapKit
import SwiftUI

struct SaloonMapView: View {
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager
    @State private var locationManager: MapLocationManager = .init()
    @Environment(\.dismiss) var dismiss: DismissAction
    @StateObject var viewModel = SaloonMapViewModel()
    
    @Namespace var mapScope
    
    var body: some View {
        SuperView(pageState: self.$viewModel.pageState, loadingView: {
            SaloonMapShimmerView()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(.white)
        }, content: {
            VStack {
                ScrollViewReader { proxy in
                    VStack(spacing: 0) {
                        Map(position: self.$viewModel.cameraPosition,
                            interactionModes: .all,
                            scope: self.mapScope)
                        {
                            // Display user location with a custom annotation
                            UserAnnotation()

                            // Map Annotations for Vendors
                            ForEach(self.viewModel.saloonMapList) { model in
                                Annotation(model.vendorName, coordinate: model.coordinate) {
                                    Button(action: {
                                        self.selectAnnotation(model, proxy: proxy)
                                    }, label: {
                                        MapMarkerView(isSelected: self.viewModel.selectedAnnotation?.id == model.id)
                                    })
                                }
                            }
                        }
                        .animation(.bouncy, value: self.viewModel.cameraPosition)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .edgesIgnoringSafeArea(.all)
                        .onLoad {
                            self.updateCameraPosition()
                        }
                        .onDisappear {
                            self.locationManager.stopLocationServices()
                        }
                        
                        self.vendorListingSection(proxy: proxy)
                    }
                    .overlay(alignment: .top) {
                        self.navigationButtons()
                    }
                }
                .safeAreaPadding()
            }
            .hideNavigationBar()
            .background(ColorConstants.WhiteA700)
        })
    }
    
    // Function to update the camera position to the user's location
    func updateCameraPosition() {
        if let userLocation = locationManager.userLocation {
            let userRegion = MKCoordinateRegion(
                center: userLocation.coordinate,
                span: MapDetails.defaultSpan
            )
            Utilities.enQueue {
                self.viewModel.cameraPosition = .region(userRegion)
            }
        }
    }
    
    // Action to handle selecting a specific annotation (vendor)
    func selectAnnotation(_ model: SaloonMapModel.VendorModel, proxy: ScrollViewProxy) {
        Utilities.enQueue {
            self.viewModel.updateAnnotation(model)
            
            self.viewModel.cameraPosition = MapCameraPosition.region(MKCoordinateRegion(center: model.coordinate, span: model.span))
        }
        Utilities.enQueue(after: .now() + 0.5) {
            withAnimation(.bouncy) {
                proxy.scrollTo(model.id, anchor: .center)
            }
        }
    }
    
    // Vendor Listing Section with LazyHStack
    func vendorListingSection(proxy: ScrollViewProxy) -> some View {
        Group {
            if let _ = viewModel.selectedAnnotation {
                VStack {
                    ScrollView(.horizontal, showsIndicators: false) {
                        LazyHStack {
                            ForEach(self.viewModel.saloonMapList) { annotation in
                                let isSelected = annotation.id == self.viewModel.selectedAnnotation?.id
                                MapSaloonCardCell(model: annotation)
                                    .id(annotation.id)
                                    .background(.white)
                                    .opacity(isSelected ? 1.0 : 0.49)
                                    .onTapGesture {
                                        self.handleVendorSelection(annotation, proxy: proxy)
                                    }
                                    .offset(x: 0, y: isSelected ? -20 : 0)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .scrollClipDisabled()
                    .frame(height: getRelativeHeight(272.0))
                    .frame(maxWidth: .infinity)
                }
                .animation(.bouncy, value: self.viewModel.selectedAnnotation)
                .background(.white)
                .transition(.move(edge: .bottom))
            }
        }
    }
    
    // Handles vendor selection from the list
    func handleVendorSelection(_ model: SaloonMapModel.VendorModel, proxy: ScrollViewProxy) {
        if model.id == self.viewModel.selectedAnnotation?.id {
            let routesType = self.routerManager.mapRouterWithTab(appState: self.appState)
            self.routerManager.push(to: .shopDetails(type: routesType, vendorID: model.vendorID), where: routesType)
        } else {
            self.selectAnnotation(model, proxy: proxy)
        }
    }
    
    // Navigation buttons (Back and GPS)
    func navigationButtons() -> some View {
        HStack {
            Button(action: { self.dismiss() }) {
                Image("img_arrowleft")
                    .rotateBasedOnLanguage()
                    .frame(width: getRelativeWidth(49.0), height: getRelativeWidth(49.0))
                    .background(Circle().fill(ColorConstants.WhiteA700))
                    .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
            }
            Spacer()
            Button(action: self.updateCameraPosition) {
                Image("img_gps")
                    .frame(width: getRelativeWidth(49.0), height: getRelativeWidth(49.0))
                    .background(Circle().fill(ColorConstants.WhiteA700))
                    .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
            }
        }
    }
}

// Custom Map Marker View for Vendor Annotations
struct MapMarkerView: View {
    var isSelected: Bool
    
    var body: some View {
        ZStack(alignment: .center) {
            Image("img_vector_cyan_800")
                .resizable()
                .frame(width: getRelativeWidth(42.0),
                       height: getRelativeHeight(54.0), alignment: .center)
                       
                .background {
                    Image("img_vector_cyan_800")
                        .renderingMode(.template)
                        .resizable()
                        .frame(width: getRelativeWidth(42.0),
                               height: getRelativeHeight(54.0), alignment: .center)
                        .if(self.isSelected, transform: { $0.foregroundStyle(.yellow.gradient) })
                        .scaleEffect(self.isSelected ? 1.15 : 1.0)
                }
            Image("img_group29")
                .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(22.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 11.44, topRight: 11.44,
                                           bottomLeft: 11.44, bottomRight: 11.44)
                        .fill(ColorConstants.Cyan800))
                .padding(.bottom, getRelativeHeight(19.11))
                .padding(.horizontal, getRelativeWidth(10.24))
        }
            
        .frame(width: getRelativeWidth(42.0), height: getRelativeHeight(54.0),
               alignment: .trailing)
    }
}
