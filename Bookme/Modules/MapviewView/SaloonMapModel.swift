//
//  SaloonMapModel.swift
//  Bookme
//
//  Created by Apple on 06/05/2024.
//

import Foundation
import MapKit

// MARK: - SaloonMapModel

struct SaloonMapModel: Codable, Equatable {
    let vendors:[VendorModel]
    
    
    struct VendorModel:  Codable, Identifiable, Equatable {
        let id: UUID = .init()
        let vendorName: String
        let vendorID: Int
        let vendorEmail, vendorPhone, vendorLogo, lat: String
        let lng, openingTime, closingTime: String
        let type: Int
        let address: String
        let distance: String
        let rating: String?
        
        var vendorLogoUrl:String { AppConstants.Server.baseURL + vendorLogo }

        var span: MKCoordinateSpan {
            MapDetails.defaultSpan
        }

        var coordinate: CLLocationCoordinate2D {
            .init(latitude: Double(lat) ?? 0.0, longitude: Double(lng) ?? 0.0)
        }

        enum CodingKeys: String, CodingKey {
            case vendorName = "vendor_name"
            case vendorID = "vendor_id"
            case vendorEmail = "vendor_email"
            case vendorPhone = "vendor_phone"
            case vendorLogo = "vendor_logo"
            case lat, lng
            case openingTime = "opening_time"
            case closingTime = "closing_time"
            case type, address, distance, rating
        }
    }
}
