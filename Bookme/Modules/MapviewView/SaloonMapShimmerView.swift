//
//  SaloonMapShimmerView.swift
//  Bookme
//
//  Created by Apple on 29/08/2024.
//
import MapKit
import SwiftUI

struct SaloonMapShimmerView: View {
   
    @Namespace var mapScope
    var body: some View {
        VStack {
            ScrollViewReader { proxy in
                ZStack(alignment: .bottom) {
                    VStack(spacing: 0) {
                        MapReader { reader in
                            Map(position: .constant( MapCameraPosition.region(MKCoordinateRegion(center: MapDetails.startingLocation, span: MapDetails.defaultSpan))), interactionModes: .all, scope: mapScope) {
                              
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .edgesIgnoringSafeArea(.all)
                        }
                  
                    }.ignoresSafeArea(.all, edges: .top)
                    
                    HStack {
                        HStack {
                            Button(action: {
                              
                            }, label: {
                                Image("img_arrowleft")
                                    .frame(width: getRelativeWidth(49.0), height: getRelativeWidth(49.0),
                                           alignment: .center)
                                    .background(Circle()
                                            .fill(ColorConstants.WhiteA700))
                                    .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 1)
                            })
                            
                            Spacer()
                        }
                    }
                    
                    .frame(maxHeight: .infinity, alignment: .top)
                }
            }
            .safeAreaPadding()
        }
        .hideNavigationBar()
        .background(ColorConstants.WhiteA700)
        .shimmerize()
    }
}

#Preview {
    SaloonMapShimmerView()
}
