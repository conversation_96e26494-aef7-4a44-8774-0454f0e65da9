import Foundation
import MapKit
import SwiftUI

enum MapDetails {
    static let startingLocation = CLLocationCoordinate2D(latitude: 29.334929841887412, longitude: 47.986971037631484)
    static let defaultSpan = MKCoordinateSpan(latitudeDelta: 0.09, longitudeDelta: 0.09)
}

class SaloonMapViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var selectedAnnotation: SaloonMapModel.VendorModel?
    @Published var saloonMapList: [SaloonMapModel.VendorModel] = []
    @Published var cameraPosition: MapCameraPosition = .userLocation(followsHeading: true, fallback: .automatic)
    override init() {
        super.init()
        getLocations()
    }
    
    func updateAnnotation(_ value: SaloonMapModel.VendorModel) {
        selectedAnnotation = value
    }
    
    func getLocations() {
        guard let coordinate = AppState.userLocation else { return }
        let parameters: [String: String] = [
            "lat": "\(coordinate.latitude)",
            "long": "\(coordinate.longitude)",
            "distance": "1000"
        ]
        onApiCall(api.mapSalons, parameters: parameters) {
            self.saloonMapList = $0.data?.vendors ?? []
//            if let firstData = $0.data?.vendors.first{
//                self.position = MapCameraPosition.region(MKCoordinateRegion(center: firstData.coordinate, span: MapDetails.defaultSpan))
//            }
        }
    }
}

struct AnnotationModel: Identifiable, Equatable {
    static func == (lhs: AnnotationModel, rhs: AnnotationModel) -> Bool {
        lhs.id == rhs.id && lhs.name == rhs.name && lhs.coordinate.latitude == rhs.coordinate.latitude && lhs.coordinate.longitude == rhs.coordinate.longitude && lhs.span.latitudeDelta == rhs.span.latitudeDelta && lhs.span.longitudeDelta == rhs.span.longitudeDelta
    }
    
    let id: UUID = .init()
    let name: String
    let coordinate: CLLocationCoordinate2D
    let span: MKCoordinateSpan
}
