import SwiftUI

struct MapPinCell: View {
    var body: some View {
        ZStack(alignment: .center) {
            Image("img_vector_cyan_800")
                .resizable()
                .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(54.0),
                       alignment: .leading)
                .scaledToFit()
            But<PERSON>(action: {}, label: {
                Image("img_group29")
            })
            .frame(width: getRelativeWidth(20.0), height: getRelativeWidth(22.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 11.44, topRight: 11.44, bottomLeft: 11.44,
                                       bottomRight: 11.44)
                    .fill(ColorConstants.Cyan800))
            .padding(.bottom, getRelativeHeight(21.11))
            .padding(.horizontal, getRelativeWidth(10.24))
        }
        .hideNavigationBar()
        .frame(width: getRelativeWidth(40.0), alignment: .leading)
    }
}

/* struct MapPinCell_Previews: PreviewProvider {

 static var previews: some View {
 			MapPinCell()
 }
 } */
