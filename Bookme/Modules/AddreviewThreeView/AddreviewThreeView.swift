import SwiftUI

struct AddreviewThreeView: View {
    @StateObject var addreviewThreeViewModel = AddreviewThreeViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            VStack(alignment: .leading, spacing: 0) {
                VStack(alignment: .leading, spacing: 0) {
                    Text(StringConstants.kMsgNov222023)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(123.0), height: getRelativeHeight(17.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(15.0))
                        .padding(.horizontal, getRelativeWidth(17.0))
                    Divider()
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(1.0),
                               alignment: .center)
                        .background(ColorConstants.Cyan8004c)
                        .padding(.top, getRelativeHeight(10.0))
                        .padding(.horizontal, getRelativeWidth(14.0))
                    ZStack(alignment: .topTrailing) {
                        Image("img_rectangle10")
                            .resizable()
                            .frame(width: getRelativeWidth(74.0), height: getRelativeWidth(74.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipShape(Circle())
                            .clipShape(Circle())
                            .padding(.bottom, getRelativeHeight(67.35))
                            .padding(.trailing, getRelativeWidth(256.0))
                        VStack(alignment: .leading, spacing: 0) {
                            Text(StringConstants.kMsgBroadwayBarber)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(176.0),
                                       height: getRelativeHeight(20.0), alignment: .topLeading)
                                .padding(.trailing)
                            Text(StringConstants.kMsg4thFloorAlZ)
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Black900B2)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(227.0),
                                       height: getRelativeHeight(33.0), alignment: .topLeading)
                        }
                        .frame(width: getRelativeWidth(227.0), height: getRelativeHeight(56.0),
                               alignment: .topTrailing)
                        .padding(.bottom, getRelativeHeight(90.0))
                        .padding(.leading, getRelativeWidth(89.87))
                        VStack {
                            Text(StringConstants.kLblAddReview)
                                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                .fontWeight(.semibold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(67.0),
                                       height: getRelativeHeight(17.0), alignment: .topLeading)
                                .padding(.bottom, getRelativeHeight(4.0))
                                .padding(.horizontal, getRelativeWidth(10.0))
                        }
                        .frame(width: getRelativeWidth(87.0), height: getRelativeHeight(25.0),
                               alignment: .trailing)
                        .overlay(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                bottomRight: 12.5)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                   bottomRight: 12.5)
                                .fill(ColorConstants.Cyan8003f))
                        .padding(.leading, getRelativeWidth(243.0))
                        Text(StringConstants.kLbl6Km)
                            .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.semibold)
                            .foregroundColor(ColorConstants.Black900B2)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(27.0), height: getRelativeHeight(17.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(107.5))
                            .padding(.trailing, getRelativeWidth(195.5))
                        VStack {
                            ZStack(alignment: .center) {
                                Image("img_vector_cyan_800_11x9")
                                    .resizable()
                                    .frame(width: getRelativeWidth(9.0),
                                           height: getRelativeHeight(11.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                Image("img_vector_cyan_800_3x3")
                                    .resizable()
                                    .frame(width: getRelativeWidth(3.0),
                                           height: getRelativeWidth(3.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                    .padding(.bottom, getRelativeHeight(4.83))
                                    .padding(.horizontal, getRelativeWidth(3.17))
                            }
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(9.0), height: getRelativeHeight(11.0),
                                   alignment: .leading)
                            .padding(.horizontal, getRelativeWidth(90.0))
                            Divider()
                                .frame(width: getRelativeWidth(330.0),
                                       height: getRelativeHeight(1.0), alignment: .leading)
                                .background(ColorConstants.Cyan8004c)
                                .padding(.top, getRelativeHeight(17.0))
                            HStack {
                                VStack {
                                    Text(StringConstants.kLblReBook)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(57.0),
                                               height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.vertical, getRelativeHeight(7.0))
                                        .padding(.horizontal, getRelativeWidth(35.0))
                                }
                                .frame(width: getRelativeWidth(129.0),
                                       height: getRelativeHeight(35.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                        bottomLeft: 17.5, bottomRight: 17.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                           bottomLeft: 17.5, bottomRight: 17.5)
                                        .fill(Color.clear.opacity(0.7)))
                                Spacer()
                                VStack {
                                    Text(StringConstants.kLblReceipt)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(48.0),
                                               height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.top, getRelativeHeight(8.0))
                                        .padding(.bottom, getRelativeHeight(6.0))
                                        .padding(.leading, getRelativeWidth(42.0))
                                        .padding(.trailing, getRelativeWidth(38.0))
                                }
                                .frame(width: getRelativeWidth(129.0),
                                       height: getRelativeHeight(35.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                           bottomLeft: 17.5, bottomRight: 17.5)
                                        .fill(ColorConstants.Cyan800))
                            }
                            .frame(width: getRelativeWidth(329.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .padding(.top, getRelativeHeight(17.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(82.0),
                               alignment: .bottomLeading)
                        .padding(.top, getRelativeHeight(64.04))
                    }
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(146.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(13.0))
                    .padding(.bottom, getRelativeHeight(16.0))
                    .padding(.horizontal, getRelativeWidth(14.0))
                }
                .frame(width: getRelativeWidth(360.0), height: getRelativeHeight(220.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                        bottomRight: 20.0)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                           bottomRight: 20.0)
                        .fill(ColorConstants.WhiteA700))
                .padding(.horizontal, getRelativeWidth(15.0))
               
                VStack(alignment: .leading, spacing: 0) {
                    Text(StringConstants.kMsgNov222023)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(123.0), height: getRelativeHeight(17.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(15.0))
                        .padding(.horizontal, getRelativeWidth(17.0))
                    Divider()
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(1.0),
                               alignment: .center)
                        .background(ColorConstants.Cyan8004c)
                        .padding(.top, getRelativeHeight(10.0))
                        .padding(.horizontal, getRelativeWidth(14.0))
                    ZStack(alignment: .topTrailing) {
                        Image("img_rectangle10")
                            .resizable()
                            .frame(width: getRelativeWidth(74.0), height: getRelativeWidth(74.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipShape(Circle())
                            .clipShape(Circle())
                            .padding(.bottom, getRelativeHeight(60.34))
                            .padding(.trailing, getRelativeWidth(256.0))
                        VStack(alignment: .leading, spacing: 0) {
                            Text(StringConstants.kMsgBroadwayBarber)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(176.0),
                                       height: getRelativeHeight(20.0), alignment: .topLeading)
                                .padding(.trailing)
                            Text(StringConstants.kMsg4thFloorAlZ)
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Black900B2)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(227.0),
                                       height: getRelativeHeight(33.0), alignment: .topLeading)
                        }
                        .frame(width: getRelativeWidth(227.0), height: getRelativeHeight(56.0),
                               alignment: .topTrailing)
                        .padding(.bottom, getRelativeHeight(83.0))
                        .padding(.leading, getRelativeWidth(89.87))
                        VStack {
                            Text(StringConstants.kLblAddReview)
                                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                .fontWeight(.semibold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(67.0),
                                       height: getRelativeHeight(17.0), alignment: .topLeading)
                                .padding(.bottom, getRelativeHeight(4.0))
                                .padding(.horizontal, getRelativeWidth(10.0))
                        }
                        .frame(width: getRelativeWidth(87.0), height: getRelativeHeight(25.0),
                               alignment: .bottomTrailing)
                        .overlay(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                bottomRight: 12.5)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                   bottomRight: 12.5)
                                .fill(ColorConstants.Cyan8003f))
                        .padding(.top, getRelativeHeight(61.66))
                        .padding(.leading, getRelativeWidth(243.0))
                        Text(StringConstants.kLbl6Km)
                            .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.semibold)
                            .foregroundColor(ColorConstants.Black900B2)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(27.0), height: getRelativeHeight(17.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(107.5))
                            .padding(.trailing, getRelativeWidth(195.5))
                        VStack {
                            ZStack(alignment: .center) {
                                Image("img_vector_cyan_800_11x9")
                                    .resizable()
                                    .frame(width: getRelativeWidth(9.0),
                                           height: getRelativeHeight(11.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                Image("img_vector_cyan_800_3x3")
                                    .resizable()
                                    .frame(width: getRelativeWidth(3.0),
                                           height: getRelativeWidth(3.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                    .padding(.bottom, getRelativeHeight(4.83))
                                    .padding(.horizontal, getRelativeWidth(3.17))
                            }
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(9.0), height: getRelativeHeight(11.0),
                                   alignment: .leading)
                            .padding(.horizontal, getRelativeWidth(90.0))
                            Divider()
                                .frame(width: getRelativeWidth(330.0),
                                       height: getRelativeHeight(1.0), alignment: .leading)
                                .background(ColorConstants.Cyan8004c)
                                .padding(.top, getRelativeHeight(17.0))
                            HStack {
                                VStack {
                                    Text(StringConstants.kLblReBook)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(57.0),
                                               height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.vertical, getRelativeHeight(7.0))
                                        .padding(.horizontal, getRelativeWidth(36.0))
                                }
                                .frame(width: getRelativeWidth(129.0),
                                       height: getRelativeHeight(35.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                        bottomLeft: 17.5, bottomRight: 17.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                           bottomLeft: 17.5, bottomRight: 17.5)
                                        .fill(Color.clear.opacity(0.7)))
                                Spacer()
                                VStack {
                                    Text(StringConstants.kLblReceipt)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(49.0),
                                               height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.top, getRelativeHeight(8.0))
                                        .padding(.bottom, getRelativeHeight(6.0))
                                        .padding(.leading, getRelativeWidth(41.0))
                                        .padding(.trailing, getRelativeWidth(38.0))
                                }
                                .frame(width: getRelativeWidth(129.0),
                                       height: getRelativeHeight(35.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                           bottomLeft: 17.5, bottomRight: 17.5)
                                        .fill(ColorConstants.Cyan800))
                            }
                            .frame(width: getRelativeWidth(329.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .padding(.top, getRelativeHeight(10.0))
                        }
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(75.0),
                               alignment: .bottomLeading)
                        .padding(.top, getRelativeHeight(64.05))
                    }
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(139.0),
                           alignment: .center)
                    .padding(.vertical, getRelativeHeight(13.0))
                    .padding(.horizontal, getRelativeWidth(14.0))
                }
                .frame(width: getRelativeWidth(360.0), height: getRelativeHeight(220.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                        bottomRight: 20.0)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                           bottomRight: 20.0)
                        .fill(ColorConstants.WhiteA700))
                .padding(.bottom, getRelativeHeight(110.0))
                .padding(.horizontal, getRelativeWidth(15.0))
            }
            .frame(width: UIScreen.main.bounds.width)
        }
        .hideNavigationBar()
    }
}

struct AddreviewThreeView_Previews: PreviewProvider {
    static var previews: some View {
        AddreviewThreeView()
    }
}
