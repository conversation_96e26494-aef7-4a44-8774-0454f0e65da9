//
//  CategoryView.swift
//  Bookme
//
//  Created by Apple on 25/10/2024.
//

import SwiftUI
import WrappingHStack

struct MainCategoryView: View {
    let servicesList: [ServicesCategoryModel]
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager
    var body: some View {
        MainScrollBody(invertColor: false,backButtonWithTitle: "Categories") {
            VStack {
                VStack(alignment: .leading, spacing: 0) {
                    
                        
                    WrappingHStack(servicesList, id: \.self, alignment: .leading, spacing: .constant(32.relativeWidth), lineSpacing: 24.relativeHeight) { item in
                        Button(action: {
                            appState.selectedHeader = item
                            appState.updateSelectedTab(.explore)
                        }, label: {
                            VStack {
                                NetworkImageView(path: item.imageUrl, contentMode: .fit)
                                    .scaleEffect(0.7)
                                    .frame(width: getRelativeWidth(69.0),
                                           height: getRelativeWidth(69.0), alignment: .center)
                           
                                    .overlay(Circle()
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                    .background(Circle()
                                        .fill(ColorConstants.Cyan80038))
                                Text(item.catName)
                                    .font(FontScheme
                                        .kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .multilineTextAlignment(.center)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .lineLimit(1, reservesSpace: true)
                                    .fixedSize()
                                    .multilineTextAlignment(.leading)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(4.0))
                            }
                            .frame(width: getRelativeWidth(69.0),
                                   alignment: .center)

                        })
                    }
                    .padding()
                    
                   
                      
                    
                }
                .padding(.horizontal)
                .padding(.top)
            }
        }
        .background(ColorConstants.bgGradient)
        .onDisappear {
            appState.updateTabBarHidden(false)
        }
    }
    
}
