import SwiftUI

struct MyAccountView: View {
    var isFromMenu: Bool = false
    @StateObject var viewModel = MyAccountViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(RouterManager.self) private var routerManager
    @EnvironmentObject private var appState: AppState
    @State private var isScrolled: Bool = false
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            ScrollViewWithOffset(.vertical, showsIndicators: false) { self.isScrolled = $0.y < -10 }
                content: {
                    VStack(alignment: .leading, spacing: 0) {
                        if let user = appState.currentUserModel?.user, AppState.isLoggedIn {
                            HStack {
                                NetworkImageView(path: user.imageUrl, contentMode: .fill)
                                    .frame(width: getRelativeWidth(68.0), height: getRelativeWidth(68.0),
                                           alignment: .center)
                                    .scaledToFit()
                                    .clipShape(Circle())
                                    .clipShape(Circle())
                                VStack(alignment: .leading, spacing: 0) {
                                    Text(user.name)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(height: getRelativeHeight(22.0),
                                               alignment: .topLeading)

                                    if let userPhone = user.userPhone {
                                        Text("+965 " + userPhone)
                                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                            .fontWeight(.regular)
                                            .foregroundColor(ColorConstants.Gray401)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.top, getRelativeHeight(4.0))
                                    }
                                }
                                .frame(height: getRelativeHeight(44.0),
                                       alignment: .bottom)

                                .padding(.bottom, getRelativeHeight(10.0))
                                .padding(.leading, getRelativeWidth(20.0))
                            }

                            .frame(height: getRelativeHeight(126.0),
                                   alignment: .leading)
                            .padding(.leading, getRelativeWidth(19.0))
                            .padding(.trailing, getRelativeWidth(19.0))
                            .padding(.bottom, getRelativeWidth(16.0))
                        }
                        VStack {
                            ForEach(viewModel.filteredList) { item in

                                Button(action: {
                                    if isFromMenu {
                                        appState.showSideMenu.toggle()
                                    }

                                    let route = routerManager.mapRouterWithTab(appState: appState)

                                    if item.isLogin {
                                        if AppState.isLoggedIn {
                                            self.appState.updatePopUpView(true)

                                        } else {
                                            routerManager.push(to: item.destination, where: route)
                                        }
                                    } else {
                                        routerManager.push(to: item.destination, where: route)
                                    }

                                }, label: {
                                    Group {
                                        VStack(spacing: 0) {
                                            HStack {
                                                HStack {
                                                    VStack {
                                                        if item.isSystemImage {
                                                            Image(systemName: item.image)
                                                                .renderingMode(.template)
                                                                .resizable()
                                                                .scaledToFit()

                                                        } else {
                                                            Image(item.image)
                                                                .renderingMode(.template)
                                                                .resizable()
                                                                .scaledToFit()
                                                        }
                                                    }
                                                    .foregroundStyle(ColorConstants.Cyan800)
                                                    .frame(width: getRelativeWidth(24.0), height: getRelativeHeight(24.0), alignment: .center)
//                                                        .background(.orange)
                                                    .clipped()

                                                    Text(item.isLogin && AppState.isLoggedIn ? "Log Out" : item.title.localize)
                                                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                                        .fontWeight(.regular)
                                                        .foregroundColor(ColorConstants.WhiteA700)
                                                        .fixedSize()
                                                        .multilineTextAlignment(.leading)
                                                        .frame(width: getRelativeWidth(68.0), height: getRelativeHeight(18.0),
                                                               alignment: .topLeading)
                                                        .padding(.leading, getRelativeWidth(14.0))
                                                }
                                                .frame(height: getRelativeHeight(28.0),
                                                       alignment: .center)
                                                .padding(.vertical, getRelativeHeight(9.0))
                                                .padding(.leading, getRelativeWidth(4.0))
                                                Spacer()
                                                ArrowRightView()
                                            }

                                            .background(RoundedCorners().fill(Color.clear.opacity(0.7)))

                                            Divider()
                                                .frame(
                                                    height: 1, alignment: .leading
                                                )
                                                .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.25))

                                        }.padding(.horizontal, 8)
                                    }
                                })
                            }
                        }

                        .padding(.horizontal, getRelativeWidth(19.0))
                    }
                }
                .scrollIndicators(.hidden)
                .safeAreaInset(edge: .top) {
                    CustomNavigationBar(hideMenu: true)
                        .if(isScrolled, transform: {
                            $0.background(.thinMaterial)
                        })
                        .overlay(alignment: .bottom, content: {
                            Divider()
                                .visibility(isScrolled ? .visible : .gone)
                        })
                        .visibility(isFromMenu ? .gone : .visible)
                }
//                .safeAreaPadding(.vertical)
        }
        .background(ColorConstants.bgGradient)
        .onAppear {
            // Sync AppState currentUserModel with UserDefaults on first load
            if appState.currentUserModel == nil {
                appState.currentUserModel = AppState.userModel
            }
        }

        .onChange(of: routerManager.myAccountRouteList) { _, newValue in
            appState.updateTabBarHidden(!newValue.isEmpty)
        }
        .handleDeepLinkNavigation(routesType: .myAccountRoute)
    }
}

struct MyaccountView_Previews: PreviewProvider {
    static var previews: some View {
        MyAccountView().attachAllEnvironmentObjects()
            .background(ColorConstants.bgGradient)
    }
}

struct ArrowRightView: View {
    var body: some View {
        Image("img_arrowright_cyan_800")
            .resizable()
            .scaledToFit()
            .rotateBasedOnLanguage()
            .frame(
                width: getRelativeWidth(6.0),
                height: getRelativeHeight(12.0),
                alignment: .center
            )
            .clipped()
            .padding(.vertical, getRelativeHeight(19.0))
    }
}
