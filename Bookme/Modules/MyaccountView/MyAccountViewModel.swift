import Foundation
import SwiftUI

class MyAccountViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil

//    @Published var showLogoutPopupView: Bool = false
    // Removed local userModel - now using AppState.currentUserModel for reactive updates

    var filteredList: [MyAccountModel] {
        if AppState.isLoggedIn {
            return accountItemList
        } else {
            return accountItemList.filter { !$0.isLoginNeeded }
        }
    }

    let accountItemList: [MyAccountModel] = [
        .init(title: "Profile", image: "img_usericon", isLoginNeeded: true, destination: .profile),

        .init(title: "Address", image: "account.address", isLoginNeeded: true, destination: .addressListing),
        .init(title: "Payment Method", image: "img_creditcardlig", isLoginNeeded: true, destination: .paymentMethod),

        .init(title: "Saved", image: "img_vector15_cyan_800", isLoginNeeded: true, destination: .saved),

        .init(title: "Settings", image: "img_subtract", isLoginNeeded: false, destination: .settings),

        .init(title: "Transactions", image: "img_arrowleft_cyan_800", isLoginNeeded: true, destination: .transaction),

        .init(title: "Help Center", image: "help_center", isLoginNeeded: false, destination: .helpCenter),

        .init(title: "Privacy Policy", image: "img_group8", isLoginNeeded: false, destination: .customRichTextPage(type: .privacy)),

        .init(title: "Terms & Conditions", image: "doc.text", isLoginNeeded: false, destination: .customRichTextPage(type: .terms), isSystemImage: true),
        .init(title: "Log In", image: "img_arrowright_cyan_800_19x19", isLoginNeeded: false, destination: .signIn(type: .myAccountRoute), isLogin: true),
    ]

//    func updatePopUpView(_ value: Bool) { withAnimation {
//        self.showLogoutPopupView = value
//    } }

    func onLogout(completion: @escaping () -> Void) {
        guard let token = UserDefaultsSecure.sharedInstance.getGeneratedToken() else { return }

        let parameters = ["token": token]
        onApiCall(api.logout, parameters: parameters) { response in
            if response.success { completion() }
        }
    }
}
