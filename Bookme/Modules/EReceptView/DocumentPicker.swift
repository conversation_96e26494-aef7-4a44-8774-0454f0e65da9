//
//  DocumentPicker.swift
//  Bookme
//
//  Created by Apple on 25/09/2024.
//

import SwiftUI
import UIKit

struct DocumentPicker: UIViewControllerRepresentable {
    var url: URL
    
    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forExporting: [url])
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, UIDocumentPickerDelegate {
        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            // Handle what to do after the user selects the location and saves the file
            print("File saved at: \(urls.first?.absoluteString ?? "No URL")")
        }
        
        func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
            print("User canceled saving the file.")
        }
    }
}
