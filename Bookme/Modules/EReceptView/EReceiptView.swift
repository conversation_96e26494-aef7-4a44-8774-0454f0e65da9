import SwiftUI

struct EReceiptView: View {
    let model: BookingAppointmentModel
  
    @StateObject var eReceptViewModel = EReceptViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var appState: AppState
    @State private var isEventTriggered: Bool = false
    @State private var isEventAdded: Bool = false
    
    var body: some View {
        
        
        SuperView(pageState: $eReceptViewModel.pageState) {
            MainScrollBody(invertColor: true, backButtonWithTitle: "Receipt") {
               
                    VStack {
                        VStack {
                            VStack {
                                Group {
                                    HStack {
                                        Text("Barber / Vendor")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(88.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                            .padding(.bottom, getRelativeHeight(4.0))
                                        Spacer()
                                        Text(model.vendorName)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(18.0), alignment: .topLeading)
                                            .padding(.leading, getRelativeWidth(70.0))
                                    }
                                    .frame(width: getRelativeWidth(323.0), height: getRelativeHeight(20.0),
                                           alignment: .center)
                                    .padding(.top, getRelativeHeight(20.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(9.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text("Address")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(52.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                            .padding(.vertical, getRelativeHeight(2.0))
                                        Spacer()
                                        Text(model.address)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.trailing)
                                            .padding(.leading, getRelativeWidth(64.0))
                                    }
                                    .frame(width: getRelativeWidth(325.0),
                                           alignment: .center)
                                    .padding(.top, getRelativeHeight(10.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(7.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text("Name")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(39.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                        Spacer()
                                        Text(model.userName)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(18.0), alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
                                           alignment: .center)
                                    .padding(.top, getRelativeHeight(12.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(8.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text("Phone")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(42.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                        Spacer()
                                        Text(model.vendorPhone)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(18.0), alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(19.0),
                                           alignment: .center)
                                    .padding(.top, getRelativeHeight(14.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(8.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                }
                                Group {
                                    HStack {
                                        Text("Booking Date")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(88.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                        Spacer()
                                        Text(model.serviceDate)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(18.0), alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
                                           alignment: .center)
                                    .padding(.top, getRelativeHeight(15.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(7.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text("Booking Hours")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(87.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                        Spacer()
                                        Text(model.serviceTime)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(18.0), alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
                                           alignment: .center)
                                    .padding(.top, getRelativeHeight(15.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(7.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text("Specialist")
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(60.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                        Spacer()
                                        
                                        let staffNames = model.orderDetails.map { $0.staffName }.uniqued().formatted(.list(type: .and, width: .short))
                                        
                                        Text(staffNames)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(18.0), alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
                                           alignment: .center)
                                    .padding(.vertical, getRelativeHeight(16.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    
                                    
                                    Button(action: {
                                        isEventTriggered = true
                                    }, label: {
                                        Text(isEventAdded ? "Added" : "Add to Calendar")
                                            .font(FontScheme
                                                .kNunitoSemiBold(size: 12.0.relativeFontSize))
                                            .fontWeight(.regular)
                                            .padding(.horizontal, getRelativeWidth(32.0))
                                            .padding(.vertical, getRelativeHeight(13.0))
                                            .foregroundColor(ColorConstants.Cyan800)
                                            .multilineTextAlignment(.center)
                                            .frame(
                                                height: getRelativeHeight(40.0), alignment: .center)
                                            .frame(maxWidth: 240.relativeWidth)
                                            .background(ColorConstants.WhiteA700.clipShape(.capsule))
                                            .clipped()
                                            .overlay(Capsule()
                                                .stroke(ColorConstants.Cyan800,
                                                        lineWidth: 1))
                                            
                                                
                                    })
                                    .padding(.vertical, getRelativeHeight(12.0))
                                    .disableWithOpacity(isEventAdded)
                                    .addToCalendar(isTriggered: $isEventTriggered, event: appState.event, onCompletion: { success, _ in
                                        if success {
                                            eReceptViewModel.updatePageState(.message(config: .init(title: "Success", text: "Calendar event added successfully")))
                                            isEventAdded = true
                                        }
                                    })
                                }
                            }
                            .onAppear {
                               
//                                let eventTitle = "You have booked an Appointment at \(model.vendorName)"
//                                let eventNote = "For services: \(model.orderDetails.map(\.serviceName).uniqued().formatted(.list(type: .and, width: .short)))"
//                                let eventStartDate = eReceptViewModel.selectedDate.settingTime(from: viewModel.cartModelList.first?.startTime ?? "")
//                                let eventEndDate = eReceptViewModel.selectedDate.settingTime(from: viewModel.cartModelList.last?.endTime ?? "")
//                                let alertOffset: TimeInterval = -2 * 60 * 60
//                                if
//                                    let eventStartDate = eventStartDate,
//                                    let eventEndDate = eventEndDate
//                                {
//                                    appState.event = .init(title: eventTitle, startDate: eventStartDate, endDate: eventEndDate, note: eventNote, location: model.address, alertOffset: alertOffset)
//                                }
                            }
                           
                            .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                    bottomRight: 20.0)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                       bottomRight: 20.0)
                                    .fill(ColorConstants.WhiteA700))
                            VStack {
                                ForEach(model.orderDetails) { detail in
                                    
                                    HStack {
                                        Text(detail.serviceName)
                                            .font(FontScheme.kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(92.0),
                                                   height: getRelativeHeight(16.0), alignment: .topLeading)
                                        Spacer()
                                        CurrencyText(detail.price)
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .multilineTextAlignment(.leading)
                                            .frame(
                                                height: getRelativeHeight(18.0), alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(18.0),
                                           alignment: .center)
                                    .padding(.top, getRelativeHeight(21.0))
                                    .padding(.horizontal, getRelativeWidth(16.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                                               alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(10.0))
                                        .padding(.horizontal, getRelativeWidth(16.0))
                                }
                                HStack {
                                    Text("Total")
                                        .font(FontScheme.kMontserratMedium(size: getRelativeHeight(13.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.Black900B2)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(30.0),
                                               height: getRelativeHeight(16.0), alignment: .leading)
                                    Spacer()
                                    CurrencyText(model.price)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(18.0), alignment: .leading)
                                }
                                .frame(width: getRelativeWidth(324.0), height: getRelativeHeight(19.0),
                                       alignment: .center)
                                .padding(.vertical, getRelativeHeight(19.0))
                                .padding(.horizontal, getRelativeWidth(16.0))
                            }
                            
                            .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                    bottomRight: 20.0)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                       bottomRight: 20.0)
                                    .fill(ColorConstants.WhiteA700))
                            .padding(.top, getRelativeHeight(30.0))
                        }
                       
                        .padding(.top, getRelativeHeight(32.0))
                        .padding(.horizontal, getRelativeWidth(15.0))
                        .background(ColorConstants.WhiteA700)
                    }
                    .background(ColorConstants.WhiteA700)
                
               
            }
            
            .safeAreaInset(edge: .bottom) {
                
                if let pdfURL = eReceptViewModel.pdfURL {
                    ShareLink(item: pdfURL) {
                        HStack(spacing: 0) {
                            Text("Download Receipt")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.heavy)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(13.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                   
                                .multilineTextAlignment(.center)
                                .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                .background(RoundedCorners(topLeft: 23.81, topRight: 23.81,
                                                           bottomLeft: 23.81, bottomRight: 23.81)
                                        .fill(ColorConstants.Cyan800))
                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
        //                        .padding(.top, getRelativeHeight(107.0))
                                .padding(.horizontal, getRelativeWidth(24.0))
                        }
                    }
    //                .padding(.bottom, (AppConstants.tabBarHeight + 8).relativeHeight)
                }
                
            }
            .onAppear {
                eReceptViewModel.downloadPDF(bookingID: model.orderDetails.first?.bookID)
            }
        }
       
    }
}

extension Sequence where Element: Hashable {
    func uniqued() -> [Element] {
        var set = Set<Element>()
        return filter { set.insert($0).inserted }
    }
}
