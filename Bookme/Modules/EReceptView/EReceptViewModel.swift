import Foundation
import SwiftUI

class EReceptViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var pdfURL: URL? = nil
    
    
    @Published var selectedDate: Date = BookAppointmentViewModel.now
    
    // Function to make the POST request and download the PDF
    func downloadPDF(bookingID: Int?) {
        guard
            let url = URL(string: "https://projects.crisance.com/bookme/adminpanel/public/api/downloadPDF"),
            let userID: Int = AppState.userModel?.user.id,
            let bookingID: Int = bookingID
        
        else {
            print("Invalid URL.")
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
        // Add any necessary parameters for the POST request
        let parameters: [String: Any] = ["user_id": userID, "bookid": bookingID]
        request.httpBody = try? JSONSerialization.data(withJSONObject: parameters, options: [])
            
        self.updatePageState(.loading(true))
            
        URLSession.shared.downloadTask(with: request) { tempURL, _, error in
            DispatchQueue.main.async {
                self.updatePageState(.stable)
            }
                
            if let error = error {
                print("Download error: \(error)")
                return
            }
                
            guard let tempURL = tempURL else {
                print("No data downloaded.")
                return
            }
                
            // Save the file to a temporary directory
                   let fileManager = FileManager.default
                   let destinationURL = fileManager.temporaryDirectory.appendingPathComponent("bookme_receipt.pdf")
                   
                   do {
                       // Remove existing file if it exists
                       if fileManager.fileExists(atPath: destinationURL.path) {
                           try fileManager.removeItem(at: destinationURL)
                       }
                       
                       // Move the downloaded PDF from the temp location to the destination URL
                       try fileManager.moveItem(at: tempURL, to: destinationURL)
                       
                       // Save the URL to be used by ShareLink
                       DispatchQueue.main.async {
                           self.pdfURL = destinationURL
                           print("File saved to: \(destinationURL)")
                       }
                   } catch {
                       print("File error: \(error)")
                   }
               }.resume()
           }
}
