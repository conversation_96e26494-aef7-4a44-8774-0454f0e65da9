import Combine
import Foundation
import SwiftUI

class ShopDetailsServiceViewModel: SuperViewModel {
    @Published var searchText: String = .init()
    @Published var selectedCategory: ServicesCategoryModel?
    @Published var servicesCategoryList: [ServicesCategoryModel] = []
    @Published var isScrolled: Bool = false
    @Published var services: [VendorDetailsModel.Service] = []
    let vendorID: Int?
    let isFromBooking: Bool
    init(vendorID: Int?, isFromBooking: Bool) {
        self.vendorID = vendorID
        self.isFromBooking = isFromBooking
        super.init()
        getServices()
        getServiceCategories()
        
    }
    
    func getServiceCategories() {
        guard let vendorID = vendorID else { return }
        
        let parameters: [String: Any] = ["vendor_id": vendorID]
        onApiCall(api.vendorServicesCategory, parameters: parameters) {
            self.servicesCategoryList = $0.data ?? []
            self.selectedCategory = self.servicesCategoryList.first
        }
    }
    
    func getServices() {
        guard let vendorID = vendorID else { return }
        
        let parameters: [String: Any] = ["vendor_id": vendorID]
        onApiCall(api.vendorServices, parameters: parameters) { self.services = $0.data ?? [] }
    }
    
    var filteredServices: [VendorDetailsModel.Service] {
        if searchText.isEmpty {
            return services
        } else {
            return services.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }

    func checkSelectedCategory(_ value: ServicesCategoryModel) -> Bool { selectedCategory == value }
    func updateSelectedCategory(_ value: ServicesCategoryModel) { selectedCategory = value }
}
