//
//  TestCell.swift
//  Bookme
//
//  Created by Apple on 11/03/2024.
//

import SwiftUI


struct CategoryCell: View {
    let title:String
    let selected:Bool
    var body: some View {
        HStack {
           
            Text(title)
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(11.0)))
                .fontWeight(.semibold)
                .padding(.horizontal, getRelativeWidth(14.0))
                .padding(.bottom, getRelativeHeight(5.0))
                .padding(.top, getRelativeHeight(7.0))
                .foregroundColor(selected ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                
                .multilineTextAlignment(.center)
                .frame(minWidth: getRelativeWidth(78.0),
                       alignment: .center)
                .frame(height: getRelativeHeight(29.0))
                .overlay(Capsule()
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(selected ? ColorConstants.Cyan800.clipShape(.capsule) : ColorConstants.Cyan8003f.clipShape(.capsule))
                
                
          
        }

    }
}

struct CategoryShimmerCell: View {
  
    var body: some View {
        HStack {
           
            Text("title")
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(11.0)))
                .fontWeight(.semibold)
                .padding(.horizontal, getRelativeWidth(14.0))
                .padding(.bottom, getRelativeHeight(5.0))
                .padding(.top, getRelativeHeight(7.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                
                .multilineTextAlignment(.center)
                .frame(minWidth: getRelativeWidth(78.0),
                       alignment: .center)
                .frame(height: getRelativeHeight(29.0))
                .overlay(Capsule()
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan8003f.clipShape(.capsule))
                
                
          
        }

    }
}

struct CategoryCell_Previews: PreviewProvider {
    static var previews: some View {
        CategoryCell(title: "hello", selected: true)
    }
}
