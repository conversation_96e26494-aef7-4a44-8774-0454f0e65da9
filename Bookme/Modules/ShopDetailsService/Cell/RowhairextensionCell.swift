import SwiftUI

struct RowhairextensionCell: View {
    let model: VendorDetailsModel.Service
    let checked: Bool
    let onSelect:(VendorDetailsModel.Service) -> Void
    @EnvironmentObject private var appState:AppState
    
   
    
    var body: some View {
        Button(action: {
//            checked.toggle()
            onSelect(model)
        }, label: {
            VStack {
                HStack {
                    VStack(alignment: .leading, spacing: 0) {
                        Text(model.name)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            
                            .multilineTextAlignment(.leading)
                            
                        Text("\(model.serviceTime) Min")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(11.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Black900)
                            .multilineTextAlignment(.leading)
                            .padding(.top, getRelativeHeight(18.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                    }
                    //                .frame(width: getRelativeWidth(93.0), height: getRelativeHeight(52.0),
                    //                       alignment: .leading)
                    .padding(.leading, getRelativeWidth(14.0))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    VStack(alignment: .trailing, spacing: 12) {
                        CurrencyText(model.price ?? "")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .multilineTextAlignment(.leading)
                            .padding(.horizontal, 8.0.relativeWidth)
                            .frame(height: getRelativeHeight(22.0),
                                   alignment: .center)
                            .background(ColorConstants.Cyan8003f.clipShape(.rect(cornerRadii: .init(topLeading: 8.0.relativeHeight, bottomLeading: 8.0.relativeHeight, bottomTrailing: 8.0.relativeHeight, topTrailing: 8.0.relativeHeight))))
                           
                        CheckBoxView(checked: .constant(checked))
                            .disabled(true)
                    }
                    
                    //                .padding(.leading, getRelativeWidth(197.0))
                }
                
                Divider()
                    .foregroundStyle(.clear)
                    .background(ColorConstants.Cyan90030)
                //                .overlay(RoundedCorners().stroke(ColorConstants.Cyan90030, lineWidth: 1))
                   
            }.padding(.horizontal)
            
        })
        .onAppear{
//            checked = appState.bookingRequestModelList.contains(where: { $0.service.servicesID == model.servicesID })
        }
    }
}

struct RowhairextensionShimmerCell: View {
    var body: some View {
        Button(action: {
        }, label: {
            VStack {
                HStack {
                    VStack(alignment: .leading, spacing: 0) {
                        Text("model.name")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .multilineTextAlignment(.leading)
                        Text("\(0) Min")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(11.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Cyan800)
                            .multilineTextAlignment(.leading)
                            .padding(.top, getRelativeHeight(18.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                    }
                    .padding(.leading, getRelativeWidth(14.0))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    VStack(alignment: .trailing, spacing: 12) {
                        CurrencyText("0.000")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .multilineTextAlignment(.leading)
                            .padding(.horizontal, 8.0.relativeWidth)
                            .frame(height: getRelativeHeight(22.0),
                                   alignment: .center)
                            .background(ColorConstants.Cyan8003f.clipShape(.rect(cornerRadii: .init(topLeading: 8.0.relativeHeight, bottomLeading: 8.0.relativeHeight, bottomTrailing: 8.0.relativeHeight, topTrailing: 8.0.relativeHeight))))
                           
                        CheckBoxView(checked: .constant(false))
                            .disabled(true)
                    }
                }
                
                Divider()
                    .foregroundStyle(.clear)
                    .background(ColorConstants.Cyan90030)
                   
            }.padding(.horizontal)
            
        })
    }
}

// struct RowhairextensionCell_Previews: PreviewProvider {
//    static var previews: some View {
//        RowhairextensionCell()
//    }
// }

struct CheckBoxView: View {
    var size: CGSize = .init(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0))
    @Binding var checked: Bool

    var body: some View {
        Button(action: {
            self.checked.toggle()
        }, label: {
//            Image(systemName: checked ? "checkmark.square.fill" : "square")
            Image( checked ? .checkmark : .uncheckmark)
                .resizable()
                .frame(width: size.width, height: size.height, alignment: .center)
                .foregroundColor(checked ? ColorConstants.Cyan800 : ColorConstants.Cyan8007f)
        })
    }
}



struct CurrencyText:View {
    let text:String?
    init(_ text: String?) {
        self.text = text
    }
    
    var body: some View {
        Text(Double(text ?? "0") ?? 0, format: .currency(code: "KWD").presentation(.isoCode))
    }
}

