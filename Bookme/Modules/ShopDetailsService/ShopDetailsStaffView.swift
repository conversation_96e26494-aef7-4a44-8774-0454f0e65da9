//
//  ShopDetailsStaffs.swift
//  Bookme
//
//  Created by Apple on 01/10/2024.
//

import SwiftUI

struct ShopDetailsStaffView: View {
    let selectedStaffID: String?
    let onSelect: (StaffModel.StaffData) -> Void
    @StateObject var viewModel: ShopDetailsStaffViewModel
    
    init(staffModelRequest:StaffModelRequest, selectedStaffID: String?, onSelect: @escaping (StaffModel.StaffData) -> Void) {
     
        self.selectedStaffID = selectedStaffID
        self.onSelect = onSelect
        self._viewModel = StateObject(wrappedValue: ShopDetailsStaffViewModel(staffModelRequest: staffModelRequest))
    }
    
    @Environment(\.dismiss) var dismiss: DismissAction
    @Namespace private var animation
    @EnvironmentObject private var appState: AppState
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            ShopDetailsStaffShimmerView()
        } content: {
            MainScrollBody {
                mainSection
            }.background(.white)
        }
    }
    
    var mainSection: some View {
        VStack(alignment: .leading, spacing: 0) {
                   
            serviceListView
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
    }
    
    var serviceListView: some View {
        VStack(spacing: 0) {
            LazyVStack {
                if viewModel.serviceStaffModelList.isEmpty && viewModel.pageState != .loading(true) {
                    CustomPlaceholder(placeholderType: .noData)
                } else {
                    ForEach(viewModel.serviceStaffModelList) { model in
                            
                        let checked: Bool = selectedStaffID == model.staffID
                        ShopDetailsStaffViewCell(model: model, isSelected: checked, onSelect: onSelect)
                                
                            .transition(.flipFromTop)
                    }
                }
            }
           
            .padding(.top, 16)
        }
    }
}

struct ShopDetailsStaffViewCell: View {
    let model: StaffModel.StaffData
  
    let isSelected: Bool
    let onSelect: (StaffModel.StaffData) -> Void
    @EnvironmentObject private var appState: AppState
    
    var body: some View {
        Button(action: {
            onSelect(model)
        }, label: {
            VStack {
                HStack {
                    // Circle image with border
                    NetworkImageView(path: model.staffImageUrl)
                        .frame(width: 38.relativeFontSize, height: 38.relativeFontSize)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .inset(by: 0.5)
                                .stroke(isSelected ? Color(hex: "#008F96") : Color(red: 0, green: 0.56, blue: 0.59).opacity(0.21), lineWidth: 1)
                        ).overlay(alignment: .topTrailing) {
                            // Checkmark icon if selected
                            if isSelected {
                                Image(.staffCheckmark)
                                    .resizable()
                                    .padding(4)
                                    .frame(width: 20.relativeFontSize, height: 20.relativeFontSize)
                                    .offset(x: 7, y: -1)
                            }
                        }
                    
                    VStack(alignment: .leading, spacing: 2.relativeHeight) {
                        Text(model.staffName)
                            .font(Font.custom("Nunito", size: 13.relativeFontSize).weight(.bold))
                            .multilineTextAlignment(.center)
                            .foregroundColor(.black.opacity(0.75))
                        
                        Text(model.message)
                            .font(Font.custom("Nunito", size: 10.relativeFontSize))
                            .multilineTextAlignment(.center)
                            .foregroundColor(model.available ? Color(red: 0.07, green: 0.76, blue: 0.57) : Color(red: 0.8, green: 0.05, blue: 0.06))
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    
                    Image(.arrowFill)
                        .frame(width: 7.relativeFontSize, height: 13.relativeFontSize)
                        .visibility(model.notAvailable ? .gone : .visible)
                }
                .padding(.horizontal, 22.relativeWidth)
                
                Divider()
                    .foregroundStyle(.clear)
                    .background(ColorConstants.Cyan90030)
                   
                //                .overlay(RoundedCorners().stroke(ColorConstants.Cyan90030, lineWidth: 1))
            }
            
        })
        .disabled(model.notAvailable)
    }
}

struct ShopDetailsStaffShimmerView: View {
    var body: some View {
        mainSection
            .shimmerize()
    }
    
    var mainSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            serviceListView
        }
        
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
    }
    
    var serviceListView: some View {
        VStack(spacing: 0) {
            LazyVStack {
                ForEach(0 ... 8, id: \.self) { _ in
                            
                    ShopDetailsStaffShimmerViewCell()
                }
            }
           
            .padding(.top, 32)
        }
        .frame(width: getRelativeWidth(373.0), alignment: .center)
        .padding(.top, getRelativeHeight(8.0))
        .padding(.leading, getRelativeWidth(13.0))
        .padding(.trailing, getRelativeWidth(4.0))
    }
}

struct ShopDetailsStaffShimmerViewCell: View {
    var body: some View {
        Button(action: {}, label: {
            VStack {
                HStack {
                    VStack(alignment: .leading, spacing: 0) {
                        HStack {
                            NetworkImageView(path: nil)
                                .frame(width: getRelativeWidth(23.0),
                                       height: getRelativeWidth(23.0), alignment: .center)
                                
                                .clipShape(Circle())
                            
                            Text("model.staffName")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .multilineTextAlignment(.leading)
                        }
                    }
                    .padding(.leading, getRelativeWidth(14.0))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    VStack(alignment: .trailing, spacing: 12) {
                        CheckBoxView(checked: .constant(false))
                            .disabled(true)
                    }
                    
                    //                .padding(.leading, getRelativeWidth(197.0))
                }
                
                Divider()
                    .foregroundStyle(.clear)
                    .background(ColorConstants.Cyan90030)
                //                .overlay(RoundedCorners().stroke(ColorConstants.Cyan90030, lineWidth: 1))
                   
            }.padding(.horizontal)
            
        })
        .onAppear {
//            checked = appState.bookingRequestModelList.contains(where: { $0.service.servicesID == model.servicesID })
        }
    }
}
