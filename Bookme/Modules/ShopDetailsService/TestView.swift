//
//  TestView.swift
//  Bookme
//
//  Created by Apple on 19/10/2024.
//

import SwiftUI

struct TestView : View {
    @Environment(\.dismiss) var dismiss: DismissAction
    @State private var searchText: String = .init()
    @State private var isScrolled: Bool = false
    var body: some View {
        ScrollViewWithOffset(.vertical, showsIndicators: false) { self.isScrolled = $0.y < 0 }
            content: {
                VStack(spacing: 16){
                    ForEach(0...100, id: \.self) { index in
                        Text("item \(index)")
                    }
                }
               
            }
            .safeAreaPadding(.vertical)
            .safeAreaInset(edge: .top, content: {
                HStack(spacing: 19.relativeWidth) {
                    Button {
                        dismiss()
                    } label: {
                        Image(.imgArrowleftWhiteA700)
                            .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
                            .foregroundStyle(.black)
                            .frame(width: getRelativeWidth(18.0),
                                   height: getRelativeHeight(18.0), alignment: .center)
                    }
                    
                    HStack(spacing: 14.relativeWidth) {
                        Image(.searchServices)
                            .frame(width: 18.0009, height: 18)
                        
                        TextField("", text: $searchText, prompt: Text("Search Services....")
                            .font(Font.custom("Montserrat", size: 13).weight(.medium))
                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.7)))
                            .font(Font.custom("Montserrat", size: 13).weight(.medium))
                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.7))
                    }
                    .padding(.horizontal, 9.relativeWidth)
                    .frame(height: 45)
                    .frame(maxWidth: .infinity)
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .inset(by: 0.5)
                            .stroke(Color(red: 0, green: 0.56, blue: 0.59), lineWidth: 1)
                    )
                }
                .padding(.bottom, 8)
                .padding(.horizontal)
                .overlay(alignment: .bottom, content: {
                    Divider()
                        .visibility(isScrolled ? .visible: .gone)
                })
                .if(isScrolled, transform: {
                    $0.background(.thinMaterial)
                })
                .if(!isScrolled, transform: {
                    $0.background(ColorConstants.WhiteA700)
                })
                
               
            })
            .hideNavigationBar()
            .background(ColorConstants.WhiteA700)
    }
}



#Preview {
    TestView()
}
