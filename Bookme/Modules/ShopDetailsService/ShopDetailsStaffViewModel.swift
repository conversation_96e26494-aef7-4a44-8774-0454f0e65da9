//
//  ShopDetailsStaffViewModel.swift
//  Bookme
//
//  Created by Apple on 01/10/2024.
//

import SwiftUI

class ShopDetailsStaffViewModel: SuperViewModel {
    @Published var serviceStaffModelList: [StaffModel.StaffData] = []

    let staffModelRequest: StaffModelRequest
    init(staffModelRequest: StaffModelRequest) {
        self.staffModelRequest = staffModelRequest
        super.init()
        getServiceStaffList()
    }

    func getServiceStaffList() {
        onApiCall(api.staffAvailableSlots, parameters: staffModelRequest.toDictionary) {
            self.serviceStaffModelList = $0.data?.staffData ?? []
        }
    }
}

struct StaffModelRequest: Encodable, Equatable, Hashable {
    let serviceID: Int
    let vendorID, date, time: String

    enum CodingKeys: String, CodingKey {
        case vendorID = "vendor_id"
        case serviceID = "service"
        case date, time
    }
}
