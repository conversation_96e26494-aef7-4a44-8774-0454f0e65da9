import SwiftUI

struct ShopDetailsServiceView: View {
    var isFromBooking: Bool

    let onSelect: (VendorDetailsModel.Service) -> Void
    let selectedServiceIDs: [Int]
    @StateObject var viewModel: ShopDetailsServiceViewModel
    @Namespace private var animation
    @Environment(\.dismiss) var dismiss: DismissAction
    
    init(isFromBooking: Bool = true, vendorID: Int?, selectedServiceIDs: [Int], onSelect: @escaping (VendorDetailsModel.Service) -> Void) {
        self.isFromBooking = isFromBooking
        self.selectedServiceIDs = selectedServiceIDs
        self.onSelect = onSelect
        self._viewModel = StateObject(wrappedValue: ShopDetailsServiceViewModel(vendorID: vendorID, isFromBooking: isFromBooking))
    }
    
    var body: some View {
        if isFromBooking {
            ScrollViewWithOffset(.vertical, showsIndicators: false) { self.viewModel.isScrolled = $0.y < 0 }
                content: {
                    mainSection
                }
                .scrollDismissesKeyboard(.interactively)
                .safeAreaPadding(.vertical)
                .safeAreaInset(edge: .top, content: {
                    safeAreaView
                })
                .hideNavigationBar()
                .background(ColorConstants.WhiteA700)
        } else {
            mainSection
        }
    }
    
    var mainSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            
            
            
            
            if isFromBooking {
                if viewModel.pageState == .loading(true) {
                    VStack(spacing: 10.relativeHeight) {
                        ForEach(0 ... 5, id: \.self) { _ in
                            ServiceBoxShimmerView()
                                .shimmerize()
                        }
                    }
                } else {
                    VStack(spacing: 10.relativeHeight) {
                        ForEach(viewModel.servicesCategoryList) { service in
                            
                            
                            let filteredData: [VendorDetailsModel.Service] = viewModel.filteredServices.filter { $0.serviceCategory == service.catIDInt || service.catIDInt == 0 }
                            // Step 1: Extract ids from both arrays
                            let firstArrayIDs = Set(selectedServiceIDs)
                            let secondArrayIDs = Set(filteredData.map { $0.servicesID })
                            let commonIDs = firstArrayIDs.intersection(secondArrayIDs)
                            
                            ServiceBoxView(isSearching: !viewModel.searchText.isEmpty, model: service, filteredData: filteredData, selectedServiceIDs: selectedServiceIDs, isExpanded: !commonIDs.isEmpty && service.catName.lowercased() != "all", onSelect: onSelect)
                        }
                    }
                }
                
            } else {
                serviceCategoryView
                
                serviceListView
            }
        }
        
        .animation(.bouncy, value: viewModel.selectedCategory)
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
    }
    
    var serviceCategoryView: some View {
        ScrollView(.horizontal) {
            LazyHStack(content: {
                if viewModel.pageState == .loading(true) {
                    ForEach(0 ... 9, id: \.self) { _ in CategoryShimmerCell()}
                    .shimmerize()
                } else {
                    ForEach(viewModel.servicesCategoryList) { item in
                                    
                        Button(action: {
                            viewModel.updateSelectedCategory(item)
                        }, label: {
                            CategoryCell(title: item.catName, selected: viewModel.checkSelectedCategory(item))
                                .if(viewModel.checkSelectedCategory(item)) {
                                    $0.matchedGeometryEffect(id: "category.chip", in: animation)
                                }
                        })
                    }
                }
                
            })
                
            .padding(.horizontal)
        }
        .scrollClipDisabled()
        .scrollIndicators(.hidden)
        .frame(maxHeight: 50)
    }
    
    var safeAreaView: some View {
        HStack(spacing: 12.relativeWidth) {
            Button {
                dismiss()
            } label: {
                Image(.imgArrowleftWhiteA700)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .foregroundStyle(.black)
                    .frame(width: getRelativeWidth(18.0),
                           height: getRelativeHeight(18.0), alignment: .center)
                    .frame(width: 40,
                           height: 40, alignment: .center)
//                    .background(.orange)
            }
           
            
            HStack(spacing: 14.relativeWidth) {
                Image(.searchServices)
                    .frame(width: 18.0009, height: 18)
                
                TextField("", text: $viewModel.searchText, prompt: Text("Search Services....")
                    .font(Font.custom("Montserrat", size: 13).weight(.medium))
                    .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.7)))
                    .font(Font.custom("Montserrat", size: 13).weight(.medium))
                    .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.7))
            }
            .padding(.horizontal, 9.relativeWidth)
            .frame(height: 45)
            .frame(maxWidth: .infinity)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .inset(by: 0.5)
                    .stroke(Color(red: 0, green: 0.56, blue: 0.59), lineWidth: 1)
            )
        }
        .padding(.bottom, 8)
        .padding(.horizontal)
        .overlay(alignment: .bottom, content: {
            Divider()
                .visibility(viewModel.isScrolled ? .visible : .gone)
        })
        .if(viewModel.isScrolled, transform: {
            $0.background(.regularMaterial)
        })

    }
    
    var serviceListView: some View {
        VStack(spacing: 0) {
            LazyVStack {
                if viewModel.pageState == .loading(true) {
                    ForEach(0...9, id: \.self) { index in
                        RowhairextensionShimmerCell()
                            .transition(.flipFromTop)
                    }
                    .shimmerize()
                }else{
                   
                    let filteredData: [VendorDetailsModel.Service] = viewModel.services.filter { $0.serviceCategory == viewModel.selectedCategory?.catIDInt || viewModel.selectedCategory?.catIDInt == 0 }
                        
                        if filteredData.isEmpty {
                            CustomPlaceholder(placeholderType: .noData)
                        } else {
                            ForEach(filteredData) { model in
                                
                                let checked: Bool = selectedServiceIDs.contains(model.servicesID)
                                RowhairextensionCell(model: model, checked: checked, onSelect: onSelect)
                                    
                                    .disableWithOpacity(isFromBooking && checked)
                                    .transition(.flipFromTop)
                            }
                        }
                    
                }
                
                
               
            }
            .padding(.top, 32)
        }
        .frame(width: getRelativeWidth(373.0), alignment: .center)
        .padding(.top, getRelativeHeight(8.0))
        .padding(.leading, getRelativeWidth(13.0))
        .padding(.trailing, getRelativeWidth(4.0))
    }
}

struct ServiceBoxShimmerView: View {
    var body: some View {
        VStack {
            Button {} label: {
                VStack {
                    HStack {
                        Text("model.catName")
                            .font(
                                Font.custom("Nunito", size: 15)
                                    .weight(.bold)
                            )
                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                            
                        Spacer()
                            
                        Image(.serviceExpand)
                            .frame(width: 24, height: 24)
                            .rotationEffect(.degrees(0))
                    }
                    .padding(.top, 0)
                    .padding(.leading, 8)
                    .frame(height: 45)
                }
            }
        }
        .padding(.horizontal)
        .background(ColorConstants.WhiteA700)
        .cornerRadius(10)
        .overlay(RoundedRectangle(cornerRadius: 10).inset(by: 0.5).stroke(Color(red: 0, green: 0.56, blue: 0.59), lineWidth: 1))
        .padding(.horizontal)
    }
}

struct ServiceBoxView: View {
    let isSearching: Bool
    let model: ServicesCategoryModel
    let filteredData: [VendorDetailsModel.Service]
    let selectedServiceIDs: [Int]

    @State var isExpanded: Bool
    let onSelect: (VendorDetailsModel.Service) -> Void
    
    
   
    var body: some View {
        if isSearching && filteredData.isEmpty {
            EmptyView()
        } else {
            VStack {
                Button {
                    withAnimation(.bouncy) {
                        isExpanded.toggle()
                    }
                    
                } label: {
                    VStack {
                        HStack {
                            Text(model.catName)
                                .font(
                                    Font.custom("Nunito", size: 15)
                                        .weight(.bold)
                                )
                                .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                            
                            Spacer()
                            
                            Image(.serviceExpand)
                                .frame(width: 24, height: 24)
                                .rotationEffect(isExpanded ? .degrees(180) : .degrees(0))
                        }
                        .padding(.top, isExpanded ? 8 : 0)
                        .padding(.leading, 8)
                        .frame(height: 45)
                        
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(height: 1)
                            .frame(maxWidth: .infinity)
                            .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.5))
                            .padding(.bottom, 8)
                            .visibility(isExpanded ? .visible : .gone)
                    }
                }

                serviceListView
                   
                    .padding(.horizontal, -8)
                    .visibility(isExpanded ? .visible : .gone)
            }
            
            .padding(.horizontal)
            .background(ColorConstants.WhiteA700)
            .cornerRadius(10)
            .overlay(RoundedRectangle(cornerRadius: 10).inset(by: 0.5).stroke(Color(red: 0, green: 0.56, blue: 0.59), lineWidth: 1))
            .padding(.horizontal)
            .animation(.bouncy, value: filteredData)
        }
    }
    
    var serviceListView: some View {
        VStack(spacing: 0) {
            LazyVStack {
                ForEach(filteredData) { model in
                    
                    
                        
                    let checked: Bool = selectedServiceIDs.contains(model.servicesID)
                    ServiceBoxCellView(model: model, checked: checked, onSelect: onSelect)
//                        .transition(.slide.combined(with: .opacity))
                }
            }
        }
    }
        
}

struct ServiceBoxCellView: View {
    let model: VendorDetailsModel.Service
    let checked: Bool
    let onSelect: (VendorDetailsModel.Service) -> Void
    
    var body: some View {
        VStack {
            HStack {
                VStack(alignment: .leading, spacing: 0) {
                    Text(model.name)
                        .font(Font.custom("Nunito", size: 12.relativeFontSize).weight(.bold))
                        .kerning(0.52)
                        .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                        
                    Text("\(model.serviceTime) Min")
                        .font(Font.custom("Nunito", size: 11.relativeFontSize))
                        .kerning(0.52)
                        .foregroundColor(.black.opacity(0.7))
                        .padding(.top, getRelativeHeight(18.0))
                        .padding(.trailing, getRelativeWidth(10.0))
                }
                .padding(.leading, getRelativeWidth(14.0))
                .frame(maxWidth: .infinity, alignment: .leading)
                VStack(alignment: .trailing, spacing: 6) {
                    CurrencyText(model.price ?? "")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                        .padding(.horizontal, 8.0.relativeWidth)
                        .frame(height: getRelativeHeight(22.0),
                               alignment: .center)
                        .background(ColorConstants.Cyan8003f.clipShape(.rect(cornerRadii: .init(topLeading: 8.0.relativeHeight, bottomLeading: 8.0.relativeHeight, bottomTrailing: 8.0.relativeHeight, topTrailing: 8.0.relativeHeight))))
                     
                    Button {
                        onSelect(model)
                    } label: {
                        Text("Book").font(Font.custom("Nunito", size: 13.relativeFontSize).weight(.semibold))
                            .multilineTextAlignment(.center)
                            .foregroundColor(.white)
                            .padding(10)
                            .frame(width: 80, height: 28, alignment: .center)
                            .background(Color(red: 0, green: 0.56, blue: 0.59))
                            .cornerRadius(18)
                            .shadow(color: .black.opacity(0.16), radius: 1, x: 0, y: 1)
                    }
                    .disableWithOpacity(checked)
                }
            }
            
            Divider()
                .foregroundStyle(.clear)
                .background(ColorConstants.Cyan90030)
        }
    }
}
