//
//  VendorServicesCategoryModel.swift
//  Bookme
//
//  Created by Apple on 22/10/2024.
//

import Foundation

// MARK: - VendorServicesCategoryModel

struct VendorServicesCategoryModel: Codable, Identifiable, Equatable {
    let catID: Int
    let catName: String
    let services: [Service]

    enum CodingKeys: String, CodingKey {
        case catID = "Cat_ID"
        case catName = "Cat_Name"
        case services = "Services"
    }
    
    
    var id: Int { catID }
    
    // MARK: - Service
    struct Service: Codable, Identifiable, Equatable {
        let servicesID: Int
        let name, image, servicesfor, servicePrice: String
        let serviceTime: String
        let serviceCategory, staffID, vendorID, enabled: Int
        let afterdiscountprice, price, commision: String
        
        var id: Int { servicesID }

        enum CodingKeys: String, CodingKey {
            case servicesID = "ServicesID"
            case name = "Name"
            case image = "Image"
            case servicesfor
            case servicePrice = "Service_Price"
            case serviceTime = "Service_Time"
            case serviceCategory = "Service_Category"
            case staffID = "Staff_ID"
            case vendorID = "vendor_id"
            case enabled
            case afterdiscountprice = "Afterdiscountprice"
            case price, commision
        }
    }
}



// MARK: - StaffModel
struct StaffModel: Codable {
    let selectedDate: String
    let staffData: [StaffData]
    
    enum CodingKeys: String, CodingKey {
        case selectedDate = "selected_date"
        case staffData = "time_slot"
    }
    
    
    // MARK: - TimeSlot
    struct StaffData: Codable, Identifiable, Equatable, Hashable {
        let staffImage: String
        let staffName: String
        let staffID: String // Change staffID to String to handle both Int and String cases
        let available: Bool
        let isSelectable:Bool
        let message: String
        let offDays: [String]?
        
        // Custom initializer to handle the case where staffID can be an Int or a String
        init(from decoder: Decoder) throws {
            let container = try decoder.container(keyedBy: CodingKeys.self)
            
            // Try to decode staffID as a String first, if it fails, try to decode as an Int and convert it to String
            if let staffIDString = try? container.decode(String.self, forKey: .staffID) {
                self.staffID = staffIDString
            } else if let staffIDInt = try? container.decode(Int.self, forKey: .staffID) {
                self.staffID = String(staffIDInt)
            } else {
                // Handle missing or unknown value for staffID, you can throw an error or provide a default value
                throw DecodingError.dataCorruptedError(forKey: .staffID, in: container, debugDescription: "staff_id is not a valid String or Int")
            }
            
            self.staffImage = try container.decode(String.self, forKey: .staffImage)
            self.staffName = try container.decode(String.self, forKey: .staffName)
            self.available = try container.decode(Bool.self, forKey: .available)
            self.isSelectable = try container.decode(Bool.self, forKey: .isSelectable)
            self.message = try container.decode(String.self, forKey: .message)
            self.offDays = try container.decodeIfPresent([String].self, forKey: .offDays)
        }
        
    
        
        // Computed property to meet `Identifiable` protocol
        var id: String { staffID }
        
        // Computed property to check if staff is not available
        var notAvailable: Bool { !isSelectable }
        
        // Computed property for staff image URL
        var staffImageUrl: String {
            AppConstants.Server.baseURL + staffImage
        }
        
      
        
        // Coding keys to map JSON keys to Swift property names
        enum CodingKeys: String, CodingKey {
            case staffImage = "staff_image"
            case staffName = "staff_name"
            case staffID = "staff_id"
            case isSelectable = "isselectable"
            case available, message
            case offDays = "offdays"
        }
    }
    
}

