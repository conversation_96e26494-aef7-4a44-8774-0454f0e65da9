//
//  UserCardViewModel.swift
//  Bookme
//
//  Created by Apple on 23/11/2024.
//
import SwiftUI

class UserPaymentCardViewModel: SuperViewModel {
    @Published var currentIndex: Int? = 0
    @Published var cardList: [PaymentCardModel.CustomerCard] = []
    @Published var checkoutSuccessModel: CheckoutSuccessModel?
    
    override init() {
        super.init()
        
        self.listAllCards()
    }
    
    func listAllCards() {
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": userID]
        onApiCall(api.listAllCards, parameters: parameters) {
            self.cardList = $0.data?.customerCards ?? []
        }
    }
    
    func addNewCard(completion: @escaping (String) -> Void) {
        guard let userID = AppState.userModel?.user.id else { return }
        let parameters: [String: Any] = ["user_id": userID]
        onApiCall(api.addCard, parameters: parameters) {
            if let data = $0.data {
                completion(data)
            }
        }
    }
}
