//
//  CheckoutPaymentMethodView.swift
//  Bookme
//
//  Created by Apple on 14/09/2024.
//

import EventKit
import EventKitUI
import SwiftUI

struct UserPaymentCardView: View {
    let routesType: RoutesType
    
    @StateObject var viewModel: UserPaymentCardViewModel = .init()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager: RouterManager
    
    @State private var showAlert = false
    @State private var showAddBookingAlert = false
    @State private var eventStore = EKEventStore()
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(backButtonWithTitle: "") {
                VStack {
                    VStack {
                        HStack {
                            Text("Save Cards")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Black900)
                                .multilineTextAlignment(.leading)
                                .fixedSize()
                               
                            Button(action: {
//                                let type = routerManager.mapRouterWithTab(appState: appState)
//                                routerManager.push(to: .addCard(type: type), where: type)
                                
                                viewModel.addNewCard { url in
                                    let type = routerManager.mapRouterWithTab(appState: appState)
                                    routerManager.push(to: .uPaymentCard(link: url, onRedirect: .init(onBack: {
                                        if $0 {
                                            routerManager.goBack(where: type)
                                            self.viewModel.listAllCards()
                                        }
                                    })), where: type)
                                }
                                
                            }, label: {
                                HStack {
                                    Image("img_add_light")
                                        .resizable()
                                        .frame(width: getRelativeWidth(20.0),
                                               height: getRelativeWidth(20.0), alignment: .center)
                                        .scaledToFit()
                                        .clipped()
                                        .padding(.leading, getRelativeWidth(183.0))
                                    Text("Add Card")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .fixedSize()
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(20.0), alignment: .topLeading)
                                       
                                        .padding(.leading, getRelativeWidth(0.0))
                                }
                            })
                        }
                        .frame(width: getRelativeWidth(348.0), height: getRelativeHeight(25.0),
                               alignment: .center)
//                        .padding(.top, getRelativeHeight(14.0))
                        .padding(.horizontal, getRelativeWidth(21.0))
                        VStack {
                            if viewModel.cardList.isEmpty {
                              
                                CustomPlaceholder(placeholderType: .noData,title: "There is no Cards Saved", subTitle: "", image: .noCard, size: .init(width: 78.relativeFontSize, height: 78.relativeFontSize), titleColor: .black)
                                        .padding(.top, 32.relativeHeight)
                                
                            } else {
                                PageView(viewModel.cardList, selection: $viewModel.currentIndex.animation()) {
                                    PaymentCardView(model: $0)
                                       
                                        .scaleEffect(0.96)
                                }
                                PageIndicatorOptional(numPages: viewModel.cardList.count,
                                                      currentPage: $viewModel
                                                          .currentIndex,
                                                      selectedColor: ColorConstants.Cyan800,
                                                      unSelectedColor: ColorConstants.Cyan8003f,
                                                      spacing: 8.0, diameter: 8, isCircular: false)
                                    .animation(.bouncy, value: viewModel.currentIndex)
                            }
                        }
                        .frame(height: getRelativeHeight(199.0),
                               alignment: .trailing)
                        .padding(.top, getRelativeHeight(16.0))
                        
//                        Text(StringConstants.kMsgOtherWayToPayment)
//                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
//                            .fontWeight(.bold)
//                            .foregroundColor(ColorConstants.Black900)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                            .frame(width: getRelativeWidth(167.0), height: getRelativeHeight(22.0),
//                                   alignment: .topLeading)
//                            .padding(.top, getRelativeHeight(45.0))
//                            .padding(.leading, getRelativeWidth(22.0))
//                        VStack(spacing: 0) {
//                            LazyVStack(spacing: 10.0.relativeHeight) {
//                                ForEach(viewModel.paymentMethodArray) { model in
//                                    RowellipseCell(model: model, isSelected: viewModel.selectedPaymentMethod == model)
//                                        .onTapGesture {
//                                            viewModel.updateSelectedPaymentMethod(model)
//                                        }
//                                }
//                            }.animation(.bouncy, value: viewModel.selectedPaymentMethod)
//                        }
//                        .padding()
                    }
                }
                
                .background(ColorConstants.WhiteA700)
                .padding(.top, getRelativeHeight(30.0))
                .padding(.bottom, getRelativeHeight(10.0))
            }
//            .safeAreaInset(edge: .bottom, content: {
//                VStack {
            ////                    Text(StringConstants.kMsgAmountToPayKd)
            ////                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
            ////                        .fontWeight(.heavy)
            ////                        .foregroundColor(ColorConstants.Black900)
            ////                        .minimumScaleFactor(0.5)
            ////                        .multilineTextAlignment(.leading)
            ////                        .frame(width: getRelativeWidth(181.0), height: getRelativeHeight(20.0),
            ////                               alignment: .topLeading)
            ////
            ////                    Button(action: {
            //////                        let type = routerManager.mapRouterWithTab(appState: appState)
            //////                        self.paymentmethodeViewModel.onBook {
            ////
            //////                            routerManager.push(to: .bookingSuccess(type: type, viewModel: paymentmethodeViewModel), where: type)
            ////
            //////                        } authFail: {
            //////                            routerManager.push(to: .signIn(type: type), where: type)
            //////                        }
            ////
            ////                    }, label: {
            ////                        HStack(spacing: 0) {
            ////                            Text(StringConstants.kMsgProceedToPayment)
            ////                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
            ////                                .fontWeight(.heavy)
            ////                                .padding(.horizontal, getRelativeWidth(30.0))
            ////                                .padding(.vertical, getRelativeHeight(13.0))
            ////                                .foregroundColor(ColorConstants.WhiteA700)
            ////                                .minimumScaleFactor(0.5)
            ////                                .multilineTextAlignment(.leading)
            ////                                .frame(width: getRelativeWidth(310.0),
            ////                                       height: getRelativeHeight(47.0), alignment: .center)
            ////                                .background(RoundedCorners(topLeft: 23.0, topRight: 23.0,
            ////                                                           bottomLeft: 23.0, bottomRight: 23.0)
            ////                                        .fill(ColorConstants.Cyan800))
            ////                                .shadow(color: ColorConstants.Black90044, radius: 2, x: 0, y: 1)
            ////                                .padding(.bottom, getRelativeHeight(23.0))
            ////                                .padding(.horizontal, getRelativeWidth(39.0))
            ////                        }
            ////                    })
            ////                }
            ////                .padding(.bottom, AppConstants.tabBarHeight.relativeHeight)
//
//            })
            .background(ColorConstants.WhiteA700)
        }
//        .alert("Add Booking to Calendar", isPresented: $showAddBookingAlert) {
//            Button("Cancel", role: .cancel) {}
//            Button("Yes") {
//                let eventTitle = "Booking"
//                let eventLocation = "Online"
//                let startDate = Date()
//                let endDate = Date().addingTimeInterval(60 * 60) // 1 hour after startDate
//                addEventToCalendar(title: eventTitle, location: eventLocation, startDate: startDate, endDate: endDate)
//            }
//        } message: {
//            Text("Do you want add this booking to your calendar?")
//        }
//        .alert("Error", isPresented: $showAlert) {
//            Button("Cancel", role: .cancel) {}
//            Button("Settings") {
//                if let url = URL(string: UIApplication.openSettingsURLString) {
//                    UIApplication.shared.open(url, options: [:], completionHandler: nil)
//                }
//            }
//        } message: {
//            Text("Do you want add this booking to your calendar?")
//        }
    }
    
    private func requestAccessToCalendar(title: String, location: String, startDate: Date, endDate: Date) {
        eventStore.requestFullAccessToEvents { granted, _ in
            if granted {
                addEventToCalendar(title: title, location: location, startDate: startDate, endDate: endDate)
            } else {
                showAlert = true
            }
        }
    }

    private func addEventToCalendar(title: String, location: String, startDate: Date, endDate: Date) {
        let event = EKEvent(eventStore: eventStore)
        event.title = title
        event.location = location
        event.startDate = startDate
        event.endDate = endDate
        event.calendar = eventStore.defaultCalendarForNewEvents

        do {
            try eventStore.save(event, span: .thisEvent)
            print("Event added to calendar!")
        } catch {
            print("Error saving event: \(error.localizedDescription)")
        }
    }
}

#Preview {
    NavigationStack {
        UserPaymentCardView(routesType: .myAccountRoute).attachAllEnvironmentObjects()
    }
}

struct PageView<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let content: (Item) -> Content

    @Binding var selection: Int?
    @State private var position: Int? = 0
    init(_ items: [Item], selection: Binding<Int?>, @ViewBuilder content: @escaping (Item) -> Content) {
        self.items = items
        self.content = content
        self._selection = selection
    }

    var body: some View {
        GeometryReader { geometry in
         
            ScrollView(.horizontal) {
                LazyHStack(spacing: 0) {
                    ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                        content(item)
                            .frame(width: items.count > 1 ? nil : geometry.size.width - 32, height: geometry.size.height)
                            .id(index) // Assign an ID for each item to target in scroll
                    }
                }
                .scrollTargetLayout()
            }
            .safeAreaPadding(.horizontal)
            .scrollPosition(id: $selection)
            .scrollTargetBehavior(.viewAligned)
            .scrollIndicators(.hidden)
        }
    }
}
