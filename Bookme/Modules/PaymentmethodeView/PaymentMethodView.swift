import SwiftUI

struct PaymentMethodView: View {
    @StateObject var paymentmethodeViewModel = PaymentmethodeViewModel()
    @Environment(RouterManager.self) var routerManager: RouterManager
    @Environment(\.dismiss) var dismiss
    @EnvironmentObject private var appState: AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            
                HStack {
                    
                    Button(action: {
                        self.presentationMode.wrappedValue.dismiss()
                    }, label: {
                        Image("img_arrowleft_white_a700")
                            .resizable()
                            .scaledToFit()
                            .frame(width: getRelativeWidth(18.0),
                                   height: getRelativeHeight(18.0), alignment: .center)
                            
                         
                    })
                    
                    Text("Payment Method")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .fixedSize()
                        .multilineTextAlignment(.leading)
                        .frame(
                               height: getRelativeHeight(22.0), alignment: .topLeading)
                        .padding(.leading, getRelativeWidth(15.0))
                       
                }
                .padding(.leading, getRelativeWidth(16.0))
                .frame(maxWidth: .infinity, alignment: .leading)
               
            ScrollView {
                VStack{
                
                    VStack(alignment:.leading) {
                        Text("Credit & Debit Card")
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(16.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(151.0), height: getRelativeHeight(22.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(32.0))
                            .padding(.bottom, getRelativeHeight(8.0))
                           
                       
                        .padding(.horizontal, getRelativeWidth(8.0))
                        
                        Button {
                            routerManager.push(to: .userPaymentCard(type: .myAccountRoute), where: .myAccountRoute)
                        } label: {
                            HStack {
                                HStack {
                                    Image("img_creditcard_cyan_800")
                                        .renderingMode(.template)
                                        .resizable()
                                        .foregroundStyle(Color(red: 0.01, green: 0.76, blue: 0.8))
                                        .scaledToFit()
                                        .frame(width: getRelativeWidth(22.0), height: getRelativeHeight(22.0),
                                               alignment: .center)
                                        
                                        .clipped()
                                    Text("Add New Card")
                                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(Color(red: 0.01, green: 0.76, blue: 0.8))
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(94.0), height: getRelativeHeight(20.0),
                                               alignment: .topLeading)
                                        .padding(.leading, getRelativeWidth(8.0))
                                }
                                .frame(width: getRelativeWidth(125.0), height: getRelativeHeight(20.0),
                                       alignment: .top)
                                .padding(.top, getRelativeHeight(13.0))
                                .padding(.bottom, getRelativeHeight(16.0))
                                .padding(.leading, getRelativeWidth(10.0))
                                Spacer()
//                                
//                                Text(StringConstants.kLblLink)
//                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
//                                    .fontWeight(.bold)
//                                    .foregroundColor(ColorConstants.Cyan800)
//                                    .minimumScaleFactor(0.5)
//                                    .multilineTextAlignment(.leading)
//                                    .frame(width: getRelativeWidth(28.0), height: getRelativeHeight(20.0),
//                                           alignment: .topLeading)
//                                    .padding(.top, getRelativeHeight(14.0))
//                                    .padding(.bottom, getRelativeHeight(16.0))
//                                    .padding(.trailing, getRelativeWidth(13.0))
                                
                               
                            }
                        
                            .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                    bottomRight: 13.0)
                                    .stroke(Color(red: 0, green: 0.56, blue: 0.59),
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                       bottomRight: 13.0)
                                    .fill(ColorConstants.Cyan8003f1))
                        }

                       
                       
                    }
                    
                    .padding(.top, getRelativeHeight(18.0))
                    .padding(.horizontal, getRelativeWidth(18.0))
                    
                    
                    
//                    VStack(alignment: .leading, spacing: 0) {
//                        Text(StringConstants.kMsgMorePaymentOp)
//                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(16.0)))
//                            .fontWeight(.medium)
//                            .foregroundColor(ColorConstants.WhiteA700)
//                            .minimumScaleFactor(0.5)
//                            .multilineTextAlignment(.leading)
//                           
//                            .padding(.horizontal, getRelativeWidth(22.0))
//                            .padding(.horizontal, getRelativeWidth(8.0))
//                        
//                        VStack{
//                           
//                             
//                            HStack{
//                                HStack {
//                                    Image("img_paypal")
//                                        .resizable()
//                                        .scaledToFit()
//                                        .frame(width: getRelativeWidth(26.0), height: getRelativeHeight(26.0),
//                                               alignment: .leading)
//                                    Text(StringConstants.kLblPaypal)
//                                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Cyan8007f)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(94.0), height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.leading, getRelativeWidth(8.0))
//                                }
//                                
//                                .padding(.top, getRelativeHeight(13.0))
////                                .padding(.bottom, getRelativeHeight(16.0))
//                                .padding(.leading, getRelativeWidth(10.0))
//                                Spacer()
//                                Button(action: {
//                                    
//                                }, label: {
//                                    Text(StringConstants.kLblLink)
//                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.bold)
//                                        .foregroundColor(ColorConstants.Cyan800)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(28.0), height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(14.0))
//                                        .padding(.bottom, getRelativeHeight(16.0))
//                                        .padding(.trailing, getRelativeWidth(13.0))
//                                })
//                            }
//                            
//                            Divider()
//                                .background(ColorConstants.Cyan800)
//                            
//                            
//                            HStack{
//                                HStack {
//                                    Image("img_apple")
//                                        .resizable()
//                                        .scaledToFit()
//                                        .frame(width: getRelativeWidth(26.0), height: getRelativeHeight(26.0),
//                                               alignment: .leading)
//                                    Text(StringConstants.kLblApplePay)
//                                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.medium)
//                                        .foregroundColor(ColorConstants.Cyan8007f)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(94.0), height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.leading, getRelativeWidth(8.0))
//                                }
//                                
////                                .padding(.top, getRelativeHeight(13.0))
////                                .padding(.bottom, getRelativeHeight(16.0))
//                                .padding(.leading, getRelativeWidth(10.0))
//                                Spacer()
//                                Button(action: {
//                                    
//                                }, label: {
//                                    Text(StringConstants.kLblLink)
//                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
//                                        .fontWeight(.bold)
//                                        .foregroundColor(ColorConstants.Cyan800)
//                                        .minimumScaleFactor(0.5)
//                                        .multilineTextAlignment(.leading)
//                                        .frame(width: getRelativeWidth(28.0), height: getRelativeHeight(20.0),
//                                               alignment: .topLeading)
//                                        .padding(.top, getRelativeHeight(14.0))
//                                        .padding(.bottom, getRelativeHeight(16.0))
//                                        .padding(.trailing, getRelativeWidth(13.0))
//                                })
//                            }
//                            
//                        
//                        }
//
//                        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                                bottomRight: 13.0)
//                            .stroke(ColorConstants.Cyan800, lineWidth: 1))
//                        .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
//                                                   bottomRight: 13.0).fill(ColorConstants.Cyan8003f1))
//                        .frame(maxWidth: .infinity)
//                        .hideNavigationBar()
//                      
//                        .padding(.top, getRelativeHeight(17.0))
//                        .padding(.horizontal, getRelativeWidth(22.0))
//                    }
//                   
//                    .padding(.top, getRelativeHeight(18.0))
                    
                    
                }
            }
            
            
          
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(ColorConstants.bgGradient)
        .hideNavigationBar()
    }
}

struct PaymentmethodeView_Previews: PreviewProvider {
    static var previews: some View {
        PaymentMethodView()
    }
}
