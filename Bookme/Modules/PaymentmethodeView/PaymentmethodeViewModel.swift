import Foundation
import SwiftUI

class PaymentmethodeViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var userprofileCurrentPage: Int = 0
    
    @Published var checkoutSuccessModel: CheckoutSuccessModel?
    @Published var sliderData: [TutorialItem] = [
        TutorialItem(index: 0), TutorialItem(index: 1)
    ]
    
    struct TutorialItem: Identifiable {
        let id: UUID = .init()
        let index: Int
    }
    
    @Published var selectedPaymentMethod: PaymentMethodModel?
    
    let paymentMethodArray: [PaymentMethodModel] = [
        .init(title: "Apple", image: .appleBlackLogo),
        .init(title: "PayPal", image: .paypalLogo)
    ]
    
    func updateSelectedPaymentMethod(_ value: PaymentMethodModel?) {
        selectedPaymentMethod = value
    }
    
    let additionalNote: String?
    let couponCode: String?
    init(additionalNote: String? = nil, couponCode: String? = nil) {
        self.additionalNote = additionalNote
        self.couponCode = couponCode
//        self.bookingRequestModelList = bookingRequestModelList
        super.init()
        updateSelectedPaymentMethod(paymentMethodArray.first)
    }
    
    func getBookingDetails(model: PaymentGatewayModel, completion: @escaping (BookingAppointmentModel) -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        
        let parameters: [String: Any] = [
            "user_id": userID,
            "bookid": model.bookid
        ]
        onApiCall(api.bookingDetails, parameters: parameters) {
            if let model = $0.data?.first {
                completion(model)
            }
        }
    }
    
}

extension PaymentmethodeViewModel: RoutableProtocol {
    nonisolated static func == (lhs: PaymentmethodeViewModel, rhs: PaymentmethodeViewModel) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    nonisolated func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }
}

struct Event: Identifiable {
    let id: UUID = .init()
    let title: String
    let startDate: Date
    let endDate: Date
    let note: String
    let location: String
    let alertOffset: TimeInterval?
}
