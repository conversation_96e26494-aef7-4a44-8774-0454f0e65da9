//
//  RowellipseCell.swift
//  Bookme
//
//  Created by Apple on 14/09/2024.
//

import SwiftUI

struct RowellipseCell: View {
    let model: PaymentMethodModel
    let isSelected:Bool
    var body: some View {
        HStack {
            ZStack {}
                .hideNavigationBar()
                .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(26.0),
                       alignment: .leading)
                
                .overlay(Circle()
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
               
                .background(Circle()
                            
                    .fill(isSelected ?  ColorConstants.Cyan800  : ColorConstants.WhiteA700) .padding(4))
            Text(model.title)
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(14.0)))
                .fontWeight(.semibold)
                .foregroundColor(ColorConstants.Black900)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(68.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.leading, getRelativeWidth(10.0))
            Image(model.image)
                .resizable()
                .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(24.0),
                       alignment: .leading)
                .scaledToFit()
                .padding(.leading, getRelativeWidth(191.0))
        }
       
        .padding(8)
        .overlay(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0, bottomRight: 6.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 6.0, topRight: 6.0, bottomLeft: 6.0, bottomRight: 6.0)
            .fill(Color.clear.opacity(0.7)))
        .hideNavigationBar()
    }
}



struct PaymentMethodModel: Identifiable, Equatable {
    let id: UUID = .init()
    let title: String
    let image: ImageResource
}
