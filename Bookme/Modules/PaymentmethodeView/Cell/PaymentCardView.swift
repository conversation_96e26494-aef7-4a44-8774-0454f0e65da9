//
//  PaymentCardView.swift
//  Bookme
//
//  Created by Apple on 14/09/2024.
//

import SwiftUI

struct PaymentCardView: View {
    var model: PaymentCardModel.CustomerCard?

    var body: some View {
        if let model = model {
            HStack {
                VStack(alignment: .leading, spacing: 0) {
                    Image("img_mastercard")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 60, height: 60, alignment: .center)
                        .padding(.bottom)
                        .clipped()
                        .frame(maxWidth: .infinity, alignment: .topTrailing)

                    VStack {
                        HStack(alignment: .center, spacing: 32.relativeWidth) { // Spacing between chunks
                            ForEach(splitCardNumber(model.number), id: \.self) { chunk in
                                Text(chunk)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .multilineTextAlignment(.leading)
                            }
                        }
                        .frame(maxWidth: .infinity, alignment: .center)
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 8)
                    Spacer()
                }
                .padding()
            }
            .frame(width: getRelativeWidth(338.0), height: getRelativeHeight(187.0),
                   alignment: .center)
            .background(alignment: .topLeading, content: {
                Image("img_ellipse_175")
                    .resizable()
                    .scaledToFill()
                    .frame(width: getRelativeWidth(165.0),
                           height: getRelativeHeight(108.0), alignment: .center)
                    .clipped()
            })
            .background(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                       bottomRight: 10.0)
                        .fill(ColorConstants.Cyan800))
        } else {
            EmptyView()
        }
    }

    /// Splits the card number into chunks of 4 characters
    func splitCardNumber(_ cardNumber: String) -> [String] {
        let cleanNumber = cardNumber.replacingOccurrences(of: " ", with: "") // Remove any existing spaces
        return stride(from: 0, to: cleanNumber.count, by: 4).map {
            let startIndex = cleanNumber.index(cleanNumber.startIndex, offsetBy: $0)
            let endIndex = cleanNumber.index(startIndex, offsetBy: 4, limitedBy: cleanNumber.endIndex) ?? cleanNumber.endIndex
            return String(cleanNumber[startIndex..<endIndex])
        }
    }
}

#Preview {
    PaymentCardView(model: .init(brand: "MASTERCARD", number: "511111xxxxxx1118", scheme: "MASTERCARD", token: "9146011827895007"))
}
