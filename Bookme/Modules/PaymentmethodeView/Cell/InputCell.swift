import SwiftUI

struct InputCell: View {
    var body: some View {
        HStack {
            VStack{
               
                 
                HStack{
                    HStack {
                        Image("img_paypal")
                            .resizable()
                            .scaledToFit()
                            .frame(width: getRelativeWidth(26.0), height: getRelativeHeight(26.0),
                                   alignment: .leading)
                        Text(StringConstants.kLblPaypal)
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.Cyan8007f)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(94.0), height: getRelativeHeight(20.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(8.0))
                    }
                    .frame(width: getRelativeWidth(125.0), height: getRelativeHeight(20.0),
                           alignment: .top)
                    .padding(.top, getRelativeHeight(13.0))
                    .padding(.bottom, getRelativeHeight(16.0))
                    .padding(.leading, getRelativeWidth(10.0))
                    Spacer()
                    Text(StringConstants.kLblLink)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(28.0), height: getRelativeHeight(20.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(14.0))
                        .padding(.bottom, getRelativeHeight(16.0))
                        .padding(.trailing, getRelativeWidth(13.0))
                }
                
                Divider()
                    .background(ColorConstants.Cyan800)
                
                
                HStack{
                    HStack {
                        Image("img_apple")
                            .resizable()
                            .scaledToFit()
                            .frame(width: getRelativeWidth(26.0), height: getRelativeHeight(26.0),
                                   alignment: .leading)
                        Text(StringConstants.kLblApplePay)
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.Cyan8007f)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(94.0), height: getRelativeHeight(20.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(8.0))
                    }
                    .frame(width: getRelativeWidth(125.0), height: getRelativeHeight(20.0),
                           alignment: .top)
                    .padding(.top, getRelativeHeight(13.0))
                    .padding(.bottom, getRelativeHeight(16.0))
                    .padding(.leading, getRelativeWidth(10.0))
                    Spacer()
                    Text(StringConstants.kLblLink)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(28.0), height: getRelativeHeight(20.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(14.0))
                        .padding(.bottom, getRelativeHeight(16.0))
                        .padding(.trailing, getRelativeWidth(13.0))
                }
                
            
            }
        }
        
//        .frame(width: getRelativeWidth(319.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                bottomRight: 13.0)
            .stroke(ColorConstants.Cyan800, lineWidth: 1))
        .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                   bottomRight: 13.0).fill(ColorConstants.Cyan8003f1))
        .frame(maxWidth: .infinity)
        .hideNavigationBar()
    }
}

struct InputCell_Previews: PreviewProvider {
    static var previews: some View {
        InputCell()
    }
}
