//
//  AddCardView.swift
//  Bookme
//
//  Created by Apple on 14/09/2024.
//

import SwiftUI

struct AddCardView: View {
    let routesType: RoutesType
    @StateObject var addcardViewModel = AddCardViewModel()
    @Environment(\.dismiss) var dismiss: DismissAction
    var body: some View {
        MainScrollBody(backButtonWithTitle: "Add New Card") {
            VStack {
                PaymentCardView()
                VStack(alignment: .leading, spacing: 0) {
                    Text("Card Holder Name")
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(118.0), height: getRelativeHeight(18.0),
                               alignment: .topLeading)
                    HStack {
                        TextField(StringConstants.kLbl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                                  text: $addcardViewModel.nameText)
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                            .foregroundColor(ColorConstants.Gray500)
                            .padding()
                    }
                    .frame(width: getRelativeWidth(321.0), height: getRelativeHeight(50.0),
                           alignment: .leading)
                    .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                            bottomRight: 13.0)
                            .stroke(ColorConstants.Cyan800,
                                    lineWidth: 1))
                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                               bottomRight: 13.0)
                            .fill(ColorConstants.WhiteA7003f))
                    .padding(.top, getRelativeHeight(7.0))
                    .padding(.trailing, getRelativeWidth(11.0))
                }
                .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(75.0),
                       alignment: .center)
                .padding(.top, getRelativeHeight(25.0))
                VStack(alignment: .leading, spacing: 0) {
                    Text("Card Number")
                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                        .fontWeight(.medium)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(84.0), height: getRelativeHeight(18.0),
                               alignment: .topLeading)
                    HStack {
                        TextField(StringConstants.kMsg459534645,
                                  text: $addcardViewModel.cardnumberText)
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                            .foregroundColor(ColorConstants.Gray500)
                            .padding()
                        Image("img_mastercard")
                            .resizable()
                            .frame(width: getRelativeWidth(50.0),
                                   height: getRelativeWidth(50.0), alignment: .center)
                            .scaledToFit()
                            .clipped()
                            .padding(.leading, getRelativeWidth(30.0))
                        Spacer()
                    }
                    .frame(width: getRelativeWidth(321.0), height: getRelativeHeight(50.0),
                           alignment: .leading)
                    .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                            bottomRight: 13.0)
                            .stroke(ColorConstants.Cyan800,
                                    lineWidth: 1))
                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                               bottomRight: 13.0)
                            .fill(ColorConstants.WhiteA7003f))
                    .padding(.top, getRelativeHeight(7.0))
                    .padding(.trailing, getRelativeWidth(11.0))
                }
                .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(75.0),
                       alignment: .center)
                .padding(.top, getRelativeHeight(20.0))
                HStack {
                    VStack {
                        HStack {
                            Text(StringConstants.kLblExpiryDate)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(74.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                            Spacer()
                            Text(StringConstants.kLblMmYy)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(11.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Gray50001)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(45.0),
                                       height: getRelativeHeight(16.0), alignment: .topLeading)
                                .padding(.leading, getRelativeWidth(21.0))
                        }
                        .frame(width: getRelativeWidth(141.0), height: getRelativeHeight(18.0),
                               alignment: .center)
                        HStack {
                            TextField(StringConstants.kLbl062026,
                                      text: $addcardViewModel.expirydateText)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                                .foregroundColor(ColorConstants.Gray500)
                                .padding()
                        }
                        .frame(width: getRelativeWidth(146.0), height: getRelativeHeight(50.0),
                               alignment: .center)
                        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                bottomRight: 13.0)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 13.0, topRight: 13.0,
                                                   bottomLeft: 13.0, bottomRight: 13.0)
                                .fill(ColorConstants.WhiteA7003f))
                        .padding(.top, getRelativeHeight(6.0))
                    }
                    .frame(width: getRelativeWidth(146.0), height: getRelativeHeight(74.0),
                           alignment: .center)
                    Spacer()
                    VStack(alignment: .leading, spacing: 0) {
                        HStack {
                            Text(StringConstants.kLblCvv)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(28.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                            Image("img_info")
                                .resizable()
                                .frame(width: getRelativeWidth(20.0),
                                       height: getRelativeWidth(20.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                                .padding(.leading, getRelativeWidth(86.0))
                        }
                        .frame(width: getRelativeWidth(135.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                        HStack {
                            SecureField(StringConstants.kLbl3, text: $addcardViewModel.cvvText)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                                .foregroundColor(ColorConstants.Gray500)
                                .padding()
                        }
                        .frame(width: getRelativeWidth(146.0), height: getRelativeHeight(50.0),
                               alignment: .leading)
                        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                bottomRight: 13.0)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 13.0, topRight: 13.0,
                                                   bottomLeft: 13.0, bottomRight: 13.0)
                                .fill(ColorConstants.WhiteA7003f))
                        .padding(.top, getRelativeHeight(7.0))
                    }
                    .frame(width: getRelativeWidth(146.0), height: getRelativeHeight(78.0),
                           alignment: .center)
                    .padding(.leading, getRelativeWidth(24.0))
                }
                .frame(width: getRelativeWidth(316.0), height: getRelativeHeight(78.0),
                       alignment: .center)
                .padding(.vertical, getRelativeHeight(17.0))
                .padding(.leading, getRelativeWidth(29.0))
                .padding(.trailing, getRelativeWidth(45.0))
            }.padding(.vertical)
        }
        .safeAreaInset(edge: .bottom, content: {
            Button(action: {
                dismiss()
            }, label: {
                HStack(spacing: 0) {
                    Text(StringConstants.kLblAddCard)
                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.heavy)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(13.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(310.0), height: getRelativeHeight(47.0),
                               alignment: .center)
                        .background(RoundedCorners(topLeft: 23.0, topRight: 23.0,
                                                   bottomLeft: 23.0, bottomRight: 23.0)
                                .fill(ColorConstants.Cyan800))
                        .shadow(color: ColorConstants.Black90044, radius: 2, x: 0, y: 1)
                        .padding(.bottom, getRelativeHeight(23.0))
                        .padding(.horizontal, getRelativeWidth(39.0))
                }
            })
            .padding(.bottom, AppConstants.tabBarHeight.relativeHeight)
        })
        .background(ColorConstants.WhiteA700)
    }
}

#Preview {
    NavigationStack {
        AddCardView(routesType: .appointmentRoute)
    }.attachAllEnvironmentObjects()
}
