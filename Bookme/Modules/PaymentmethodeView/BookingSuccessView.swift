//
//  BookingSuccessView.swift
//  Bookme
//
//  Created by Apple on 17/09/2024.
//

import EventKit
import SwiftUI

struct BookingSuccessView: View {
    let routesType: RoutesType
    let model: PaymentGatewayModel
    @StateObject var viewModel: PaymentmethodeViewModel = .init()
    @State private var isEventTriggered: Bool = false
    @State private var isEventAdded: Bool = false
    @Environment(RouterManager.self) var routerManager: RouterManager
    @Environment(\.dismiss) var dismiss
    @EnvironmentObject private var appState: AppState
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(invertColor: false, backButtonWithTitle: "", enableScroll: false) {
                
                    VStack {
                        VStack(spacing: 16.0.relativeHeight) {
                            Image(.verifiedCheckmark)
                                .padding(.bottom, 32.0.relativeHeight)
                                    
                            Text("Booking Confirmed")
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(24.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.Cyan800)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, getRelativeWidth(42.0))
                            Text("Your appointment is confirmed!\nWe look forward to seeing you.")
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Black900)
                                .multilineTextAlignment(.center)
                        }
                                
                        .padding(.top, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                        
                        HStack(alignment: .top, spacing: 12.0.relativeWidth) {
                            Image(.addToCalendar)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 26, height: 26)
                                .clipped()
                            
                            VStack(alignment: .leading, spacing: 0.0) {
                                Text("Date")
                                   
                                Text(model.serviceDateFormatted)
                                    .fontWeight(.bold)
                                   
                                Text(model.serviceTimeFormatted)
                                    .fontWeight(.bold)
                                   
                                Button { isEventTriggered = true } label: {
                                    Text(isEventAdded ? "Added" : "Add to Calendar")
                                        .fontWeight(.bold)
                                        .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                                }
                                .disableWithOpacity(isEventAdded)
                            }
                            .addToCalendar(isTriggered: $isEventTriggered, event: appState.event, onCompletion: { success, _ in
                                if success {
                                    viewModel.updatePageState(.message(config: .init(title: "Success", text: "Calendar event added successfully")))
                                    isEventAdded = true
                                }
                            })
                            .multilineTextAlignment(.center)
                            .kerning(0.52)
                            .font(Font.custom("Nunito", size: 14))
                            .foregroundColor(.black)
                        }
                        .padding(.top, 12.0.relativeHeight)
                        
                        VStack {
                            Button(action: {
                                
                                    viewModel.getBookingDetails(model: model, completion: { model in
                                        routerManager.push(to: .eReceipt(model: model), where: routesType)
                                    })
                                
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("VIEW RECEIPT")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .multilineTextAlignment(.center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            })
                                    
                            Button(action: {
                                appState.updateSelectedTab(.home)
                                routerManager.popToRoot(where: routesType)
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("BACK TO HOME")
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(12.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .multilineTextAlignment(.center)
                                        .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                                        .padding(.leading, getRelativeWidth(10.0))
                                }
                            })
                        }
                                
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(22.0))
                    }
                    .padding(.top, 64.0.relativeHeight)
                    .padding(.bottom, 32.0.relativeHeight)
                
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        }
        
        .background(ColorConstants.WhiteA700)
    }
}

//#Preview {
//    NavigationStack {
//        BookingSuccessView(routesType: .myAccountRoute, viewModel: .init(additionalNote: "")).attachAllEnvironmentObjects()
//    }
//}

struct AddEventToCalendar: ViewModifier {
    @State private var eventStore = EKEventStore()
    @Binding var isEventTriggered: Bool
    @State private var showCalendarAlert = false
    
    let event: Event?
    let onCompletion: (_ success: Bool, _ error: NSError?) -> Void
    
    func body(content: Content) -> some View {
        content
            .onChange(of: isEventTriggered) { _, newValue in
                if newValue {
                    checkCalendarAuthorizationStatus()
                }
            }
            .alert("Access to Calendar was denied. Please enable it in Settings > Privacy > Calendar", isPresented: $showCalendarAlert) {
                Button("Settings") {
                    if let url = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(url)
                    }
                }
                Button("Cancel", role: .cancel) {}
            }
    }
    
    private func checkCalendarAuthorizationStatus() {
        switch EKEventStore.authorizationStatus(for: .event) {
        case .authorized:
            addEventToCalendar()
        case .notDetermined:
            requestCalendarAccess()
        case .denied, .restricted:
            showCalendarAlert = true
            isEventTriggered = false
        case .fullAccess:
            addEventToCalendar()
        case .writeOnly:
            addEventToCalendar()
        @unknown default:
            showCalendarAlert = true
            isEventTriggered = false
        }
    }
    
    private func requestCalendarAccess() {
        eventStore.requestWriteOnlyAccessToEvents { granted, error in
            DispatchQueue.main.async {
                if granted && error == nil {
                    addEventToCalendar()
                } else {
                    showCalendarAlert = true
                }
                isEventTriggered = false
            }
        }
    }
    
    private func addEventToCalendar() {
        guard let currentEvent = self.event else { return }
        
        let alertOffset: TimeInterval
        
        let event = EKEvent(eventStore: eventStore)
        event.title = currentEvent.title
        event.startDate = currentEvent.startDate
        event.endDate = currentEvent.endDate
        event.location = currentEvent.location
        event.notes = currentEvent.note
       
        event.calendar = eventStore.defaultCalendarForNewEvents
        
        // Add alert
        if let offset = currentEvent.alertOffset {
            let alarm = EKAlarm(relativeOffset: offset) // offset in seconds
            event.addAlarm(alarm)
        }
        
        do {
            print(currentEvent)
            try eventStore.save(event, span: .thisEvent)
            print("Event added successfully!")
            onCompletion(true, nil)
        } catch let error as NSError {
            onCompletion(false, error)
            print("Error saving event: \(error)")
        }
    }
}

extension View {
    func addToCalendar(
        isTriggered: Binding<Bool>,
        event: Event?,
        onCompletion: @escaping (_ success: Bool, _ error: NSError?) -> Void
    ) -> some View {
        modifier(AddEventToCalendar(
            isEventTriggered: isTriggered,
            event: event,
            onCompletion: onCompletion
        ))
    }
}
