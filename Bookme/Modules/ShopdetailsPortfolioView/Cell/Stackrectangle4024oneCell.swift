import SwiftUI

struct Stackrectangle4024oneCell: View {
let model: VendorDetailsModel.Portfolio
    var body: some View {
        ZStack(alignment: .bottomLeading) {
            NetworkImageView(path: model.photoUrl, contentMode: .fill)
                .frame(width: getRelativeWidth(337.0), height: getRelativeHeight(195.0),
                       alignment: .center)
                .clipShape(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                           bottomRight: 10.0))
//            HStack {
//                Button(action: {}, label: {
//                    Image("img_arrowdown_white_a700")
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: getRelativeWidth(24.0), height: getRelativeWidth(24.0),
//                               alignment: .leading)
//                       
//                })
//                    .padding(.leading, getRelativeWidth(22.0))
//                
//                Spacer()
//                HStack {
//                    Image("img_union")
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: getRelativeWidth(18.0), height: getRelativeHeight(18.0),
//                               alignment: .leading)
//                       
//                    Text(StringConstants.kLbl0)
//                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
//                        .fontWeight(.regular)
//                        .foregroundColor(ColorConstants.WhiteA700)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.leading)
//                        .frame( height: getRelativeHeight(18.0),
//                               alignment: .leading)
//                        .padding(.leading, getRelativeWidth(4.0))
//                    Image("img_vector15")
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: getRelativeWidth(18.0), height: getRelativeHeight(18.0),
//                               alignment: .leading)
//                       
//                        .padding(.leading, getRelativeWidth(25.0))
//                    Text("\(model.likes)")
//                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
//                        .fontWeight(.regular)
//                        .foregroundColor(ColorConstants.WhiteA700)
//                        .minimumScaleFactor(0.5)
//                        .multilineTextAlignment(.leading)
//                        .frame( height: getRelativeHeight(18.0),
//                               alignment: .leading)
////                        .padding(.leading, getRelativeWidth(5.0))
//                }
//               
//              
//                .padding(.trailing, getRelativeWidth(23.0))
//            }
//            .frame(width: getRelativeWidth(337.0), height: getRelativeHeight(38.0),
//                   alignment: .leading)
//            .background(RoundedCorners(bottomLeft: 10.0, bottomRight: 10.0)
//                .fill(ColorConstants.Cyan8007f))
//            .padding(.top, getRelativeHeight(157.0))
        }
        .frame(width: getRelativeWidth(337.0), alignment: .center)
        .background(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                   bottomRight: 10.0)
                .fill(ColorConstants.Bluegray100))
    }
}

//struct Stackrectangle4024oneCell_Previews: PreviewProvider {
//    static var previews: some View {
//        Stackrectangle4024oneCell()
//    }
//}
