import SwiftUI

struct ShopdetailsPortfolioView: View {
    @StateObject var shopdetailsPortfolioViewModel = ShopdetailsPortfolioViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var shopDetailsViewModel: ShopDetailsViewModel
    var body: some View {
       
        if let vendorDetailsModel = shopDetailsViewModel.vendorDetailsModel{
            VStack(alignment: .leading, spacing: 0) {
                VStack(spacing: 0) {
    //                            ScrollView(.vertical, showsIndicators: false) {
                    LazyVStack(spacing: 20.0.relativeHeight) {
                        ForEach(vendorDetailsModel.portfolio) {
                            Stackrectangle4024oneCell(model: $0)
                        }
                    }
    //                            }
                }
                .frame(width: getRelativeWidth(339.0), alignment: .center)
                .padding(.top, getRelativeHeight(20.0))
                .padding(.horizontal, getRelativeWidth(24.0))
                           
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        }
    }
}

struct ShopdetailsPortfolioView_Previews: PreviewProvider {
    static var previews: some View {
        ShopdetailsPortfolioView()
    }
}
