import SwiftUI

struct SelectionView: View {
    @StateObject var selectionViewModel = SelectionViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var appState:AppState
    
    var body: some View {
        
        SuperView(pageState: $selectionViewModel.pageState) {
            VStack(alignment: .leading, spacing: 0) {
                ZStack(alignment: .leading) {
                    Image("img_76021231")
                        .resizable()
                        .frame(width: getRelativeWidth(290.0), height: getRelativeHeight(282.0),
                               alignment: .center)
                        .scaledToFit()
                        .clipped()
                        .padding(.horizontal, getRelativeWidth(22.0))
                    
                    ZStack {}
                      
                        .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(2.0),
                               alignment: .center)
                        .background(RoundedCorners(topLeft: 174.5, topRight: 174.5, bottomLeft: 174.5,
                                                   bottomRight: 174.5)
                                .fill(ColorConstants.Cyan800))
                        .padding(.top, getRelativeHeight(282.22))
                }
                .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(282.0),
                       alignment: .center)
                .padding(.horizontal, getRelativeWidth(15.0))
                VStack {
                   
                    Text(StringConstants.kMsgBookMeLetsYo)
                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(28.0)))
                        .fontWeight(.heavy)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(316.0), height: getRelativeHeight(106.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(78.0))
                        .padding(.leading, getRelativeWidth(21.0))
                        .padding(.trailing, getRelativeWidth(12.0))
                    Text(StringConstants.kMsgShowMeService)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(16.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(166.0), height: getRelativeHeight(22.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(32.0))
                        .padding(.horizontal, getRelativeWidth(21.0))
                  
                    
                    
                    VStack(spacing:getRelativeHeight(10)){
                       
                        
                        
                        ForEach(ServiceType.allCases) { type in
                            Button(action: {
                                updateSelection(type)
                            }, label: {
                                HStack(spacing: 0) {
                                    Text(LocalizedStringKey(type.title))
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(15.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(310.0), height: getRelativeHeight(50.0),
                                               alignment: .center)
                                        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0,
                                                                   bottomLeft: 25.0, bottomRight: 25.0)
                                                .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                        .padding(.top, getRelativeHeight(65.0))
                                        .padding(.leading, getRelativeWidth(21.0))
                                        .padding(.trailing, getRelativeWidth(17.0))
                                }
                            })
                        }
                        
                        
                       
                     
                       
                       
                    }
                   
                }
                .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(477.0),
                       alignment: .center)
                .padding(.horizontal, getRelativeWidth(15.0))
            }
            .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(820.0))
            .background(ColorConstants.bgGradient)
            .hideNavigationBar()
        }
        
       
    }
    
    
    func updateSelection(_ type: ServiceType) {
        selectionViewModel.updatePageState(.loading(true))
        appState.updateServiceType([type])
        Utilities.enQueue(after: .now() + 1.0) {
            selectionViewModel.updatePageState(.stable)
            appState.updateInitialScreen(.dashboard)
        }
        
       
        
    }
}

struct SelectionView_Previews: PreviewProvider {
    static var previews: some View {
        SelectionView().attachAllEnvironmentObjects()
    }
}
