import SwiftUI

struct AppoinmentcompleteView: View {
    @StateObject var appoinmentcompleteViewModel = AppoinmentcompleteViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack(spacing: 0) {
            ScrollView(.vertical, showsIndicators: false) {
                LazyVStack {
                    ForEach(0 ... 1, id: \.self) { index in
                        Appoinmentitem1Cell()
                    }
                }
            }
        }
        .frame(width: UIScreen.main.bounds.width, alignment: .leading)
    }
}

struct AppoinmentcompleteView_Previews: PreviewProvider {
    static var previews: some View {
        AppoinmentcompleteView()
    }
}
