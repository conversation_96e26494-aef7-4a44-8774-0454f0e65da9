import SwiftUI

struct Appoinmentitem1Cell: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(StringConstants.kMsgNov222023)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(121.0), height: getRelativeHeight(17.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(15.0))
                .padding(.horizontal, getRelativeWidth(17.0))
            Divider()
                .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, getRelativeHeight(10.0))
                .padding(.horizontal, getRelativeWidth(14.0))
            ZStack(alignment: .topTrailing) {
                Image("img_rectangle10")
                    .resizable()
                    .frame(width: getRelativeWidth(72.0), height: getRelativeWidth(74.0),
                           alignment: .leading)
                    .scaledToFit()
                    .clipShape(Circle())
                    .clipShape(Circle())
                    .padding(.bottom, getRelativeHeight(67.35))
                    .padding(.trailing, getRelativeWidth(256.0))
                VStack(alignment: .leading, spacing: 0) {
                    Text(StringConstants.kMsgBroadwayBarber)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(174.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                        .padding(.trailing)
                    Text(StringConstants.kMsg4thFloorAlZ)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900B2)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(33.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(56.0),
                       alignment: .leading)
                .padding(.bottom, getRelativeHeight(90.0))
                .padding(.leading, getRelativeWidth(89.87))
                Button(action: {}, label: {
                    HStack(spacing: 0) {
                        Text(StringConstants.kLblAddReview)
                            .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.semibold)
                            .padding(.horizontal, getRelativeWidth(10.0))
                            .padding(.vertical, getRelativeHeight(4.0))
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(85.0), height: getRelativeHeight(25.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                    bottomRight: 12.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 12.5, topRight: 12.5,
                                                       bottomLeft: 12.5, bottomRight: 12.5)
                                    .fill(ColorConstants.Cyan8003f))
                            .padding(.leading, getRelativeWidth(243.0))
                    }
                })
                .frame(width: getRelativeWidth(85.0), height: getRelativeHeight(25.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                        bottomRight: 12.5)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                           bottomRight: 12.5)
                        .fill(ColorConstants.Cyan8003f))
                .padding(.leading, getRelativeWidth(243.0))
                Text(StringConstants.kLbl6Km)
                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                    .fontWeight(.semibold)
                    .foregroundColor(ColorConstants.Black900B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(17.0),
                           alignment: .leading)
                    .padding(.leading, getRelativeWidth(107.5))
                    .padding(.trailing, getRelativeWidth(195.5))
                VStack {
                    ZStack(alignment: .center) {
                        Image("img_vector_cyan_800_11x9")
                            .resizable()
                            .frame(width: getRelativeWidth(7.0), height: getRelativeHeight(11.0),
                                   alignment: .leading)
                            .scaledToFit()
                        Image("img_vector_cyan_800_3x3")
                            .resizable()
                            .frame(width: getRelativeWidth(1.0), height: getRelativeWidth(3.0),
                                   alignment: .leading)
                            .scaledToFit()
                            .padding(.bottom, getRelativeHeight(4.83))
                            .padding(.horizontal, getRelativeWidth(3.17))
                    }
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(7.0), height: getRelativeHeight(11.0),
                           alignment: .leading)
                    .padding(.horizontal, getRelativeWidth(90.0))
                    Divider()
                        .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                               alignment: .leading)
                        .background(ColorConstants.Cyan8004c)
                        .padding(.top, getRelativeHeight(17.0))
                    HStack {
                        Button(action: {}, label: {
                            HStack(spacing: 0) {
                                Text(StringConstants.kLblReBook)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .padding(.horizontal, getRelativeWidth(30.0))
                                    .padding(.vertical, getRelativeHeight(7.0))
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(127.0),
                                           height: getRelativeHeight(35.0), alignment: .center)
                                    .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                            bottomLeft: 17.5, bottomRight: 17.5)
                                            .stroke(ColorConstants.Cyan800,
                                                    lineWidth: 1))
                                    .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                               bottomLeft: 17.5, bottomRight: 17.5)
                                            .fill(Color.clear.opacity(0.7)))
                            }
                        })
                        .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                               alignment: .center)
                        .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                bottomRight: 17.5)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                   bottomRight: 17.5)
                                .fill(Color.clear.opacity(0.7)))
                        Spacer()
                        Button(action: {}, label: {
                            HStack(spacing: 0) {
                                Text(StringConstants.kLblReceipt)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .padding(.horizontal, getRelativeWidth(30.0))
                                    .padding(.vertical, getRelativeHeight(7.0))
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(127.0),
                                           height: getRelativeHeight(35.0), alignment: .leading)
                                    .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                               bottomLeft: 17.5, bottomRight: 17.5)
                                            .fill(ColorConstants.Cyan800))
                            }
                        })
                        .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                               alignment: .leading)
                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                   bottomRight: 17.5)
                                .fill(ColorConstants.Cyan800))
                    }
                    .frame(width: getRelativeWidth(327.0), height: getRelativeHeight(35.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(17.0))
                }
                .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(82.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(64.04))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(146.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(13.0))
            .padding(.bottom, getRelativeHeight(16.0))
            .padding(.horizontal, getRelativeWidth(14.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0, bottomRight: 20.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                   bottomRight: 20.0)
                .fill(ColorConstants.WhiteA700))
        .hideNavigationBar()
    }
}

 struct Appoinmentitem1Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Appoinmentitem1Cell()
 }
 }
