import SwiftUI

struct Rowhairextension1Cell: View {
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 0) {
                Text(StringConstants.kLblHairExtension)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(93.0), height: getRelativeHeight(18.0),
                           alignment: .leading)
                Text(StringConstants.kLbl45Min)
                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(11.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Cyan8007f)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(35.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(18.0))
                    .padding(.trailing, getRelativeWidth(10.0))
            }
            .frame(width: getRelativeWidth(93.0), height: getRelativeHeight(52.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(14.0))
            VStack(alignment: .trailing, spacing: 0) {
                Text(StringConstants.kLblKd000)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(62.0), height: getRelativeHeight(21.0),
                           alignment: .leading)
                    .background(ColorConstants.Cyan8003f)
                    .padding(.leading)
                    .padding(.leading)
                ZStack {}
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(22.0), height: getRelativeWidth(24.0),
                           alignment: .leading)
                    .overlay(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                            bottomRight: 3.0)
                            .stroke(ColorConstants.Cyan8003f,
                                    lineWidth: 1))
                    .background(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                               bottomRight: 3.0)
                            .fill(ColorConstants.WhiteA700))
                    .padding(.top, getRelativeHeight(8.0))
                    .padding(.leading, getRelativeWidth(39.0))
            }
            .frame(width: getRelativeWidth(62.0), height: getRelativeHeight(53.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(197.0))
        }
        .frame(width: getRelativeWidth(371.0), alignment: .leading)
        .overlay(RoundedCorners().stroke(ColorConstants.Cyan90030, lineWidth: 1))
        .background(RoundedCorners().fill(ColorConstants.WhiteA700))
        .hideNavigationBar()
    }
}

/* struct Rowhairextension1Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Rowhairextension1Cell()
 }
 } */
