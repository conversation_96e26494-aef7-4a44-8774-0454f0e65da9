import SwiftUI

struct Shopdetails1View: View {
    @StateObject var shopdetails1ViewModel = Shopdetails1ViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        NavigationView {
            ScrollView(.vertical, showsIndicators: false) {
                VStack(alignment: .leading, spacing: 0) {
                    ZStack(alignment: .bottomLeading) {
                        VStack(alignment: .trailing, spacing: 0) {
                            Text(StringConstants.kLblKd000)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(64.0),
                                       height: getRelativeHeight(21.0), alignment: .topLeading)
                                .background(ColorConstants.Cyan8003f)
                                .padding(.leading)
                                .padding(.leading)
                                .padding(.leading)
                                .padding(.leading)
                            HStack {
                                Text(StringConstants.kLbl45Min)
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(11.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.Cyan8007f)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(37.0),
                                           height: getRelativeHeight(16.0), alignment: .topLeading)
                                    .padding(.vertical, getRelativeHeight(1.0))
                                Spacer()
                                Button(action: {}, label: {
                                    Image("img_tick")
                                })
                                .frame(width: getRelativeWidth(24.0),
                                       height: getRelativeWidth(24.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 3.0, topRight: 3.0,
                                                        bottomLeft: 3.0,
                                                        bottomRight: 3.0)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 3.0, topRight: 3.0,
                                                           bottomLeft: 3.0, bottomRight: 3.0)
                                        .fill(ColorConstants.WhiteA700))
                            }
                            .frame(width: getRelativeWidth(354.0), height: getRelativeHeight(24.0),
                                   alignment: .center)
                            .padding(.top, getRelativeHeight(11.0))
                            .padding(.bottom, getRelativeHeight(8.0))
                            .padding(.leading, getRelativeWidth(14.0))
                            .padding(.trailing, getRelativeWidth(5.0))
                        }
                        .frame(width: getRelativeWidth(373.0), height: getRelativeHeight(72.0),
                               alignment: .topLeading)
                        .overlay(RoundedCorners().stroke(ColorConstants.Cyan90030, lineWidth: 1))
                        .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                        .padding(.bottom, getRelativeHeight(80.0))
                        VStack {
                            Text(StringConstants.kMsgHairCutting)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(143.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                                .padding(.horizontal, getRelativeWidth(14.0))
                            VStack {
                                HStack {
                                    Text(StringConstants.kLblHairColoring)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(86.0),
                                               height: getRelativeHeight(18.0),
                                               alignment: .topLeading)
                                        .padding(.bottom, getRelativeHeight(6.0))
                                    Spacer()
                                    Text(StringConstants.kLblKd000)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                                        .fontWeight(.bold)
                                        .padding(.leading, getRelativeWidth(12.0))
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(64.0),
                                               height: getRelativeHeight(21.0),
                                               alignment: .topLeading)
                                        .background(ColorConstants.Cyan8003f)
                                }
                                .frame(width: getRelativeWidth(356.0),
                                       height: getRelativeHeight(24.0), alignment: .center)
                                .padding(.top, getRelativeHeight(4.0))
                                .padding(.leading, getRelativeWidth(14.0))
                                HStack {
                                    Text(StringConstants.kLbl45Min)
                                        .font(FontScheme
                                            .kNunitoRegular(size: getRelativeHeight(11.0)))
                                        .fontWeight(.regular)
                                        .foregroundColor(ColorConstants.Cyan8007f)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(37.0),
                                               height: getRelativeHeight(16.0),
                                               alignment: .topLeading)
                                        .padding(.bottom, getRelativeHeight(4.0))
                                    Spacer()
                                    ZStack {}
                                        .hideNavigationBar()
                                        .frame(width: getRelativeWidth(24.0),
                                               height: getRelativeWidth(24.0), alignment: .center)
                                        .overlay(RoundedCorners(topLeft: 3.0, topRight: 3.0,
                                                                bottomLeft: 3.0, bottomRight: 3.0)
                                                .stroke(ColorConstants.Cyan8003f,
                                                        lineWidth: 1))
                                        .background(RoundedCorners(topLeft: 3.0, topRight: 3.0,
                                                                   bottomLeft: 3.0,
                                                                   bottomRight: 3.0)
                                                .fill(ColorConstants.WhiteA700))
                                }
                                .frame(width: getRelativeWidth(354.0),
                                       height: getRelativeHeight(24.0), alignment: .center)
                                .padding(.top, getRelativeHeight(8.0))
                                .padding(.bottom, getRelativeHeight(11.0))
                                .padding(.leading, getRelativeWidth(14.0))
                                .padding(.trailing, getRelativeWidth(5.0))
                            }
                            .frame(width: getRelativeWidth(373.0), height: getRelativeHeight(72.0),
                                   alignment: .leading)
                            .overlay(RoundedCorners()
                                .stroke(ColorConstants.Cyan90030, lineWidth: 1))
                            .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                            .padding(.top, getRelativeHeight(52.0))
                        }
                        .frame(width: getRelativeWidth(373.0), height: getRelativeHeight(142.0),
                               alignment: .bottomLeading)
                        .padding(.top, getRelativeHeight(9.54))
                    }
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(373.0), height: getRelativeHeight(152.0),
                           alignment: .center)
                    .padding(.leading, getRelativeWidth(13.0))
                    .padding(.trailing, getRelativeWidth(13.0))
                    VStack(spacing: 0) {
                        ScrollView(.vertical, showsIndicators: false) {
                            LazyVStack {
                                ForEach(0 ... 1, id: \.self) { index in
                                    Rowhairextension1Cell()
                                }
                            }
                        }
                    }
                    .frame(width: getRelativeWidth(373.0), alignment: .center)
                    .padding(.top, getRelativeHeight(8.0))
                    .padding(.leading, getRelativeWidth(13.0))
                    .padding(.trailing, getRelativeWidth(4.0))
                    HStack {
                        VStack(alignment: .leading, spacing: 0) {
                            Text(StringConstants.kLbl03Service)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(72.0),
                                       height: getRelativeHeight(20.0), alignment: .topLeading)
                                .padding(.horizontal, getRelativeWidth(5.0))
                            Text(StringConstants.kLblKd0000)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(82.0),
                                       height: getRelativeHeight(23.0), alignment: .topLeading)
                            Text(StringConstants.kLblSubTotal)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(65.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                                .padding(.leading, getRelativeWidth(7.0))
                                .padding(.trailing, getRelativeWidth(10.0))
                        }
                        .frame(width: getRelativeWidth(82.0), height: getRelativeHeight(61.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(6.0))
                        .padding(.bottom, getRelativeHeight(4.0))
                        .padding(.leading, getRelativeWidth(24.0))
                        Button(action: {
                            shopdetails1ViewModel.nextScreen = "BookappoinmentView"
                        }, label: {
                            HStack(spacing: 0) {
                                Text(StringConstants.kLblBook)
                                    .font(FontScheme
                                        .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.heavy)
                                    .padding(.horizontal, getRelativeWidth(30.0))
                                    .padding(.vertical, getRelativeHeight(12.0))
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(250.0),
                                           height: getRelativeHeight(44.0), alignment: .leading)
                                    .background(RoundedCorners(topLeft: 22.0, topRight: 22.0,
                                                               bottomLeft: 22.0, bottomRight: 22.0)
                                            .fill(ColorConstants.Black9003f))
                                    .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0,
                                            y: 1)
                                    .padding(.top, getRelativeHeight(13.0))
                                    .padding(.bottom, getRelativeHeight(15.0))
                                    .padding(.leading, getRelativeWidth(16.0))
                            }
                        })
                        .frame(width: getRelativeWidth(250.0), height: getRelativeHeight(44.0),
                               alignment: .leading)
                        .background(RoundedCorners(topLeft: 22.0, topRight: 22.0, bottomLeft: 22.0,
                                                   bottomRight: 22.0)
                                .fill(ColorConstants.Black9003f))
                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                        .padding(.top, getRelativeHeight(13.0))
                        .padding(.bottom, getRelativeHeight(15.0))
                        .padding(.leading, getRelativeWidth(16.0))
                    }
                    .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(72.0),
                           alignment: .leading)
                    .background(RoundedCorners(topLeft: 40.0, topRight: 40.0)
                        .fill(ColorConstants.Cyan800))
                    .shadow(color: ColorConstants.Black9003f, radius: 6, x: 0, y: 0)
                    .padding(.top, getRelativeHeight(20.0))
                }
                .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
//                Group {
//                    NavigationLink(destination: BookAppointmentView(vendorID: 1, serviceIDs: []),
//                                   tag: "BookappoinmentView",
//                                   selection: $shopdetails1ViewModel.nextScreen,
//                                   label: {
//                                       EmptyView()
//                                   })
//                }
            }
            .hideNavigationBar()
        }
        .hideNavigationBar()
    }
}

struct Shopdetails1View_Previews: PreviewProvider {
    static var previews: some View {
        Shopdetails1View()
    }
}
