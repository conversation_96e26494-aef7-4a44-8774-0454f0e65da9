import SwiftUI

struct Rowtime1Cell: View {
    var body: some View {
        HStack {
            Text(StringConstants.kLbl0900Am)
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(11.0)))
                .fontWeight(.semibold)
                .padding(.horizontal, getRelativeWidth(14.0))
                .padding(.bottom, getRelativeHeight(5.0))
                .padding(.top, getRelativeHeight(7.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.center)
                .frame(width: getRelativeWidth(78.0), height: getRelativeHeight(29.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 14.5, topRight: 14.5, bottomLeft: 14.5,
                                        bottomRight: 14.5)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan80035)
            Text(StringConstants.kLbl1000Am)
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(11.0)))
                .fontWeight(.semibold)
                .padding(.horizontal, getRelativeWidth(14.0))
                .padding(.bottom, getRelativeHeight(5.0))
                .padding(.top, getRelativeHeight(7.0))
                .foregroundColor(ColorConstants.WhiteA700)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.center)
                .frame(width: getRelativeWidth(78.0), height: getRelativeHeight(29.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 14.5, topRight: 14.5, bottomLeft: 14.5,
                                        bottomRight: 14.5)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan800)
                .padding(.leading, getRelativeWidth(8.0))
            ZStack(alignment: .center) {
                Text(StringConstants.kLbl1100Am)
                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(11.0)))
                    .fontWeight(.semibold)
                    .foregroundColor(ColorConstants.Bluegray102)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(47.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(7.12))
                    .padding(.horizontal, getRelativeWidth(14.91))
                Divider()
                    .frame(width: getRelativeWidth(60.0), height: getRelativeHeight(25.0),
                           alignment: .leading)
                    .background(ColorConstants.Bluegray101)
                    .padding(.horizontal, getRelativeWidth(8.0))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(78.0), height: getRelativeHeight(29.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 14.5, topRight: 14.5, bottomLeft: 14.5,
                                    bottomRight: 14.5)
                    .stroke(ColorConstants.Bluegray101,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 14.5, topRight: 14.5, bottomLeft: 14.5,
                                       bottomRight: 14.5)
                    .fill(ColorConstants.Gray5003f1))
            .padding(.leading, getRelativeWidth(8.0))
            Text(StringConstants.kLbl1200Pm)
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(11.0)))
                .fontWeight(.semibold)
                .padding(.horizontal, getRelativeWidth(15.0))
                .padding(.bottom, getRelativeHeight(5.0))
                .padding(.top, getRelativeHeight(7.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.center)
                .frame(width: getRelativeWidth(78.0), height: getRelativeHeight(29.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 14.5, topRight: 14.5, bottomLeft: 14.5,
                                        bottomRight: 14.5)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(ColorConstants.Cyan80035)
                .padding(.leading, getRelativeWidth(8.0))
        }
        .frame(width: getRelativeWidth(342.0), alignment: .leading)
        .hideNavigationBar()
    }
}

/* struct Rowtime1Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Rowtime1Cell()
 }
 } */
