import SwiftUI

struct Rowbookingdate2Cell: View {
    var body: some View {
        HStack {
            Text(StringConstants.kLblBookingDate)
                .font(FontScheme.kMontserratMedium(size: getRelativeHeight(13.0)))
                .fontWeight(.medium)
                .foregroundColor(ColorConstants.Black900B2)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(86.0), height: getRelativeHeight(16.0),
                       alignment: .leading)
            HStack {
                Text(StringConstants.kLblNov282023)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black900)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(80.0), height: getRelativeHeight(18.0),
                           alignment: .leading)
                But<PERSON>(action: {}, label: {
                    Image("img_edit")
                })
                .frame(width: getRelativeWidth(23.0), height: getRelativeWidth(25.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                        bottomRight: 12.5)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                           bottomRight: 12.5)
                        .fill(Color.clear.opacity(0.7)))
                .padding(.leading, getRelativeWidth(9.0))
            }
            .frame(width: getRelativeWidth(114.0), height: getRelativeHeight(25.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(124.0))
        }
        .frame(width: getRelativeWidth(327.0), alignment: .leading)
        .hideNavigationBar()
    }
}

/* struct Rowbookingdate2Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Rowbookingdate2Cell()
 }
 } */
