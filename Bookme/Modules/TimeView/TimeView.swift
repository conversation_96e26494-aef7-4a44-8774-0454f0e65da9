import SwiftUI

struct TimeView: View {
    @StateObject var timeViewModel = TimeViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            ZStack(alignment: .leading) {
                HStack {
                    HStack {
                        Image("img_arrowleft_black_900")
                            .resizable()
                            .frame(width: getRelativeWidth(16.0), height: getRelativeHeight(12.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                            .padding(.top, getRelativeHeight(5.0))
                            .onTapGesture {
                                self.presentationMode.wrappedValue.dismiss()
                            }
                        Text("Cancel / Modify")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(100.0), height: getRelativeHeight(20.0),
                                   alignment: .topLeading)
                            .padding(.leading, getRelativeWidth(32.0))
                    }
                    .frame(width: getRelativeWidth(148.0), height: getRelativeHeight(20.0),
                           alignment: .leading)
                }
                .frame(width: getRelativeWidth(148.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.trailing, getRelativeWidth(220.0))
                ScrollView(.vertical, showsIndicators: false) {
                    ZStack(alignment: .center) {
                        VStack(alignment: .leading, spacing: 0) {
                            VStack {
                                Group {
                                    HStack {
                                        Text(StringConstants.kLblBarberSalon)
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(88.0),
                                                   height: getRelativeHeight(16.0),
                                                   alignment: .topLeading)
                                            .padding(.bottom, getRelativeHeight(4.0))
                                        Spacer()
                                        Text(StringConstants.kMsgBroadwayBarber)
                                            .font(FontScheme
                                                .kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(163.0),
                                                   height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                            .padding(.leading, getRelativeWidth(70.0))
                                    }
                                    .frame(width: getRelativeWidth(323.0),
                                           height: getRelativeHeight(20.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(20.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(9.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text(StringConstants.kLblAddress)
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(52.0),
                                                   height: getRelativeHeight(16.0),
                                                   alignment: .topLeading)
                                            .padding(.vertical, getRelativeHeight(2.0))
                                        Spacer()
                                        Text(StringConstants.kMsg4thFloorAlZ2)
                                            .font(FontScheme
                                                .kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.trailing)
                                            .frame(width: getRelativeWidth(208.0),
                                                   height: getRelativeHeight(34.0),
                                                   alignment: .topTrailing)
                                            .padding(.leading, getRelativeWidth(64.0))
                                    }
                                    .frame(width: getRelativeWidth(325.0),
                                           height: getRelativeHeight(34.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(10.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(7.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text(StringConstants.kLblName)
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(39.0),
                                                   height: getRelativeHeight(16.0),
                                                   alignment: .topLeading)
                                        Spacer()
                                        Text(StringConstants.kLblJithun)
                                            .font(FontScheme
                                                .kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(39.0),
                                                   height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0),
                                           height: getRelativeHeight(18.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(12.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(8.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text(StringConstants.kLblPhone)
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(42.0),
                                                   height: getRelativeHeight(16.0),
                                                   alignment: .topLeading)
                                        Spacer()
                                        Text(StringConstants.kLbl9539187941)
                                            .font(FontScheme
                                                .kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(82.0),
                                                   height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0),
                                           height: getRelativeHeight(19.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(14.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(8.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                }
                                Group {
                                    VStack(spacing: 0) {
                                        ScrollView(.vertical, showsIndicators: false) {
                                            LazyVStack {
                                                ForEach(0 ... 1, id: \.self) { index in
                                                    Rowbookingdate2Cell()
                                                }
                                            }
                                        }
                                    }
                                    .frame(width: getRelativeWidth(329.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(10.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(328.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8004c)
                                        .padding(.top, getRelativeHeight(6.0))
                                        .padding(.horizontal, getRelativeWidth(15.0))
                                    HStack {
                                        Text(StringConstants.kLblSpecialist)
                                            .font(FontScheme
                                                .kMontserratMedium(size: getRelativeHeight(13.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Black900B2)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(60.0),
                                                   height: getRelativeHeight(16.0),
                                                   alignment: .topLeading)
                                        Spacer()
                                        Text(StringConstants.kLblJineesh)
                                            .font(FontScheme
                                                .kNunitoBold(size: getRelativeHeight(13.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Black900)
                                            .minimumScaleFactor(0.5)
                                            .multilineTextAlignment(.leading)
                                            .frame(width: getRelativeWidth(48.0),
                                                   height: getRelativeHeight(18.0),
                                                   alignment: .topLeading)
                                    }
                                    .frame(width: getRelativeWidth(324.0),
                                           height: getRelativeHeight(18.0), alignment: .center)
                                    .padding(.vertical, getRelativeHeight(16.0))
                                    .padding(.horizontal, getRelativeWidth(15.0))
                                }
                            }
                            .frame(width: getRelativeWidth(360.0), height: getRelativeHeight(337.0),
                                   alignment: .leading)
                            .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                    bottomRight: 20.0)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 20.0, topRight: 20.0,
                                                       bottomLeft: 20.0, bottomRight: 20.0)
                                    .fill(ColorConstants.WhiteA700))
                            Text(StringConstants.kMsgSelectedServic)
                                .font(FontScheme.kMontserratMedium(size: getRelativeHeight(16.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Black900)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(137.0),
                                       height: getRelativeHeight(20.0), alignment: .topLeading)
                                .padding(.top, getRelativeHeight(39.0))
                                .padding(.trailing, getRelativeWidth(10.0))
                            VStack {
                                VStack(spacing: 0) {
                                    ScrollView(.vertical, showsIndicators: false) {
                                        LazyVStack {
                                            ForEach(0 ... 2, id: \.self) { index in
                                                Rowkdcounter2Cell()
                                            }
                                        }
                                    }
                                }
                                .frame(width: getRelativeWidth(321.0), alignment: .center)
                                .padding(.vertical, getRelativeHeight(19.0))
                                .padding(.horizontal, getRelativeWidth(19.0))
                            }
                            .frame(width: getRelativeWidth(360.0), height: getRelativeHeight(152.0),
                                   alignment: .leading)
                            .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                                    bottomRight: 20.0)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 20.0, topRight: 20.0,
                                                       bottomLeft: 20.0, bottomRight: 20.0)
                                    .fill(ColorConstants.WhiteA700))
                            .padding(.top, getRelativeHeight(10.0))
                        }
                        .frame(width: getRelativeWidth(360.0), height: getRelativeHeight(559.0),
                               alignment: .center)
                        .padding(.bottom, getRelativeHeight(250.78))
                        .padding(.horizontal, getRelativeWidth(15.0))
                        VStack {
                            Text(StringConstants.kLblSubmit)
                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.heavy)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(51.0),
                                       height: getRelativeHeight(20.0), alignment: .topLeading)
                                .padding(.top, getRelativeHeight(12.0))
                                .padding(.bottom, getRelativeHeight(14.0))
                                .padding(.horizontal, getRelativeWidth(129.0))
                        }
                        .frame(width: getRelativeWidth(310.0), height: getRelativeHeight(47.0),
                               alignment: .center)
                        .background(RoundedCorners(topLeft: 23.81, topRight: 23.81,
                                                   bottomLeft: 23.81, bottomRight: 23.81)
                                .fill(ColorConstants.Cyan800))
                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                        .padding(.top, getRelativeHeight(807.22))
                        .padding(.horizontal, getRelativeWidth(39.0))
                        Text(StringConstants.kMsgIfYouCancelL)
                            .font(FontScheme.kMontserratSemiBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.semibold)
                            .foregroundColor(ColorConstants.Black900)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(132.0),
                                   alignment: .topLeading)
                            .padding(.top, getRelativeHeight(651.13))
                            .padding(.horizontal, getRelativeWidth(19.93))
                        ZStack {}
                            .hideNavigationBar()
                            .frame(width: UIScreen.main.bounds.width,
                                   height: UIScreen.main.bounds.height,
                                   alignment: .topLeading)
                            .background(ColorConstants.Black90066)
                        VStack {
                            ZStack(alignment: .bottomLeading) {
                                VStack(alignment: .trailing, spacing: 0) {
                                    Image("img_closeroundlig")
                                        .resizable()
                                        .frame(width: getRelativeWidth(12.0),
                                               height: getRelativeWidth(12.0), alignment: .center)
                                        .scaledToFit()
                                        .clipped()
                                        .padding(.leading)
                                        .padding(.leading)
                                        .padding(.trailing, getRelativeWidth(6.0))
                                    Divider()
                                        .frame(width: getRelativeWidth(349.0),
                                               height: getRelativeHeight(1.0), alignment: .center)
                                        .background(ColorConstants.Cyan8003f)
                                        .padding(.top, getRelativeHeight(23.0))
                                        .padding(.trailing, getRelativeWidth(4.0))
                                }
                                .frame(width: getRelativeWidth(353.0),
                                       height: getRelativeHeight(42.0), alignment: .center)
                                .padding(.bottom, getRelativeHeight(87.0))
                                VStack {
                                    Text(StringConstants.kLblSelectTime)
                                        .font(FontScheme
                                            .kNunitoSemiBold(size: getRelativeHeight(16.0)))
                                        .fontWeight(.semibold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(86.0),
                                               height: getRelativeHeight(22.0),
                                               alignment: .topLeading)
                                        .padding(.trailing, getRelativeWidth(10.0))
                                    VStack(spacing: 0) {
                                        ScrollView(.vertical, showsIndicators: false) {
                                            LazyVStack {
                                                ForEach(0 ... 1, id: \.self) { index in
                                                    Rowtime1Cell()
                                                }
                                            }
                                        }
                                    }
                                    .frame(width: getRelativeWidth(347.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(34.0))
                                    .padding(.leading, getRelativeWidth(7.0))
                                }
                                .frame(width: getRelativeWidth(354.0),
                                       height: getRelativeHeight(123.0), alignment: .bottomLeading)
                                .background(RoundedCorners(topLeft: 22.0, topRight: 22.0,
                                                           bottomLeft: 22.0, bottomRight: 22.0))
                                .padding(.top, getRelativeHeight(5.36))
                            }
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(354.0), height: getRelativeHeight(129.0),
                                   alignment: .center)
                            .padding(.top, getRelativeHeight(10.0))
                            .padding(.leading, getRelativeWidth(20.0))
                            .padding(.trailing, getRelativeWidth(16.0))
                            Button(action: {}, label: {
                                HStack(spacing: 0) {
                                    Text(StringConstants.kLblSubmit)
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(7.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(129.0),
                                               height: getRelativeHeight(35.0), alignment: .center)
                                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                                   bottomLeft: 17.5,
                                                                   bottomRight: 17.5)
                                                .fill(ColorConstants.Cyan800))
                                        .padding(.vertical, getRelativeHeight(45.0))
                                        .padding(.horizontal, getRelativeWidth(20.0))
                                }
                            })
                            .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.vertical, getRelativeHeight(45.0))
                            .padding(.horizontal, getRelativeWidth(20.0))
                        }
                        .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(232.0),
                               alignment: .bottomLeading)
                        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                            .fill(ColorConstants.WhiteA700))
                        .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
                        .padding(.top, getRelativeHeight(651.22))
                    }
                    .hideNavigationBar()
                    .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                           alignment: .topLeading)
                }
            }
            .hideNavigationBar()
            .frame(width: UIScreen.main.bounds.width, alignment: .topLeading)
            .background(ColorConstants.WhiteA700)
            .padding(.top, getRelativeHeight(30.0))
            .padding(.bottom, getRelativeHeight(10.0))
        }
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(ColorConstants.WhiteA700)
        .ignoresSafeArea()
        .hideNavigationBar()
    }
}

struct TimeView_Previews: PreviewProvider {
    static var previews: some View {
        TimeView()
    }
}
