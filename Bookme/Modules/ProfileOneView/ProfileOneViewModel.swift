import Foundation
import SwiftUI

class ProfileOneViewModel: ObservableObject {
    @Published var nextScreen: String? = nil
    @Published var group186Text: String = ""
    @Published var emailoneText: String = ""
    @Published var phonNumberText: String = ""
    @Published var isValidEmailoneText: Bool = true
    @Published var group190Text: String = ""
    @Published var group191Text: String = ""
    @Published var group192Text: String = ""
    @Published var group193Text: String = ""
}
