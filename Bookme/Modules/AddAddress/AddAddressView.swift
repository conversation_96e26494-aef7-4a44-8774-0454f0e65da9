import SwiftUI

struct AddAddressView: View {
    let model: AddressModel?
    let onDone: VoidCallback
    @Environment(\.dismiss) var dismiss: DismissAction
    
    @StateObject var viewModel: AddAddressViewModel
    @EnvironmentObject private var appState: AppState
    
    // Add focus state for keyboard navigation
    @State private var focusedFieldIndex: Int?
    @FocusState private var focusedField: TextFieldFocus?
    
    init(model: AddressModel?, onDone: @escaping VoidCallback) {
        self.model = model
        self.onDone = onDone
        self._viewModel = StateObject(wrappedValue: AddAddressViewModel(addressModel: model))
    }
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Add Address") {
                VStack {
                  
                        VStack(alignment: .leading, spacing: 0) {
                            Group {
                                let nameIndex: Int = 0
                                CustomTextField(title: "Name", placeholder: StringConstants.kLblEstherHoward, text: self.$viewModel.nameText, invalidText: self.viewModel.invalidTextFiledText[nameIndex])
                                    .frame(width: getRelativeWidth(321.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(16))
                                    .onChange(of: self.viewModel.nameText) { _, newValue in
                                        withAnimation(.bouncy) {
                                            self.viewModel.invalidTextFiledText[nameIndex] = newValue.isEmptyOrWhitespace
                                                ? "Name field can't be empty"
                                                : ""
                                        }
                                    }
                                    .focused($focusedField, equals: .field(nameIndex))
                                    .focusable(fieldIndex: nameIndex, focusedFieldIndex: $focusedFieldIndex)
                                
                                let phoneIndex: Int = 1
                                CustomTextField(title: "Mobile Number", placeholder: "Mobile Number", text: self.$viewModel.phonNumberText, invalidText: self.viewModel.invalidTextFiledText[phoneIndex], isPhone: true)
                                    .keyboardType(.numberPad)
                                    .frame(width: getRelativeWidth(321.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(16))
                                    .onChange(of: self.viewModel.phonNumberText) { _, newValue in
                                        withAnimation(.bouncy) {
                                            self.viewModel.invalidTextFiledText[phoneIndex] = newValue.isEmptyOrWhitespace
                                                ? "Phone number field can't be empty"
                                                : ""
                                        }
                                    }
                                    .focused($focusedField, equals: .field(phoneIndex))
                                    .focusable(fieldIndex: phoneIndex, focusedFieldIndex: $focusedFieldIndex)
                                
                                let emailIndex: Int = 2
                                CustomTextField(title: "Email", placeholder: "\("<EMAIL>")", text: self.$viewModel.emailText, invalidText: self.viewModel.invalidTextFiledText[emailIndex], isOptional: true)
                                    .keyboardType(.emailAddress)
                                    .frame(width: getRelativeWidth(321.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(16))
                                    .onChange(of: self.viewModel.emailText) { _, newValue in
                                        withAnimation(.bouncy) {
                                            self.viewModel.invalidTextFiledText[emailIndex] = newValue.isValidEmail(isMandatory: false)
                                                ? ""
                                                : "Please enter valid email."
                                        }
                                    }
                                    .focused($focusedField, equals: .field(emailIndex))
                                    .focusable(fieldIndex: emailIndex, focusedFieldIndex: $focusedFieldIndex)
                                    
                                VStack(alignment: .leading, spacing: 0) {
                                    HStack(spacing: 4) {
                                        Text("Area")
                                        Text("*")
                                            .foregroundColor(.red)
                                    }
                                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .multilineTextAlignment(.leading)
                                    .frame(
                                        height: getRelativeHeight(18.0), alignment: .topLeading
                                    )
                                    .padding(.top, getRelativeHeight(19.0))
                                    .padding(.horizontal, getRelativeWidth(12.0))
                                    HStack {
                                        if self.viewModel.pageState == .loading() {
                                            ProgressView()
                                                .tint(.white)
                                                .padding(.horizontal)
                                            Spacer()
                                        }
                                            
                                        else {
                                            Menu {
                                                ForEach(self.viewModel.areaList) { type in
                                                    Button {
                                                        withAnimation(.bouncy) {
                                                            self.viewModel.selectedArea = type
                                                        }
                                                                   
                                                    } label: {
                                                        Text(type.areaTitle.capitalized)
                                                    }
                                                }

                                            } label: {
                                                HStack {
                                                    if let selectedArea = viewModel.selectedArea {
                                                        HStack {
                                                            Text(selectedArea.areaTitle.capitalized)
                                                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                                                .fontWeight(.medium)
                                                                .foregroundColor(ColorConstants.WhiteA700)
                                                                .fixedSize()
                                                                .multilineTextAlignment(.leading)
                                                                .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(18.0),
                                                                       alignment: .topLeading)
                                                                .padding(.top, getRelativeHeight(15.0))
                                                                .padding(.bottom, getRelativeHeight(16.0))
                                                                .padding(.leading, getRelativeWidth(20.0))
                                                        }
                                                    } else {
                                                        Text("Select")
                                                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                                            .fontWeight(.medium)
                                                            .foregroundColor(ColorConstants.Cyan800.opacity(0.77))
                                                            .fixedSize()
                                                            .multilineTextAlignment(.leading)
                                                            .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(18.0),
                                                                   alignment: .topLeading)
                                                            .padding(.top, getRelativeHeight(15.0))
                                                            .padding(.bottom, getRelativeHeight(16.0))
                                                            .padding(.leading, getRelativeWidth(20.0))
                                                    }
                                                    Spacer()
                                                    Image("img_arrowup")
                                                        .resizable()
                                                        .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(6.0),
                                                               alignment: .center)
                                                        .scaledToFit()
                                                        .clipped()
                                                        .padding(.vertical, getRelativeHeight(22.0))
                                                        .padding(.trailing, getRelativeWidth(24.0))
                                                }
                                                .foregroundColor(.blue)
                                                .padding(.vertical, 12)
                                            }
                                        }
                                    }
                                    .frame(width: getRelativeWidth(321.0),
                                           height: getRelativeHeight(50.0), alignment: .leading)
                                    .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0,
                                                            bottomLeft: 13.0, bottomRight: 13.0)
                                            .stroke(ColorConstants.Cyan800,
                                                    lineWidth: 1))
                                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0,
                                                               bottomLeft: 13.0, bottomRight: 13.0)
                                            .fill(ColorConstants.Cyan8003f1))
                                    .padding(.top, getRelativeHeight(7.0))
                                }
                                
                                let blockIndex: Int = 3
                                CustomTextField(title: "Block No.", placeholder: "Block No.", text: self.$viewModel.blockText, invalidText: self.viewModel.invalidTextFiledText[blockIndex])
                                    .keyboardType(.default)
                                    .frame(width: getRelativeWidth(321.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(16))
                                    .onChange(of: self.viewModel.blockText) { _, newValue in
                                        withAnimation(.bouncy) {
                                            self.viewModel.invalidTextFiledText[blockIndex] = newValue.isEmptyOrWhitespace
                                                ? "Block No. field can't be empty"
                                                : ""
                                        }
                                    }
                                    .focused($focusedField, equals: .field(blockIndex))
                                    .focusable(fieldIndex: blockIndex, focusedFieldIndex: $focusedFieldIndex)
                                
                                let streetIndex: Int = 4
                                CustomTextField(title: "Street", placeholder: "Street", text: self.$viewModel.streetText, invalidText: self.viewModel.invalidTextFiledText[streetIndex])
                                    .keyboardType(.default)
                                    .frame(width: getRelativeWidth(321.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(16))
                                    .onChange(of: self.viewModel.streetText) { _, newValue in
                                        withAnimation(.bouncy) {
                                            self.viewModel.invalidTextFiledText[streetIndex] = newValue.isEmptyOrWhitespace
                                                ? "Street field can't be empty"
                                                : ""
                                        }
                                    }
                                    .focused($focusedField, equals: .field(streetIndex))
                                    .focusable(fieldIndex: streetIndex, focusedFieldIndex: $focusedFieldIndex)
                                
                                let houseFlatIndex: Int = 5
                                CustomTextField(title: "House No/ Flat No", placeholder: "House No/ Flat No", text: self.$viewModel.houseFlatText, invalidText: self.viewModel.invalidTextFiledText[houseFlatIndex])
                                    .keyboardType(.default)
                                    .frame(width: getRelativeWidth(321.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(16))
                                    .onChange(of: self.viewModel.houseFlatText) { _, newValue in
                                        withAnimation(.bouncy) {
                                            self.viewModel.invalidTextFiledText[houseFlatIndex] = newValue.isEmptyOrWhitespace
                                                ? "House No/ Flat No field can't be empty"
                                                : ""
                                        }
                                    }
                                    .focused($focusedField, equals: .field(houseFlatIndex))
                                    .focusable(fieldIndex: houseFlatIndex, focusedFieldIndex: $focusedFieldIndex)
                                
                                let avenueBuildingIndex: Int = 6
                                CustomTextField(title: "Avenue / Building", placeholder: "Avenue / Building", text: self.$viewModel.avenueBuildingText, invalidText: self.viewModel.invalidTextFiledText[avenueBuildingIndex], isOptional: true)
                                    .keyboardType(.default)
                                    .frame(width: getRelativeWidth(321.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(16))
                                    .onChange(of: self.viewModel.avenueBuildingText) { _, newValue in
                                        withAnimation(.bouncy) {
                                            self.viewModel.invalidTextFiledText[avenueBuildingIndex] = newValue.isEmptyOrWhitespace
                                                ? "Avenue / Building field can't be empty"
                                                : ""
                                        }
                                    }
                                    .focused($focusedField, equals: .field(avenueBuildingIndex))
                                    .focusable(fieldIndex: avenueBuildingIndex, focusedFieldIndex: $focusedFieldIndex)
                            }
                        }
                        .padding(.bottom, 36.0.relativeHeight)
                        .padding(.horizontal, 4)
                        .padding(.top)
                    
                }
                // Add the keyboard navigation modifiers
                .coordinateFocus(focusedField: _focusedField, focusedFieldIndex: $focusedFieldIndex)
                .addKeyboardNavigation(focusedFieldIndex: $focusedFieldIndex, totalFields: 7, onDismiss: dismissKeyboard)
                .frame(maxWidth: .infinity)
            }
            .safeAreaInset(edge: .bottom) {
                Button(action: {
                    self.viewModel.onAddAdress {
                        self.dismiss()
                        self.onDone()
                    }
                   
                }, label: {
                    HStack(spacing: 0) {
                        Text(self.viewModel.addressModel != nil ? "Update Address" : "Add Address")
                            .font(FontScheme
                                .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.heavy)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(15.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .frame(width: 240.toDouble.relativeWidth, height: 44.toDouble.relativeHeight)
                            .background(Capsule()
                                .fill(ColorConstants.Cyan800))
                            .shadow(color: ColorConstants.Black90044, radius: 2.5,
                                    x: 0, y: 1)
                    }
                })
                .disableWithOpacity(self.viewModel.disableAddAdressButton)
                .background(Capsule()
                    .fill(ColorConstants.Cyan800))
            }
            .background(ColorConstants.bgGradient)
        }
    }
    
    // Add helper method to dismiss keyboard
    private func dismissKeyboard() {
        focusedField = nil
        focusedFieldIndex = nil
    }
}

struct ProfileOneView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            AddAddressView(model: nil, onDone: {}).attachAllEnvironmentObjects()
        }
    }
}


struct DismissKeyboardOnTap: ViewModifier {
    func body(content: Content) -> some View {
        content
            .toolbar { // << here !!
                ToolbarItem(placement: .keyboard) {
                    Button("Done") {
                        dismissKeyboard()
                    }
                    .tint(.blue)
                    .frame(maxWidth: .infinity, alignment: .trailing)
                }
            }
    }
    
    private func dismissKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

extension View {
    func addDoneToKeyboard() -> some View {
        modifier(DismissKeyboardOnTap())
    }
}
