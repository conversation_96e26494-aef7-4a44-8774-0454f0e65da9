//
//  AddAddressViewModel.swift
//  Bookme
//
//  Created by Apple on 06/05/2024.
//

import Foundation

class AddAddressViewModel: SuperViewModel {
    @Published var nameText: String = ""
    @Published var phonNumberText: String = ""
    @Published var emailText: String = ""
    @Published var blockText: String = ""
    @Published var streetText: String = ""
    @Published var houseFlatText: String = ""
    @Published var avenueBuildingText: String = ""
    
    @Published var isValidEmailoneText: Bool = true
    
    @Published var selectedArea: AreaModel?
    @Published var areaList: [AreaModel] = []
    
    @Published var invalidTextFiledText: [String] = Array(repeating: "", count: 7)
    
    let addressModel: AddressModel?
    
    init(addressModel: AddressModel?) {
        self.addressModel = addressModel
         
        super.init()
        fillAllFields(addressModel)
        getAreaList(addressModel)
    }
    
    var disableAddAdressButton: Bool {
        nameText.isEmptyOrWhitespace || phonNumberText.isEmptyOrWhitespace ||  !isValidEmailoneText || blockText.isEmptyOrWhitespace || streetText.isEmptyOrWhitespace || houseFlatText.isEmptyOrWhitespace ||  selectedArea == nil
    }
    
    func fillAllFields(_ addressModel: AddressModel?) {
        if let addressModel = addressModel {
            nameText = addressModel.name
            phonNumberText = addressModel.phoneNumber
            emailText = addressModel.eMail ?? ""
            blockText = addressModel.block
            streetText = addressModel.street
            houseFlatText = addressModel.houseNo
            avenueBuildingText = addressModel.avenue ?? ""
        }
    }
    
    func getAreaList(_ addressModel: AddressModel? = nil) {
        onApiCall(api.areas, parameters: emptyDictionary, withLoadingIndicator: false) { self.areaList = $0.data ?? []
            if let addressModel = addressModel {
                self.selectedArea = self.areaList.first(where: { $0.areaTitle.lowercased() == addressModel.area.lowercased() })
            }
        }
    }
    
    func onAddAdress(completion: @escaping () -> Void) {
        if let addressModel = addressModel {
            editAddress(addressID: addressModel.addressID, completion: completion)
        } else {
            addAddress(completion: completion)
        }
    }
 
    func editAddress(addressID: Int, completion: @escaping () -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let parameters = [
            "Address_ID": "\(addressID)",
            "user_id": "\(userID)",
            "Name": nameText,
            "Phone_Number": phonNumberText,
            "E_mail": emailText,
            "Area": selectedArea?.areaTitle ?? "",
            "Block": blockText,
            "Street": streetText,
            "HouseNo": houseFlatText,
            "Avenue": avenueBuildingText
        ]
        onApiCall(api.editAddress, parameters: parameters) {
            if $0.success { completion() }
        }
    }
    
    func addAddress(completion: @escaping () -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let parameters = [
            "user_id": "\(userID)",
            "Name": nameText,
            "Phone_Number": phonNumberText,
            "E_mail": emailText,
            "Area": selectedArea?.areaTitle ?? "",
            "Block": blockText,
            "Street": streetText,
            "HouseNo": houseFlatText,
            "Avenue": avenueBuildingText
        ]
        onApiCall(api.addAddress, parameters: parameters) {
            if $0.success { completion() }
        }
    }
}
