import SwiftUI
import WrappingHStack

struct AppointmentUpcomingCell: View {
    let model: BookingAppointmentModel
    @State private var isRemindMe: Bool = true
    @Environment(RouterManager.self) private var routerManager: RouterManager
    var body: some View {
        VStack {
            HStack(alignment: .top) {
                let serviceDate = model.serviceDateFormattedAsString ?? ""
                
                Text("\(serviceDate), \(model.serviceTime)")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black900)
                    .multilineTextAlignment(.leading)
                    
                Spacer()
//                Toggle("Remind Me", isOn: $isRemindMe)
//                    .toggleStyle(SwitchToggleStyle(tint: ColorConstants.Cyan800))
//                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
//                    .fontWeight(.semibold)
//                    .foregroundColor(ColorConstants.Cyan800B2)
//                    .multilineTextAlignment(.leading)
////                    .fixedSize()
//                    .frame(maxWidth: 130.relativeWidth)
            }
            .frame(width: getRelativeWidth(328.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(15.0))
            .padding(.horizontal, getRelativeWidth(14.0))
            Divider()
                .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, getRelativeHeight(9.0))
                .padding(.horizontal, getRelativeWidth(14.0))

            HStack(alignment: .top, spacing: 16.0.relativeWidth) {
                NetworkImageView(path: model.vendorLogoUrl)
                    .frame(width: getRelativeWidth(72.0), height: getRelativeWidth(74.0),
                           alignment: .leading)
                  
                    .clipShape(Circle())
                  
                    
                VStack(alignment: .leading, spacing: 0) {
                    Text(String(repeating: model.vendorName, count: 1))
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Black900)
//                        .fixedSize(horizontal: /*@START_MENU_TOKEN@*/true/*@END_MENU_TOKEN@*/, vertical: false)
//                        .fixedSize()
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    let adaptiveColumns = [
                        GridItem(.adaptive(minimum: 40, maximum: 200), spacing: 4) // Adjust min and max to control wrapping behavior
                    ]
                    
//                    LazyVGrid(columns: adaptiveColumns, spacing: 8) {
//                        ForEach(model.orderDetails) { order in
//                            Text(order.serviceName)
//                                .font(Font.custom("Nunito", size: 11.relativeFontSize).weight(.medium))
//                                .lineLimit(1)
//                                .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
//                                .padding(.horizontal, 12.relativeWidth)
//                                .padding(.vertical, 4.relativeHeight)
//                                .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.19))
//                                .clipShape(.capsule)
//                        }
//
//                    }.padding(.vertical, 8.0)
                    
                    WrappingHStack(model.orderDetails, id: \.self, alignment: .leading, spacing: .constant(4), lineSpacing: 8) { order in
                        Text(order.serviceName)
                            .font(Font.custom("Nunito", size: 11.relativeFontSize).weight(.medium))
                            .lineLimit(1)
                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                            .padding(.horizontal, 12.relativeWidth)
                            .padding(.vertical, 4.relativeHeight)
                            .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.19))
                            .clipShape(.capsule)
                    }
                    .padding(.vertical, 8.0)
                    
                }.frame(maxWidth: .infinity, alignment: .leading)
            }
            .clipped()
            .transaction { transaction in
                transaction.animation = nil
            }

            .padding(.top, getRelativeHeight(13.0))
            .padding(.horizontal, getRelativeWidth(14.0))
            
            Divider()
                .frame(height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, -1)
                .padding(.horizontal, getRelativeWidth(14.0))
            
            HStack {
                Button(action: {
                    routerManager.push(to: .cancelModify(model: model), where: .appointmentRoute)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Cancel / Modify")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(14.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                    bottomRight: 17.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(Color.clear.opacity(0.7)))
                    }
                })
//                .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
//                       alignment: .center)
//                .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
//                                        bottomRight: 17.5)
//                        .stroke(ColorConstants.Cyan800,
//                                lineWidth: 1))
//                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
//                                           bottomRight: 17.5)
//                        .fill(Color.clear.opacity(0.7)))
                Spacer()
                Button(action: {
                    routerManager.push(to: .eReceipt(model: model), where: .appointmentRoute)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Receipt")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                    }
                })
            }
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(35.0),
                   alignment: .leading)
            .padding(.vertical, getRelativeHeight(19.0))
            .padding(.horizontal, getRelativeWidth(14.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0, bottomRight: 20.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                   bottomRight: 20.0)
                .fill(ColorConstants.WhiteA700))
        .hideNavigationBar()
        .onAppear {
            isRemindMe = model.isRemindMe
        }

    }
}

struct AppointmentUpcomingCellPreviewContainer: View {
    let jsonString = """
           {
             "service_time" : "11:00 AM-11:30 AM",
             "vendor_logo" : "uploads/171838636371.png",
             "vendor_id" : 1,
             "payment_method" : "Knet",
             "remindme" : 1,
             "vendor_email" : "<EMAIL>",
             "cancel" : 0,
             "service_date" : "2024-10-26 11:00:00",
             "vendor_phone" : "50625825",
             "user_name" : "Burhan Rabbani",
             "payment_status" : "Paid",
             "price" : "25",
             "address" : "Abu Halifa,6th Block,Ahmadi,Annapoorna,43007",
             "book_id" : 7,
             "order_details" : [
               {
                 "status" : "0",
                 "order_cart_id" : 27,
                 "vendor_id" : 1,
                 "price" : "15.000",
                 "service_name" : "Spa",
                 "service_id" : 1,
                 "book_ID" : 7,
                 "user_id" : 8,
                 "staff_name" : "Lee"
               },
               {
                 "status" : "0",
                 "order_cart_id" : 28,
                 "vendor_id" : 1,
                 "price" : "10.000",
                 "service_name" : "Hair",
                 "service_id" : 2,
                 "book_ID" : 7,
                 "user_id" : 8,
                 "staff_name" : "jineesh"
               }
             ],
             "distance" : 35,
             "status" : 1,
             "vendor_name" : "Broadway Barber Lounge"
           }
    """
    
    var body: some View {
        VStack {
            let result = JSONConverter.decode(BookingAppointmentModel.self, from: jsonString)
            
            switch result {
            case .success(let us):
                AppointmentUpcomingCell(model: us)
            case .failure(let error):
                Text(error.localizedDescription)
            }
        }
    }
}

#Preview {
    AppointmentUpcomingCellPreviewContainer()
        .attachAllEnvironmentObjects()
}

enum JSONConverter {
    // Generic function to decode a JSON string into a Codable model
    static func decode<T: Codable>(_ type: T.Type, from jsonString: String) -> Result<T, Error> {
        // Convert JSON string to Data
        guard let jsonData = jsonString.data(using: .utf8) else {
            return .failure(JSONError.invalidData)
        }
        
        do {
            // Decode the JSON data into the specified model type
            let decodedObject = try JSONDecoder().decode(T.self, from: jsonData)
            return .success(decodedObject)
        } catch {
            return .failure(error)
        }
    }
    
    // Custom error for invalid data
    enum JSONError: Error {
        case invalidData
    }
}
