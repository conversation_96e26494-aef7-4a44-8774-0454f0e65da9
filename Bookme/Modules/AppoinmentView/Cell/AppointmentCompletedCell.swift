//
//  AppointmentCompletedCell.swift
//  Bookme
//
//  Created by Apple on 02/04/2024.
//

import SwiftUI
import WrappingHStack

struct AppointmentCompletedCell: View {
    let model:BookingAppointmentModel
    let onAddReview: () -> Void
    
    @State private var isRemindMe:Bool = true
    @Environment(RouterManager.self) private var routerManager:RouterManager
    var body: some View {
        VStack {
            HStack {
                Text("\(model.serviceDate), \(model.serviceTime)")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black900)
                    .fixedSize()
                    .multilineTextAlignment(.leading)
                    
               
               
                
            }
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(18.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(15.0))
            .padding(.horizontal, getRelativeWidth(14.0))
            Divider()
                .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, getRelativeHeight(9.0))
                .padding(.horizontal, getRelativeWidth(14.0))

            HStack(alignment: .top, spacing: 16.0.relativeWidth) {
                NetworkImageView(path: model.vendorLogoUrl)
                   
                    .frame(width: getRelativeWidth(72.0), height: getRelativeWidth(74.0),
                           alignment: .leading)
             
                    .clipShape(Circle())
                   
                    
                VStack(alignment: .leading, spacing: 0) {
                    Text(String(repeating: model.vendorName, count: 1))
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Black900)
//                        .fixedSize(horizontal: /*@START_MENU_TOKEN@*/true/*@END_MENU_TOKEN@*/, vertical: false)
//                        .fixedSize()
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                     
                        
//                    Text(model.address)
//                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
//                        .fontWeight(.regular)
//                        .foregroundColor(ColorConstants.Black900B2)
//                      
//                        .multilineTextAlignment(.leading)
//                        
//                    
//
                    
                    WrappingHStack(model.orderDetails, id: \.self, alignment: .leading, spacing: .constant(4), lineSpacing: 8) { order in
                        Text(order.serviceName)
                            .font(Font.custom("Nunito", size: 11.relativeFontSize).weight(.medium))
                            .lineLimit(1)
                            .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
                            .padding(.horizontal, 12.relativeWidth)
                            .padding(.vertical, 4.relativeHeight)
                            .background(Color(red: 0, green: 0.56, blue: 0.59).opacity(0.19))
                            .clipShape(.capsule)
                    }
                    .padding(.vertical, 8.0)
                    
                    
                    
                    HStack{
                        
//                        Image("location_pin")
//                            .resizable()
//                            .scaledToFit()
//                            .frame(width: getRelativeWidth(11.0), height: getRelativeHeight(11.0),
//                                   alignment: .leading)
//                        Text("\(model.distance ?? 0) KM")
//                            .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
//                            .fontWeight(.semibold)
//                            .foregroundColor(ColorConstants.Black900B2)
//                          
//                            .multilineTextAlignment(.leading)
//                            
                           
                       Spacer()
                        Button(action: onAddReview, label: {
                            HStack(spacing: 0) {
                                Text("Add Review")
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .padding(.horizontal, getRelativeWidth(10.0))
                                    .padding(.vertical, getRelativeHeight(4.0))
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(85.0), height: getRelativeHeight(25.0),
                                           alignment: .center)
                                    .overlay(RoundedCorners(topLeft: 12.5, topRight: 12.5, bottomLeft: 12.5,
                                                            bottomRight: 12.5)
                                            .stroke(ColorConstants.Cyan800,
                                                    lineWidth: 1))
                                    .background(RoundedCorners(topLeft: 12.5, topRight: 12.5,
                                                               bottomLeft: 12.5, bottomRight: 12.5)
                                            .fill(ColorConstants.Cyan8003f))
                                   
                            }
                        })
                        .padding([.bottom, .trailing], 4)
                        
                           
                    }
                }
            }
            .clipped()
            .transaction { transaction in
                transaction.animation = nil
            }

           
            .padding(.top, getRelativeHeight(13.0))
            .padding(.horizontal, getRelativeWidth(14.0))
            
            Divider()
                .frame(height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, -1)
                .padding(.horizontal, getRelativeWidth(14.0))
            
            HStack {
                Button(action: {
                    
                    let staffID: String? = model.orderDetails.first?.staffID?.toString
                    routerManager.push(to: .bookAppointment(vendor: .init(vendorID: String(model.vendorID), salonName: model.vendorName, address: model.address, staffID: staffID ?? "NULL", calendarSchedule: model.calendarSchedule ?? 30) , selectedService: model.services ?? [], bookID: model.bookID), where: .appointmentRoute)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Re-Book")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(14.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                    bottomRight: 17.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(Color.clear.opacity(0.7)))
                    }
                })
//                .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
//                       alignment: .center)
//                .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
//                                        bottomRight: 17.5)
//                        .stroke(ColorConstants.Cyan800,
//                                lineWidth: 1))
//                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
//                                           bottomRight: 17.5)
//                        .fill(Color.clear.opacity(0.7)))
                Spacer()
                Button(action: {
                    routerManager.push(to: .eReceipt(model: model), where: .appointmentRoute)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Receipt")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                    }
                })
                
               
            }
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(35.0),
                   alignment: .leading)
            .padding(.vertical, getRelativeHeight(19.0))
            .padding(.horizontal, getRelativeWidth(14.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0, bottomRight: 20.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                   bottomRight: 20.0)
                .fill(ColorConstants.WhiteA700))
        .hideNavigationBar()
      
    }
}







extension Optional where Wrapped == Int {
    /// Converts an Optional Int to a String, returns an empty string if nil
    func toString() -> String {
        switch self {
        case .some(let value):
            return String(value)
        case .none:
            return ""
        }
    }
}
