import Combine
import Foundation
import SwiftUI

enum AppointmentHeaderType: String, CaseIterable {
    case scheduled, completed, cancelled
    
    var title: LocalizedStringKey {
        switch self {
        case .scheduled: return "Scheduled"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        }
    }
    
}

class AppointmentViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var appointmentHeaderType: AppointmentHeaderType = .scheduled
    @Published var selectedBookingAppointmentModel: BookingAppointmentModel?
    
    @Published var bookingAppointmentList: [BookingAppointmentModel] = []
    
    func checkHeader(_ value: AppointmentHeaderType) -> Bool { appointmentHeaderType == value }
    
    func updateHeaderList(_ value: AppointmentHeaderType) {
        appointmentHeaderType = value
    }
    
    func updatePopUpView(_ value: BookingAppointmentModel?) {
        withAnimation(.bouncy) {
            selectedBookingAppointmentModel = value
        }
    }
    
    var appointmentSubscriptions = Set<AnyCancellable>()
    
    override init() {
        super.init()
        
        $appointmentHeaderType
            .removeDuplicates()
            .dropFirst()
            .debounce(for: .seconds(0.0), scheduler: RunLoop.main)
            .sink { [weak self] type in
                self?.getAppointments(value: type)
            }.store(in: &appointmentSubscriptions)
    }

    @Published var isAddReviewLoading: Bool = false
    
    // Convert the `Published` property to a `Binding`
    var isAddReviewLoadingLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isAddReviewLoading }, // Getter for the binding
            set: { self.isAddReviewLoading = $0 } // Setter for the binding
        )
    }
    
    func addReview(_ value: AddReviewRequest, onComplete: @escaping () -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { return }
        var parameters: [String: Any] = ["user_id": userID, "vendor_id": value.vendorID, "rating": value.rating]
        
        if let quickReview = value.quickReview {
            if !quickReview.isEmpty { parameters.updateValue(quickReview, forKey: "quickreview") }
            
        }
        
        if let review = value.review {
            if !review.isEmpty { parameters.updateValue(review, forKey: "description") }
        }
        
        onApiCall(api.addSalonRating, parameters: parameters, customLoadingBinding: isAddReviewLoadingLoadingBinding) {
            if $0.success { onComplete() }
        }
    }
    
    func onAppear() {
        self.updateHeaderList(.scheduled)
    }
    
    func getAppointments(value: AppointmentHeaderType) {
        bookingAppointmentList.removeAll()
        guard let userID: Int = AppState.userModel?.user.id else { return }
        guard let coordination = AppState.userLocation else { return }
        
        let params: [String: Any] = ["user_id": userID, "lat": coordination.latitude, "lng": coordination.longitude]
        
        switch value {
        case .scheduled:
            getUpcoming(params)
        case .completed:
//            getUpcoming(params)
            getCompleted(params)
        case .cancelled:
            getCancelled(params)
        }
    }
    
    func getUpcoming(_ params: [String: Any]) {
        onApiCall(api.upcomingBookings, parameters: params) {
            self.bookingAppointmentList = $0.data ?? []
        } onFailure: { _ in
        }
    }
    
    func getCompleted(_ params: [String: Any]) {
        onApiCall(api.completedBookings, parameters: params) {
            self.bookingAppointmentList = $0.data ?? []
        }
    }
    
    func getCancelled(_ params: [String: Any]) {
        onApiCall(api.cancelledBookings, parameters: params) {
            self.bookingAppointmentList = $0.data ?? []
        }
    }
}
