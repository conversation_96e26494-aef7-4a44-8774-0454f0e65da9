//
//  BookingAppointment.swift
//  Bookme
//
//  Created by Apple on 18/06/2024.
//

import Foundation

// MARK: - BookingAppointment

struct AddReviewRequest {
    let rating: Double
    let quickReview, review: String?
    let vendorID: Int
    
}

struct BookingAppointmentModel: Codable, Identifiable, Equatable, Hashable {
    let id: UUID = .init()
    let userName, vendorName, vendorPhone, vendorEmail: String
    let vendorLogo, serviceDate, serviceTime, paymentMethod: String
    let bookingText: String?
    let paymentStatus, price: String
    let status, vendorID: Int
    let address: String
    let distance, remindme, bookID, cancel: Int?
    let orderDetails: [OrderDetail]
    let services: [VendorDetailsModel.Service]?
    let calendarSchedule:Int?
    
    
    

    var serviceDateFormattedAsString: String? {
        serviceDate.dateFormatter(inputFormat: "yyyy-MM-dd", outputFormat: "yyyy-MM-dd")
    }

    var serviceDateFormattedAsDate: Date {
        serviceDate.toDate(inputFormate: "yyyy-MM-dd")
    }

    var canBookingCancel: Bool { cancel == 1 }

    enum CodingKeys: String, CodingKey {
        case userName = "user_name"
        case vendorName = "vendor_name"
        case vendorPhone = "vendor_phone"
        case vendorEmail = "vendor_email"
        case vendorLogo = "vendor_logo"
        case serviceDate = "service_date"
        case serviceTime = "service_time"
        case paymentMethod = "payment_method"
        case paymentStatus = "payment_status"
        case price, status
        case vendorID = "vendor_id"
        case address, distance, remindme, cancel, services
        case bookID = "book_id"
        case orderDetails = "order_details"
        case bookingText = "booking_text"
        case calendarSchedule = "calendarschedule"
    }

    var isRemindMe: Bool { remindme == 1 }
    var vendorLogoUrl: String { AppConstants.Server.baseURL + vendorLogo }
}

// MARK: - OrderDetail

struct OrderDetail: Codable, Equatable, Hashable, Identifiable {
    let id: UUID = .init()
    let orderCartID: Int
    let serviceName: String
    let serviceID, bookID, userID, vendorID: Int
    let status, price, staffName: String
    let staffID:Int?

    enum CodingKeys: String, CodingKey {
        case orderCartID = "order_cart_id"
        case serviceName = "service_name"
        case serviceID = "service_id"
        case bookID = "book_ID"
        case userID = "user_id"
        case vendorID = "vendor_id"
        case status, price
        case staffName = "staff_name"
        case staffID = "staff_id"
    }
}
