//
//  AppointmentShimmerView.swift
//  Bookme
//
//  Created by Apple on 31/08/2024.
//

import SwiftUI

struct AppointmentShimmerView: View {
    @StateObject var viewModel = AppointmentViewModel()
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
           
            ScrollView(.vertical, showsIndicators: false) {
                LazyVStack {
                    ForEach(0...9, id: \.self) { index in
                        AppointmentUpcomingShimmerCell()
                    }
                }
            }
            .background(ColorConstants.WhiteA700)
        }
        .padding(.top, getRelativeHeight(23.0))
        .shimmerize()
    }
}


struct AppointmentUpcomingShimmerCell: View {
    
    var body: some View {
        VStack {
            HStack {
                Text("\("model"), \("model")")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black900)
                    .fixedSize()
                    .multilineTextAlignment(.leading)
                    
                Spacer()
                Toggle(StringConstants.kLblRemindMe, isOn: .constant(false))
                
                .toggleStyle(SwitchToggleStyle(tint: ColorConstants.Cyan800))
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.semibold)
                .foregroundColor(ColorConstants.Cyan800B2)
                .frame(width: 130)
                .fixedSize()
                .multilineTextAlignment(.leading)
               
                
            }
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(18.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(15.0))
            .padding(.horizontal, getRelativeWidth(14.0))
            Divider()
                .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, getRelativeHeight(9.0))
                .padding(.horizontal, getRelativeWidth(14.0))

            HStack(alignment: .top, spacing: 16.0.relativeWidth) {
                NetworkImageView(path:nil)
                   
                    .frame(width: getRelativeWidth(72.0), height: getRelativeWidth(74.0),
                           alignment: .leading)
             
                    .clipShape(Circle())
                    
                VStack(alignment: .leading, spacing: 4.0.relativeHeight) {
                    Text(String(repeating: "model.vendorName", count: 1))
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
//                        .fixedSize(horizontal: /*@START_MENU_TOKEN@*/true/*@END_MENU_TOKEN@*/, vertical: false)
//                        .fixedSize()
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                     
                        
                    Text(String(repeating: "model.address", count: 6))
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900B2)
                        
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(225.0),
                               alignment: .leading)
                    
                    
                    HStack{
                        
                        Image("location_pin")
                            .resizable()
                            .scaledToFit()
                            .frame(width: getRelativeWidth(11.0), height: getRelativeHeight(11.0),
                                   alignment: .leading)
                        Text("\(0) KM")
                            .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                            .fontWeight(.semibold)
                            .foregroundColor(ColorConstants.Black900B2)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(17.0),
                                   alignment: .leading)
                           
                       
                            
                           
                    }
                }
            }

           
            .padding(.top, getRelativeHeight(13.0))
            .padding(.horizontal, getRelativeWidth(14.0))
            
            Divider()
                .frame(height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, -1)
                .padding(.horizontal, getRelativeWidth(14.0))
            
            HStack {
                Button(action: {
                   
                }, label: {
                    HStack(spacing: 0) {
                        Text(StringConstants.kLblCancelModify)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(14.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                    bottomRight: 17.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(Color.clear.opacity(0.7)))
                    }
                })
                Spacer()
                Button(action: {
                   
                }, label: {
                    HStack(spacing: 0) {
                        Text(StringConstants.kLblReceipt)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                    }
                })
                
               
            }
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(35.0),
                   alignment: .leading)
            .padding(.vertical, getRelativeHeight(19.0))
            .padding(.horizontal, getRelativeWidth(14.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0, bottomRight: 20.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                   bottomRight: 20.0)
                .fill(ColorConstants.WhiteA700))
        .hideNavigationBar()
    }
}


#Preview {
    AppointmentShimmerView().attachAllEnvironmentObjects()
        .background(ColorConstants.bgGradient)
}

