import SwiftUI

struct AppointmentView: View {
    @StateObject var viewModel = AppointmentViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var appState: AppState
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @Namespace private var animation
   
    var body: some View {
        GeometryReader { _ in
//            let size = proxy.size
            SuperView(pageState: $viewModel.pageState) {} content: {
                VStack(spacing: 0) {
                    CustomNavigationBar()
                    VStack(alignment: .leading, spacing: 0) {
                        VStack(spacing: 0) {
                            HStack(alignment: .top, spacing: 24.0.relativeWidth) {
                                ForEach(AppointmentHeaderType.allCases, id: \.self) { item in
                                    VStack {
                                        Button(action: {
                                            viewModel.updateHeaderList(item)
                                        }, label: {
                                            HStack(alignment: .top, spacing: 2) {
                                                //                                                Spacer()
                                                VStack(spacing: 2) {
                                                    Text(item.title)
                                                        .font(FontScheme
                                                            .kNunitoMedium(size: 14.0.relativeFontSize))
                                                        .fontWeight(.medium)
                                                        .foregroundColor(viewModel.checkHeader(item) ? Color.white : Color.init(hex: "#6D7273"))
                                                        
                                                        .scaleEffect(viewModel.checkHeader(item) ? 1.1 : 1.0, anchor: .bottom)
                                                    if viewModel.checkHeader(item) {
                                                        Divider()
                                                            .frame(
                                                                width: CGFloat(item.rawValue.count * 8), height: getRelativeHeight(2.0), alignment: .center)
                                                            .background(ColorConstants.Cyan800)
                                                            //                                                            .padding(.horizontal)
                                                            .matchedGeometryEffect(id: "header.divider", in: animation)
                                                    }
                                                }
                                                //                                                Spacer()
                                            }
                                        })
                                    }
                                           
                                    .frame(maxWidth: .infinity)
                                }
                            }
                            .animation(.bouncy, value: viewModel.appointmentHeaderType)
                            .frame(maxWidth: .infinity)
                               
                            Divider()
                                .frame(
                                    height: getRelativeHeight(2.0), alignment: .leading)
                                .background(ColorConstants.Cyan800)
                        }
                        
                        if viewModel.pageState == .loading(true) {
                            AppointmentShimmerView()
                                .background(ColorConstants.WhiteA700)
                        } else {
                            ScrollView(.vertical, showsIndicators: false) {
                                VStack(alignment: .leading, spacing: 0) {
                                    VStack {
                                        switch viewModel.appointmentHeaderType {
                                        case .scheduled:
                                                
                                            LazyVStack {
                                                if viewModel.bookingAppointmentList.isEmpty {
                                                    CustomPlaceholder(placeholderType: .noAppointments, title: "Scheduled Appointments", subTitle: "Discover and book services near you.\nYour scheduled appointments will show up here.")
                                                        
                                                } else {
                                                    ForEach(viewModel.bookingAppointmentList) { model in
                                                        AppointmentUpcomingCell(model: model)
                                                    }
                                                }
                                            }
                                            .transition(.backslide)
                                        case .completed:
                                            LazyVStack {
                                                if viewModel.bookingAppointmentList.isEmpty {
                                                    CustomPlaceholder(placeholderType: .noAppointments, title: "Completed Appointments", subTitle: " Your completed appointments will show up here.")
                                                       
                                                } else {
                                                    ForEach(viewModel.bookingAppointmentList) { model in
                                                        AppointmentCompletedCell(model: model) {
                                                            withAnimation(.bouncy) {
                                                                viewModel.updatePopUpView(model)
                                                            }
                                                        }
                                                    }
                                                }
                                                
                                            }.transition(.backslide)
                                        case .cancelled:
                                            LazyVStack {
                                                if viewModel.bookingAppointmentList.isEmpty {
                                                    CustomPlaceholder(placeholderType: .noAppointments, title: "Cancelled Appointments", subTitle: "Your cancelled appointments will show up here.")
                                                        
                                                } else {
                                                    ForEach(viewModel.bookingAppointmentList) { model in
                                                        AppointmentCancelledCell(model: model)
                                                    }
                                                }
                                                
                                            }.transition(.backslide)
                                        }
                                    }
                                    .padding(.vertical)
                                }
                            }
                            .scrollDisabled(viewModel.bookingAppointmentList.isEmpty)
                            // .frame(width: size.width, height: size.height, alignment: .top)
                            .background(ColorConstants.WhiteA700)
                            .background(ColorConstants.bgGradient)
                        }
                    }
                    .padding(.top, getRelativeHeight(23.0))
                }
                .overlay(alignment: .bottom) {
                    if let model = viewModel.selectedBookingAppointmentModel {
                        Group {
                            ZStack {}
                                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
                                .background(ColorConstants.Black90066)
                                .transition(.opacity)
                            
                         
                            WriteReviewView(bookingAppointmentModel: model, onSubmit: { value, completion in
                                if let value = value {
                                    viewModel.addReview(value) {
                                        completion?()
                                        
                                    }
                                } else {
                                    
                                    viewModel.updatePopUpView(nil)
                                }
                            })
                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
                            .transition(.move(edge: .bottom))
                            .ignoresSafeArea(.container, edges: .bottom)
                            
                        }
                    }
                }
                .overlay(content: {
                    if viewModel.isAddReviewLoading {
                        ActivityLoaderView()
                    }
                })
       
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
                .onAppear {
                    viewModel.getAppointments(value: viewModel.appointmentHeaderType)
                }
            }
            .id(appState.bookingAppointmentViewID)
            .onAppear(perform: viewModel.onAppear)
        }
     
        .onChange(of: routerManager.appointmentRouteList) { _, newValue in
           
            appState.updateTabBarHidden(!newValue.isEmpty)
        }
        .handleDeepLinkNavigation(routesType: .appointmentRoute) 
    }
}

struct AppoinmentView_Previews: PreviewProvider {
    static var previews: some View {
        AppointmentView().attachAllEnvironmentObjects()
            .background(ColorConstants.bgGradient)
    }
}
