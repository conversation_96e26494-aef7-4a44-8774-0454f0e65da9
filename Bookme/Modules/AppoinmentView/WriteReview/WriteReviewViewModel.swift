//
//  WriteReviewViewModel.swift
//  Bookme
//
//  Created by Apple on 02/04/2024.
//

import SwiftUI

// var setInitialPage:ReviewPageType{ .last }

class WriteReviewViewModel: ObservableObject {
    @Published var reviewPageType: ReviewPageType = .first

    @Published var reviewText: String = ""
    
    @Published var starRating: Double = 0
    let maxAvailableStar: Int = 5
    @Published var selectReview: [String] = []
    
    let preMadeReviewList: [String] = [
        "Great Experience",
        "Wonderful Staff",
        "Very Professional",
        "Good Job",
        "Bad Experience"
    ]
    
    func onReviewSelection(_ value: String) {
        if selectReview.contains(value) {
            selectReview.removeAll { $0 == value }
        }
        else {
            selectReview.append(value)
        }
    }
    
    func resetAll() {
        starRating = 0
        reviewPageType = .first
        selectReview = []
    }
    
    func updateReviewPageType(_ value: ReviewPageType) {
        withAnimation {
            reviewPageType = value
        }
    }
}

class WriteReportViewModel: ObservableObject {
    @Published var reviewPageType: ReportPageType = .first
    @Published var reviewText: String = ""
    
    func resetAll() {
        reviewPageType = .first
        reviewText = .init()
    }
    
    func updateReviewPageType(_ value: ReportPageType) {
        withAnimation {
            reviewPageType = value
        }
    }
}
