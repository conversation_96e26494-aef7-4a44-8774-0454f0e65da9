//
//  WriteReviewView.swift
//  Bookme
//
//  Created by Apple on 02/04/2024.
//

import SwiftUI
import WrappingHStack

struct WriteReviewView: View {
    let bookingAppointmentModel: BookingAppointmentModel
    let onSubmit: (_ model: AddReviewRequest?, _ onDone: (() -> Void)?) -> Void
    @StateObject private var viewModel: WriteReviewViewModel = .init()
    @Namespace private var animation
    var body: some View {
        VStack(alignment: .center, spacing: 0) {
            if viewModel.reviewPageType != .last { headerView }
            switch viewModel.reviewPageType {
            case .first: firstPageView
            case .second: secondPageView
            // case .third: thirdPageView
            case .fourth: fourthPageView
            case .last: lastPageView
            }
        }
        .frame(maxWidth: .infinity)
        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0).fill(ColorConstants.WhiteA700))
        .clipped()
        .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
        .padding(.bottom, AppConstants.tabBarHeightWithPadding)
        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0).fill(ColorConstants.WhiteA700))
    }

    var headerView: some View {
        Group {
            HStack {
                Text("Write A Review")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black900)
                    .fixedSize()
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity)
                    .padding(.leading, 20)
                
                Button(action: {
                    onSubmit(nil, nil)
                    viewModel.resetAll()
                    
                }, label: {
                    Image("img_closeroundlig")
                        .resizable()
                        .scaledToFit()
                        .frame(width: getRelativeWidth(20.0), height: getRelativeWidth(20.0),
                               alignment: .center)
                })
            }
            
            .padding(.vertical)
            .padding(.horizontal, getRelativeWidth(16.0))
            
            Divider()
                .frame(height: getRelativeHeight(1.0),
                       alignment: .center)
                .background(ColorConstants.Cyan8003f)
                .padding(.bottom, getRelativeWidth(8.0))
                .padding(.horizontal, getRelativeWidth(21.0))
        }
    }

    var firstPageView: some View {
        VStack(alignment: .center, spacing: 0) {
            NetworkImageView(path: bookingAppointmentModel.vendorLogoUrl)
                .frame(width: getRelativeWidth(58.0), height: getRelativeWidth(58.0),
                       alignment: .center)
                .scaledToFit()
                .clipShape(Circle())
                .padding(.vertical, getRelativeHeight(8.0))
                .padding(.horizontal, getRelativeWidth(21.0))
                .transaction { transaction in
                    transaction.animation = nil
                }
            Text(bookingAppointmentModel.vendorName)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(176.0), height: getRelativeHeight(20.0),
                       alignment: .topLeading)
                .padding(.horizontal, getRelativeWidth(21.0))
            //            HStack(spacing: 4.0.relativeWidth) {
            //                Image("img_vector_yellow_a700")
            //                    .resizable()
            //                    .frame(width: getRelativeWidth(9.0), height: getRelativeWidth(9.0),
            //                           alignment: .center)
            //                    .scaledToFit()
            //                    .clipped()
            //
            //                Text(StringConstants.kMsg421kReview)
            //                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(11.0)))
            //                    .fontWeight(.regular)
            //                    .foregroundColor(ColorConstants.WhiteA700)
            //                    .minimumScaleFactor(0.5)
            //                    .multilineTextAlignment(.leading)
            //                    .frame(width: getRelativeWidth(90.0), height: getRelativeHeight(16.0),
            //                           alignment: .center)
            //            }
            //            .frame(width: getRelativeWidth(138.0), height: getRelativeHeight(24.0),
            //                   alignment: .center)
            //            .background(RoundedCorners(topLeft: 12.0, topRight: 12.0, bottomLeft: 12.0,
            //                                       bottomRight: 12.0)
            //                    .fill(ColorConstants.Cyan800))
            //            .padding(.top, getRelativeHeight(12.0))
            //            .padding(.horizontal, getRelativeWidth(21.0))
            VStack(alignment: .center, spacing: 0) {
                Text("How would you rate it?")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(162.0), height: getRelativeHeight(20.0),
                           alignment: .center)
                
                RatingBarView(selected: $viewModel.starRating,
                              heightWeightImage: getRelativeWidth(23.0), spaceBetween: 12.0.relativeHeight)
                    .padding(.top, getRelativeHeight(14.0))
            }
            .frame(width: getRelativeWidth(169.0), height: getRelativeHeight(57.0),
                   alignment: .center)
            .padding(.top, getRelativeHeight(24.0))
            .padding(.horizontal, getRelativeWidth(21.0))
            
            Button(action: {
                viewModel.updateReviewPageType(.second)
            }, label: {
                HStack(spacing: 0) {
                    Text("Next")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(7.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(129.0),
                               height: getRelativeHeight(35.0), alignment: .center)
                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                   bottomLeft: 17.5, bottomRight: 17.5)
                                .fill(ColorConstants.Cyan800))
                        .padding(.horizontal, getRelativeWidth(21.0))
                }
            })
            .disableWithOpacity(viewModel.starRating < 1)
            .padding(.vertical, getRelativeHeight(35.0))
            .padding(.horizontal, getRelativeWidth(21.0))
        }
        .background(ColorConstants.WhiteA700)
        .clipped()
        .transition(.backslide)
    }

    var secondPageView: some View {
        VStack(alignment: .center, spacing: 0) {
            VStack(spacing: 0) {
                WrappingHStack(viewModel.preMadeReviewList, id: \.self, alignment: .center, spacing: .dynamic(minSpacing: 8), lineSpacing: 16.0.relativeHeight) { type in
                    
                    Button {
                        viewModel.onReviewSelection(type)
                    } label: {
                        HStack {
                            VStack {
                                ZStack {}
                                    .hideNavigationBar()
                                    .frame(width: getRelativeWidth(13.0),
                                           height: getRelativeWidth(13.0),
                                           alignment: .center)
                                    .background(Circle()
                                        .fill(viewModel.selectReview.contains(type) ? ColorConstants.Cyan800 : ColorConstants.Cyan8003f))
                                    //                                        .if(viewModel.selectReview == type, transform: {
                                    //                                            $0.matchedGeometryEffect(id: "gender.radio", in: animation)
                                    //                                        })
                                
                                    .padding(.all, getRelativeWidth(4.0))
                            }
                            .frame(width: getRelativeWidth(21.0),
                                   height: getRelativeWidth(21.0), alignment: .center)
                            .overlay(Circle()
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                            .background(Circle()
                                .fill(Color.clear.opacity(0.7)))
                            .padding(.leading, getRelativeWidth(9.0))
                            Text(LocalizedStringKey(type))
                                .font(FontScheme
                                    .kNunitoMedium(size: getRelativeHeight(14.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Black900)
                                .fixedSize()
                                .multilineTextAlignment(.leading)
                                .frame(
                                    height: getRelativeHeight(20.0),
                                    alignment: .topLeading)
                            
                                .padding(.trailing, getRelativeWidth(32.0))
                        }
                        .frame(
                            width: 160,
                            height: getRelativeHeight(33.0),
                            
                            alignment: .leading)
                        //                                        .background(.orange)
                    }
                }
                .animation(.easeInOut, value: viewModel.selectReview)
                .padding(.top, getRelativeHeight(14.0))
                //                                .background(.orange)
            }
            .padding(.horizontal, getRelativeWidth(16.0))
            
            Button(action: {
                viewModel.updateReviewPageType(.fourth)
            }, label: {
                HStack(spacing: 0) {
                    Text("Add Additional Notes")
                        .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.semibold)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(7.0))
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(221.0),
                               height: getRelativeHeight(36.0), alignment: .center)
                        .overlay(Capsule()
                            .stroke(ColorConstants.Cyan800,
                                    lineWidth: 1))
                        .background(RoundedCorners(topLeft: 16.0, topRight: 16.0,
                                                   bottomLeft: 16.0, bottomRight: 16.0)
                                .fill(Color.clear.opacity(0.7)))
                        .padding(.top, getRelativeHeight(13.0))
                        .padding(.horizontal, getRelativeWidth(21.0))
                }
            })
            
            HStack {
                Button(action: {
                    viewModel.updateReviewPageType(.first)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Back")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                    bottomLeft: 17.5, bottomRight: 17.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(Color.clear.opacity(0.7)))
                    }
                })
                
                Spacer()
                Button(action: {
                    //                    viewModel.updateReviewPageType(.last)
                    onSubmit(.init(rating: viewModel.starRating, quickReview: viewModel.selectReview.joined(separator: ","), review: viewModel.reviewText, vendorID: bookingAppointmentModel.vendorID)) {
                        viewModel.updateReviewPageType(.last)
                    }
                }, label: {
                    HStack(spacing: 0) {
                        Text("Submit")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                    }
                }).disableWithOpacity(viewModel.selectReview.isEmpty)
            }
            .frame(width: getRelativeWidth(347.0), height: getRelativeHeight(35.0),
                   alignment: .center)
            
            .padding(.top, getRelativeHeight(26.0))
            .padding(.horizontal, getRelativeWidth(16.0))
        }
        .padding(.bottom, getRelativeHeight(35.0))
        .background(ColorConstants.WhiteA700)
        .clipped()
        .transition(.backslide)
    }

    //  var thirdPageView: some View {
    //        VStack(alignment: .center, spacing: 0) {
    //            VStack(alignment: .leading, spacing: 0) {
    //                Text(StringConstants.kMsgTitleYourRevi)
    //                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
    //                    .fontWeight(.bold)
    //                    .foregroundColor(ColorConstants.Cyan800)
    //                    .minimumScaleFactor(0.5)
    //                    .multilineTextAlignment(.leading)
    //                    .frame(width: getRelativeWidth(118.0), height: getRelativeHeight(20.0),
    //                           alignment: .topLeading)
    //                    .padding(.trailing)
    //                HStack {
    //                    TextField("", text: $viewModel.rectangleeightytwoText)
    //                        .padding()
    //                }
    //                .frame(width: getRelativeWidth(332.0), height: getRelativeHeight(50.0),
    //                       alignment: .center)
    //                .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
    //                                        bottomRight: 13.0)
    //                        .stroke(ColorConstants.Cyan800,
    //                                lineWidth: 1))
    //                .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
    //                                           bottomRight: 13.0)
    //                        .fill(ColorConstants.WhiteA7003f))
    //                .padding(.top, getRelativeHeight(6.0))
    //            }
    //            .frame(width: getRelativeWidth(333.0), height: getRelativeHeight(76.0),
    //                   alignment: .center)
    //            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
    //                                       bottomRight: 13.0).fill(.clear))
    //            .padding(.top, getRelativeHeight(15.0))
    //            .padding(.horizontal, getRelativeWidth(21.0))
    //            VStack(alignment: .leading, spacing: 0) {
    //                Text(StringConstants.kMsgShareAVideoO)
    //                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
    //                    .fontWeight(.bold)
    //                    .foregroundColor(ColorConstants.Cyan800)
    //                    .minimumScaleFactor(0.5)
    //                    .multilineTextAlignment(.leading)
    //                    .frame(width: getRelativeWidth(158.0), height: getRelativeHeight(20.0),
    //                           alignment: .topLeading)
    //
    //
    //                ZStack {
    //                    Button(action: {
    //
    //                    }, label: {
    //                        Image("img_cameralight")
    //                            .resizable()
    //                            .scaledToFit()
    //                            .frame(width: getRelativeWidth(33.0), height: getRelativeHeight(33.0),
    //                                   alignment: .center)
    //
    //                            .clipped()
    //
    //                            .padding(.leading, getRelativeWidth(152.38))
    //                            .padding(.trailing, getRelativeWidth(146.62))
    //
    //
    //                    })
    //                }
    //                .hideNavigationBar()
    //                .frame( height: getRelativeHeight(83.0),
    //                       alignment: .center)
    //                .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
    //                                        bottomRight: 13.0)
    //                        .stroke(style: StrokeStyle(lineWidth: 1, dash: [2, 2])))
    //                .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
    //                                           bottomRight: 13.0)
    //                        .fill(ColorConstants.WhiteA700))
    //                .padding(.top, getRelativeHeight(8.0))
    //
    //            }
    //            .padding(.top, getRelativeHeight(15.0))
    //
    //
    //
    //
    //            HStack {
    //                Button(action: {
    //                    viewModel.updateReviewPageType(.second)
    //                }, label: {
    //                    HStack(spacing: 0) {
    //                        Text(StringConstants.kLblBack)
    //                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
    //                            .fontWeight(.bold)
    //                            .padding(.horizontal, getRelativeWidth(30.0))
    //                            .padding(.vertical, getRelativeHeight(7.0))
    //                            .foregroundColor(ColorConstants.Cyan800)
    //                            .minimumScaleFactor(0.5)
    //                            .multilineTextAlignment(.center)
    //                            .frame(width: getRelativeWidth(129.0),
    //                                   height: getRelativeHeight(35.0), alignment: .center)
    //                            .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
    //                                                    bottomLeft: 17.5, bottomRight: 17.5)
    //                                    .stroke(ColorConstants.Cyan800,
    //                                            lineWidth: 1))
    //                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
    //                                                       bottomLeft: 17.5, bottomRight: 17.5)
    //                                    .fill(Color.clear.opacity(0.7)))
    //                    }
    //                })
    //                .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
    //                       alignment: .center)
    //                .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
    //                                        bottomRight: 17.5)
    //                        .stroke(ColorConstants.Cyan800,
    //                                lineWidth: 1))
    //                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
    //                                           bottomRight: 17.5)
    //                        .fill(Color.clear.opacity(0.7)))
    //
    //                Spacer()
    //                Button(action: {
    //                    viewModel.updateReviewPageType(.fourth)
    //                }, label: {
    //                    HStack(spacing: 0) {
    //                        Text(StringConstants.kLblNext)
    //                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
    //                            .fontWeight(.bold)
    //                            .padding(.horizontal, getRelativeWidth(30.0))
    //                            .padding(.vertical, getRelativeHeight(7.0))
    //                            .foregroundColor(ColorConstants.WhiteA700)
    //                            .minimumScaleFactor(0.5)
    //                            .multilineTextAlignment(.center)
    //                            .frame(width: getRelativeWidth(129.0),
    //                                   height: getRelativeHeight(35.0), alignment: .center)
    //                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
    //                                                       bottomLeft: 17.5, bottomRight: 17.5)
    //                                    .fill(ColorConstants.Cyan800))
    //                    }
    //                })
    //
    //                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
    //                                           bottomRight: 17.5)
    //                        .fill(ColorConstants.Cyan800))
    //            }
    //            .frame(width: getRelativeWidth(347.0), height: getRelativeHeight(35.0),
    //                   alignment: .center)
    //            .padding(.top, getRelativeHeight(18.0))
    //            .padding(.bottom, getRelativeHeight(20.0))
    //
    //        }
    //        .padding(.horizontal, getRelativeWidth(21.0))
    //        .transition(.backslide)
    //    }
    var fourthPageView: some View {
        VStack(alignment: .trailing, spacing: 0) {
            VStack(alignment: .leading, spacing: 0) {
                Text("Write your review")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(125.0), height: getRelativeHeight(20.0),
                           alignment: .topLeading)
                    .padding(.trailing)
                VStack {
                    TextField("What did you like or dislike?", text: $viewModel.reviewText, axis: .vertical)
                        .lineLimit(5 ... 5)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .padding(.vertical, getRelativeHeight(14.0))
                        .padding(.horizontal, getRelativeWidth(13.0))
                }
                
                .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                        bottomRight: 13.0)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                           bottomRight: 13.0)
                        .fill(ColorConstants.WhiteA7003f))
                .padding(.top, getRelativeHeight(8.0))
            }
            .frame(width: getRelativeWidth(343.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                       bottomRight: 13.0).fill(.clear))
            .padding(.top, getRelativeHeight(17.0))
            .padding(.horizontal, getRelativeWidth(21.0))
            HStack {
                Button(action: {
                    viewModel.updateReviewPageType(.second)
                }, label: {
                    HStack(spacing: 0) {
                        Text("Back")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                    bottomLeft: 17.5, bottomRight: 17.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(Color.clear.opacity(0.7)))
                    }
                })
                .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                        bottomRight: 17.5)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                           bottomRight: 17.5)
                        .fill(Color.clear.opacity(0.7)))
                Spacer()
                Button(action: {
                    onSubmit(.init(rating: viewModel.starRating, quickReview: viewModel.selectReview.joined(separator: ","), review: viewModel.reviewText, vendorID: bookingAppointmentModel.vendorID)) {
                        viewModel.updateReviewPageType(.last)
                    }
                }, label: {
                    HStack(spacing: 0) {
                        Text("Submit")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                    }
                })
                .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                           bottomRight: 17.5)
                        .fill(ColorConstants.Cyan800))
                .disableWithOpacity(viewModel.reviewText.isEmpty)
            }
            .frame(width: getRelativeWidth(347.0), height: getRelativeHeight(35.0),
                   alignment: .center)
            .padding(.vertical, getRelativeHeight(32.0))
            .padding(.top, getRelativeHeight(32.0))
            .padding(.horizontal, getRelativeWidth(21.0))
        }.transition(.backslide)
    }

    var lastPageView: some View {
        VStack(alignment: .center, spacing: 0) {
            Image("img_vector402")
                .frame(width: getRelativeWidth(55.0), height: getRelativeWidth(55.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 27.5, topRight: 27.5, bottomLeft: 27.5,
                                           bottomRight: 27.5)
                        .fill(ColorConstants.Cyan800))
                .padding(.top, getRelativeHeight(64.0))
                .padding(.horizontal, getRelativeWidth(104.0))
            Text("Success")
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(52.0), height: getRelativeHeight(20.0),
                       alignment: .topLeading)
                .padding(.top, getRelativeHeight(8.0))
                .padding(.horizontal, getRelativeWidth(104.0))
            Text("Your review was sent successfully")
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .foregroundColor(ColorConstants.Black900B2)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(182.0), height: getRelativeHeight(17.0),
                       alignment: .topLeading)
                .padding(.top, getRelativeHeight(11.0))
                .padding(.horizontal, getRelativeWidth(104.0))
            Button(action: {
                //                onSubmit(.init(rating: viewModel.starRating, review: viewModel.reviewText, vendorID: bookingAppointmentModel.vendorID))
                onSubmit(nil, nil)
                viewModel.resetAll()
            }, label: {
                HStack(spacing: 0) {
                    Text("Done")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(7.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(179.0),
                               height: getRelativeHeight(35.0), alignment: .center)
                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                   bottomLeft: 17.5, bottomRight: 17.5)
                                .fill(ColorConstants.Cyan800))
                        .padding(.vertical, getRelativeHeight(82.0))
                        .padding(.horizontal, getRelativeWidth(104.0))
                }
            })
            .frame(width: getRelativeWidth(179.0), height: getRelativeHeight(35.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                       bottomRight: 17.5)
                    .fill(ColorConstants.Cyan800))
            .padding(.top, getRelativeHeight(82.0))
            .padding(.bottom, getRelativeHeight(32.0))
            .padding(.horizontal, getRelativeWidth(104.0))
        }
        .transition(.backslide)
    }
}

struct WriteReportView: View {
    let reviewModel: VendorDetailsModel.Review
    let onSubmit: (_ model: VendorDetailsModel.Review?, _ isComplete: Bool, _ onDone: (() -> Void)?) -> Void
    @StateObject private var viewModel: WriteReportViewModel = .init()
    @Namespace private var animation
    @FocusState private var isTextFieldFocused: Bool
    @EnvironmentObject private var appState: AppState
    var body: some View {
        VStack(alignment: .center, spacing: 0) {
            if viewModel.reviewPageType != .last { headerView }
            switch viewModel.reviewPageType {
            case .first: firstPageView
            case .last: lastPageView
            }
        }
        .frame(maxWidth: .infinity)
        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0).fill(ColorConstants.WhiteA700))
        .clipped()
        .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
        .padding(.bottom, appState.isCustomBottomBarHidden ? 0 : AppConstants.tabBarHeightWithPadding * 1.9)
        .background(RoundedCorners(topLeft: 28.0, topRight: 28.0).fill(ColorConstants.WhiteA700))
    }

    var headerView: some View {
        Group {
            HStack {
                Text("Report A Review")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black900)
                    .fixedSize()
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity)
                    .padding(.leading, 20)
                
                Button(action: {
                    onSubmit(nil, false, nil)
                    viewModel.resetAll()
                    
                }, label: {
                    Image("img_closeroundlig")
                        .resizable()
                        .scaledToFit()
                        .frame(width: getRelativeWidth(20.0), height: getRelativeWidth(20.0),
                               alignment: .center)
                })
            }
            
            .padding(.vertical)
            .padding(.horizontal, getRelativeWidth(16.0))
            
            Divider()
                .frame(height: getRelativeHeight(1.0),
                       alignment: .center)
                .background(ColorConstants.Cyan8003f)
                .padding(.bottom, getRelativeWidth(8.0))
                .padding(.horizontal, getRelativeWidth(21.0))
        }
    }

    var firstPageView: some View {
        VStack(alignment: .trailing, spacing: 0) {
            VStack(alignment: .leading, spacing: 0) {
                Text("Write your review report")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .multilineTextAlignment(.leading)
                    .frame(height: getRelativeHeight(20.0),
                           alignment: .topLeading)
                    .padding(.trailing)
                VStack {
                    TextField("What did you like to report on this review?", text: $viewModel.reviewText, axis: .vertical)
                        .lineLimit(5 ... 5)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .padding(.vertical, getRelativeHeight(14.0))
                        .padding(.horizontal, getRelativeWidth(13.0))
                        .focused($isTextFieldFocused) // Binds focus state
                        .onChange(of: isTextFieldFocused) { _, newValue in
                            appState.updateCustomBottomBarHidden(newValue)
                        }
                }
                
                .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                        bottomRight: 13.0)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                           bottomRight: 13.0)
                        .fill(ColorConstants.WhiteA7003f))
                .padding(.top, getRelativeHeight(8.0))
            }
            .frame(width: getRelativeWidth(343.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                       bottomRight: 13.0).fill(.clear))
            .padding(.top, getRelativeHeight(17.0))
            .padding(.horizontal, getRelativeWidth(21.0))
            HStack {
                Button(action: {
                    var model = reviewModel
                    model.reportDescription = viewModel.reviewText
                    onSubmit(model, false) {
                        viewModel.updateReviewPageType(.last)
                    }
                }, label: {
                    HStack(spacing: 0) {
                        Text("Submit")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0),
                                   height: getRelativeHeight(35.0), alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                    }
                })
                .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                           bottomRight: 17.5)
                        .fill(ColorConstants.Cyan800))
                .disableWithOpacity(viewModel.reviewText.isEmpty)
            }
            .frame(width: getRelativeWidth(347.0), height: getRelativeHeight(35.0),
                   alignment: .center)
            .padding(.vertical, getRelativeHeight(32.0))
            .padding(.top, getRelativeHeight(32.0))
            .padding(.horizontal, getRelativeWidth(21.0))
        }.transition(.backslide)
    }

    var lastPageView: some View {
        VStack(alignment: .center, spacing: 0) {
            Image("img_vector402")
                .frame(width: getRelativeWidth(55.0), height: getRelativeWidth(55.0),
                       alignment: .center)
                .background(Circle()
                    .fill(ColorConstants.Cyan800))
                .padding(.top, getRelativeHeight(64.0))
                .padding(.horizontal, getRelativeWidth(104.0))
            Text("Success")
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(52.0), height: getRelativeHeight(20.0),
                       alignment: .topLeading)
                .padding(.top, getRelativeHeight(8.0))
                .padding(.horizontal, getRelativeWidth(104.0))
            Text("Your report was sent successfully")
                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                .fontWeight(.regular)
                .foregroundColor(ColorConstants.Black900B2)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(182.0), height: getRelativeHeight(17.0),
                       alignment: .topLeading)
                .padding(.top, getRelativeHeight(11.0))
                .padding(.horizontal, getRelativeWidth(104.0))
            Button(action: {
                onSubmit(nil, true, nil)
                viewModel.resetAll()
            }, label: {
                HStack(spacing: 0) {
                    Text("Done")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(7.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(179.0),
                               height: getRelativeHeight(35.0), alignment: .center)
                        .background(Capsule()
                            .fill(ColorConstants.Cyan800))
                        .padding(.vertical, getRelativeHeight(82.0))
                        .padding(.horizontal, getRelativeWidth(104.0))
                }
            })
            .frame(width: getRelativeWidth(179.0), height: getRelativeHeight(35.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                       bottomRight: 17.5)
                    .fill(ColorConstants.Cyan800))
            .padding(.top, getRelativeHeight(82.0))
            .padding(.bottom, getRelativeHeight(32.0))
            .padding(.horizontal, getRelativeWidth(104.0))
        }
        .transition(.backslide)
    }
}

