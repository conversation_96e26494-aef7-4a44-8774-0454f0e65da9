import SwiftUI

struct FAQView: View {
    @StateObject var viewModel = FAQViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        SuperView(pageState: $viewModel.pageState) {} content: {
            if viewModel.pageState == .loading(true) {
                FAQShimmerView()
                    .shimmerize()
                        
            } else {
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(alignment: .leading, spacing: 20.0.relativeHeight) {
                        ForEach(Array(viewModel.faqList.enumerated()), id: \.element.id) { index, item in
                        
                            VStack(spacing: 0) {
                                Button(action: {
                                    withAnimation(.bouncy) {
                                        viewModel.faqList[index].isExpanded.toggle()
                                    }
                                    
                                }, label: {
                                    HStack(alignment: .center) {
                                        Text(String(repeating: item.question, count: 1))
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.Cyan800)
                                            .multilineTextAlignment(.leading)
                                            
                                        Spacer()
                                        Image("img_arrowup")
                                            .resizable()
                                            .scaledToFit()
                                            .rotationEffect(.degrees(item.isExpanded ? 180 : 0))
                                            .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                                   alignment: .center)
                                    }
                                    .padding(.vertical, getRelativeHeight(16.0))
                                    .padding(.horizontal, getRelativeWidth(16.0))
                                    .clipped()
                                    
                                })
                                
                                VStack(spacing: 0) {
                                    Divider()
                                        .frame(width: getRelativeWidth(321.0), height: getRelativeHeight(1.0),
                                               alignment: .center)
                                        .background(ColorConstants.Cyan8007f)
                                        .padding(.horizontal, getRelativeWidth(16.0))
                                    VStack {
                                        CustomRichText(html: item.answer, fontFamily: "Nunito-Regular", fontSrc: "NunitoRegular.ttf", sizeAdjust: "80", fontColor: ColorConstants.Cyan800, lineHeight: 110.0.relativeFontSize)
                                            .clipped()
                                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                            .fontWeight(.regular)
                                            .foregroundColor(ColorConstants.Cyan800B2)
                                            .multilineTextAlignment(.leading)
                                            .padding(getRelativeWidth(16.0))
                                            .disabled(true)
                                    }
                                }
                                .frame(height: item.isExpanded ? nil : 0)
                                .clipped()
                            }
                           
                            .clipped()
                            .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
                                                    bottomRight: 15.0)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
                                                       bottomRight: 15.0)
                                    .fill(ColorConstants.Cyan8003f))
                        }
                    }
                    .padding(.vertical, 16.0.relativeHeight)
                    .padding(.horizontal, 16.0.relativeHeight)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
    }
}

struct HelpCenterView_Previews: PreviewProvider {
    static var previews: some View {
        FAQView()
            .background(ColorConstants.bgGradient)
    }
}

struct FAQShimmerView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 20.0.relativeHeight) {
            ForEach(0 ... 6, id: \.self) { _ in
            
                VStack(spacing: 0) {
                    Button(action: {}, label: {
                        HStack(alignment: .center) {
                            Text(String(repeating: "item.question", count: 2))
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .multilineTextAlignment(.leading)
                                
                            Spacer()
                            Image("img_arrowup")
                                .resizable()
                                .scaledToFit()
                                .rotationEffect(.degrees(0))
                                .frame(width: getRelativeWidth(12.0), height: getRelativeHeight(12.0),
                                       alignment: .center)
                        }
                        .padding(.vertical, getRelativeHeight(16.0))
                        .padding(.horizontal, getRelativeWidth(16.0))
                        .clipped()
                        
                    })
                    
                    VStack(spacing: 0) {
                        Divider()
                            .frame(width: getRelativeWidth(321.0), height: getRelativeHeight(1.0),
                                   alignment: .center)
                            .background(ColorConstants.Cyan8007f)
                            .padding(.horizontal, getRelativeWidth(16.0))
                    }
                    
                    .clipped()
                }
               
                .clipped()
                .overlay(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
                                        bottomRight: 15.0)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 15.0, topRight: 15.0, bottomLeft: 15.0,
                                           bottomRight: 15.0)
                        .fill(ColorConstants.Cyan8003f))
            }
        }
        .padding(.vertical, 16.0.relativeHeight)
        .padding(.horizontal, 16.0.relativeHeight)
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
    }
}
