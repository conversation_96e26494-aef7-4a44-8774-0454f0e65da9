//
//  FAQModel.swift
//  Bookme
//
//  Created by Apple on 06/04/2024.
//

import Foundation


struct FAQSampleModel: Identifiable {
    let id: UUID = .init()
    let question, answer:String
    var image:String?
    var isExpanded:Bool = false
    
}



// MARK: - FAQModel
struct FAQModel: Codable, Identifiable {
    let id:UUID = .init()
    let faqID: Int
    let question, answer: String
    var isExpanded:Bool = false

    enum CodingKeys: String, CodingKey {
        case faqID = "Faq_ID"
        case question = "Question"
        case answer = "Answer"
    }
}

