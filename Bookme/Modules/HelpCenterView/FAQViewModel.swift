import Foundation
import SwiftUI

class FAQViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var faqList:[FAQModel] = []
    
    override init(){
        super.init()
        
        
        getFaqData()
     }
    
    func getFaqData(){
        
        
        
        onApiCall(api.faqs, parameters: emptyDictionary) {
            self.faqList = $0.data ?? []
        }
    }
}
