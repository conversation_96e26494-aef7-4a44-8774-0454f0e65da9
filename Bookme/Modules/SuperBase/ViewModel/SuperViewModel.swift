//
//  SuperViewModel.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//

import Alamofire
import Foundation
import SwiftUI

//@MainActor
class SuperViewModel: ObservableObject {
    @Published var pageState: PageState = .stable
    var cancellables: Cancellables = []
    let api: RepositoriesAPIProtocol = RepositoriesAPI.shared
    init() {
        print("SuperViewModel class is initialized")
        
        if let token = UserDefaultsSecure.sharedInstance.getGeneratedToken() {
            print("token:", token, terminator: "\n\n\n")
        }
    }
    
    deinit {
        print("SuperViewModel class is deinitialized")
    }
    
    func isUnAuthenticateErrorOccurred(_ error: String) -> Bool {
        error.contains(AppConstants.unAuthenticateErrorString) ||
            error.contains(AppConstants.unAuthenticateErrorStringSecond)
    }
    
    func updatePageState(_ state: PageState) { pageState = state }
    
    func handleApiCallFailure(_ error: String) { updatePageState(.failure(error: error)) }
    
    func onApiCallFailure(error: String, customLoadingBinding: Binding<Bool>?) {
        handleApiCallFailure(error)
        if let customLoadingBinding = customLoadingBinding {customLoadingBinding.wrappedValue = false}
    }
    
    func onApiCallSuccess<T>(response: ApiBaseModel<T>, customLoadingBinding: Binding<Bool>?) {
//        if response.data is [Any] {
//            let dataArray = response.data as! [Any]
//            
//            if dataArray.isEmpty {
//                updatePageState(.noData)
//            }else{
//                updatePageState(.stable)
//            }
//            
//        } else {
//            updatePageState(.stable)
//        }
        
        updatePageState(.stable)
        if let customLoadingBinding = customLoadingBinding {customLoadingBinding.wrappedValue = false}
    }
    
    func onApiCall<T, R>(_ execution: (_ request: R, _ completionHandler: @escaping (Result<CommonApiResponse<T>, AFError>) -> Void) -> Void, parameters: R, dismissKeyboard: Bool = false, withStateChange: Bool = true, hideClientSideError: Bool = false, withLoadingIndicator: Bool = true, customLoadingBinding: Binding<Bool>? = nil, onSuccess: @escaping (CommonApiResponse<T>) -> Void, onFailure: TypeCallback<String>? = nil) {
       
        if withLoadingIndicator {
            updatePageState(.loading(withStateChange && withLoadingIndicator))
        }
       
        
        if let customLoadingBinding = customLoadingBinding {customLoadingBinding.wrappedValue = true}
        
        
        if dismissKeyboard { Utilities.dismissKeyboard() }
        execution(parameters) { result in
            switch result {
            case let .success(result):
                if result.error {
                    onFailure?(result.message)
                    
                    if let customLoadingBinding = customLoadingBinding {customLoadingBinding.wrappedValue = false}
                    
                    if withStateChange && !hideClientSideError {
                        self.onApiCallFailure(error: result.message, customLoadingBinding: customLoadingBinding)
                    }
                    return
                }
                if withStateChange {
                    self.onApiCallSuccess(response: result, customLoadingBinding: customLoadingBinding)
                }
                onSuccess(result)
            case let .failure(error):
                onFailure?(error.localizedDescription)
                if withStateChange {
                    self.onApiCallFailure(error: error.localizedDescription, customLoadingBinding: customLoadingBinding)
                }
            }
        }
    }
}
