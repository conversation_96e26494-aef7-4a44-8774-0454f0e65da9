//
//  SuperModel.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//

import Foundation
import SwiftUI

struct VoidStruct: Codable {}

protocol SuperApiResponse<T>: Codable {
    associatedtype T: Codable
    var status: String { get }
    var message: String { get }
    var data: T { get }
}

struct ApiBaseModel<T: Codable>: SuperApiResponse {
    var status: String

    var message: String

    var data: T?

    var error: Bool { status != "1" && status != "success" }

    var success: Bool { status == "1" || status == "success" }

    enum CodingKeys: String, CodingKey {
        case status, message, data
    }

    enum SecondCodingKeys: String, CodingKey {
        case status, message, vendors
    }

    enum ThirdCodingKeys: String, CodingKey {
        case status, message
        case timeSlot = "time_slot"
    }

    enum FourthCodingKeys: String, CodingKey {
        case status, message, services
    }

    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        let valueSecond = try decoder.container(keyedBy: SecondCodingKeys.self)
        let valueThird = try decoder.container(keyedBy: ThirdCodingKeys.self)
        let valueFourth = try decoder.container(keyedBy: FourthCodingKeys.self)
        status = try values.decode(String.self, forKey: .status)
        message = try values.decode(String.self, forKey: .message)
        data = try values.decodeIfPresent(T.self, forKey: .data)
            ?? valueSecond.decodeIfPresent(T.self, forKey: .vendors)
            ?? valueThird.decodeIfPresent(T.self, forKey: .timeSlot)
            ?? valueFourth.decodeIfPresent(T.self, forKey: .services)
    }
}

struct AlertConfig: Equatable {
    static func == (lhs: AlertConfig, rhs: AlertConfig) -> Bool {
        return lhs.title == rhs.title &&
            lhs.text == rhs.text &&
            lhs.alertType == rhs.alertType &&
            lhs.cancelButtonText == rhs.cancelButtonText &&
            lhs.okButtonText == rhs.okButtonText
    }

    let title, text: String
    let cancelButtonText, okButtonText: String
    let alertType: AlertType
    let onCancel: VoidCallback?
    let onOk: VoidCallback?

    init(title: String, text: String, cancelButtonText: String = "Cancel", okButtonText: String = "Ok", alertType: AlertType = .alert, onCancel: VoidCallback? = nil, onOk: VoidCallback? = nil) {
        self.title = title
        self.text = text
        self.cancelButtonText = cancelButtonText
        self.okButtonText = okButtonText
        self.alertType = alertType
        self.onCancel = onCancel
        self.onOk = onOk
    }
}

enum AlertType {
    case alert, choiceAlert
}

enum PageState: Equatable {
    static func == (lhs: PageState, rhs: PageState) -> Bool {
        switch (lhs, rhs) {
        case (.stable, .stable),
             (.loading, .loading),
             (.success, .success),
           
             (.failure, .failure),
             (.message, .message),
             (.custom, .custom):
            return true
        default:
            return false
        }
    }

    case stable, loading(_ load: Bool = true), success(message: String, onDone: (() -> Void)? = nil), failure(title:String? = nil ,error: String), message(config: AlertConfig), custom(view: any View)
}
