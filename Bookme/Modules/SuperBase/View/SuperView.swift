//
//  SuperView.swift
//  Bookme
//
//  Created by Apple on 25/04/2024.
//

import SwiftUI

struct SuperView<Content: View, LoadingContent: View>: View {
    let content: Content
    let loadingView: LoadingContent
    let customLoadingView: Bool
    @Binding private var pageState: PageState
    
    @EnvironmentObject private var appState: AppState
    init(pageState: Binding<PageState>, isCustomLoading: Bool = true, @ViewBuilder loadingView: () -> LoadingContent, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.loadingView = loadingView()
        self._pageState = pageState
        self.customLoadingView = isCustomLoading
    }

    
    @ViewBuilder
    var body: some View {
        content
            .overlay {
                switch pageState {
                case .stable, .success:
                    EmptyView()
                case .loading(let isLoading):
                    if isLoading && !customLoadingView {
                        VStack {}
                            .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                                   alignment: .center)
//                            .background(.ultraThinMaterial)
                            .edgesIgnoringSafeArea(.all)
                    }
           
                default:
                    VStack {}
                        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                               alignment: .center)
                        .background(.ultraThinMaterial)
                        .edgesIgnoringSafeArea(.all)
                }
            }
//            .hideNavigationBar(pageState != .stable)
            .overlay(alignment: .center) {
                switch pageState {
                case .loading(let loading):
                    if loading {
                        if customLoadingView {
                            loadingView
                        } else {
                            ActivityLoaderView()
                        }
                    }
                case .message(let config):
                    AlertView(pageState: $pageState, config: config)
                        .transition(.slide)
                case .failure(let title,let error):
                    AlertView(pageState: $pageState, config: .init(title: title ?? "Error!", text: error))
                        .padding(.horizontal)
                        .transition(.slide)
                case .custom(let view):
                    AnyView(view)
                        .transition(.move(edge: .leading))
                default:
                    EmptyView()
                }
            }
//            .hideNavigationBar(pageState != .stable)
//            .onChange(of: pageState, initial: true) { oldValue, newValue in
//                appState.hideTabBar = pageState != .stable
//            }
    }
}

extension SuperView where LoadingContent == EmptyView {
    init(pageState: Binding<PageState>, @ViewBuilder content: () -> Content) {
        self.init(pageState: pageState, isCustomLoading: false, loadingView: { EmptyView() }, content: content)
    }
}
