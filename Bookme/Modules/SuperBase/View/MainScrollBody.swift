//
//  MainScrollBody.swift
//  Bookme
//
//  Created by Apple on 05/03/2024.
//

import SwiftUI

struct MainScrollBody<Content, TrailingContent>: View where Content: View, TrailingContent: View {
    let content: Content
    let trailingContent: TrailingContent
    let backButtonWithTitle: LocalizedStringKey?
    let enableScrollView: Bool
    let hideNavigationBar: Bool
    let invertColor: Bool

    @Environment(RouterManager.self) private var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    @Environment(\.dismiss) private var dismiss

//    @State private var invertColor:Bool

    init(invertColor: Bool = true, backButtonWithTitle: LocalizedStringKey? = nil, hideNavigationBar: Bool = false, enableScroll: Bool = true, @ViewBuilder trailingContent: () -> TrailingContent, @ViewBuilder content: () -> Content) {
        self.enableScrollView = enableScroll

        self.hideNavigationBar = hideNavigationBar
        self.trailingContent = trailingContent()
        self.content = content()
        self.backButtonWithTitle = backButtonWithTitle
        self.invertColor = invertColor
    }
    
    @State private var offset: CGFloat = 0

    var body: some View {
        if self.enableScrollView {
            
            ScrollViewWithOffset(.vertical, showsIndicators: false) { 
                self.offset = $0.y
            } content: {
                self.content
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .if(!self.hideNavigationBar, transform: {
                        $0.logoNavigationBar(invertColor: invertColor ? true : offset < 0, trailingContent: self.trailingContent, appState: _appState, router: _routerManager, onDismiss: self.dismiss, backButtonWithTitle: self.backButtonWithTitle)
                    })
                    .toolbarBackground(.thinMaterial, for: .navigationBar)
                    .toolbarBackground(.automatic, for: .navigationBar)
                    .hideNavigationBar(false)
            }

        } else {
            self.content
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .if(!self.hideNavigationBar, transform: {
                    $0.logoNavigationBar(invertColor: invertColor ? true : offset < 0, trailingContent: self.trailingContent, appState: _appState, router: _routerManager, onDismiss: self.dismiss, backButtonWithTitle: self.backButtonWithTitle)
                })
                .toolbarBackground(.thinMaterial, for: .navigationBar)
                .toolbarBackground(.automatic, for: .navigationBar)
                .hideNavigationBar(false)
        }
    }
}

extension MainScrollBody where TrailingContent == EmptyView {
    init(invertColor: Bool = true, backButtonWithTitle: LocalizedStringKey? = nil, hideNavigationBar: Bool = false, enableScroll: Bool = true, @ViewBuilder content: () -> Content) {
        self.init(invertColor: invertColor, backButtonWithTitle: backButtonWithTitle, hideNavigationBar: hideNavigationBar, enableScroll: enableScroll, trailingContent: { EmptyView() }, content: content)
    }
}

extension View {
    func logoNavigationBar<Content: View>(invertColor: Bool, trailingContent: Content, appState: EnvironmentObject<AppState>, router: Environment<RouterManager>, onDismiss: DismissAction? = nil, backButtonWithTitle: LocalizedStringKey?) -> some View {
        toolbar {
            if let backButtonWithTitle = backButtonWithTitle {
                ToolbarItem(placement: .topBarLeading) {
                    HStack(spacing: getRelativeWidth(15.0)) {
                        Button(action: {
                            onDismiss?()
                        }, label: {
                           
                            Image(.imgArrowleftWhiteA700)
                                .renderingMode(.template)
                                .resizable()
                                .scaledToFit()
                                .rotateBasedOnLanguage()
                                .frame(width: getRelativeWidth(18.0),
                                       height: getRelativeHeight(18.0), alignment: .center)

                        })

                        Text(backButtonWithTitle)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundStyle(invertColor ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
                            .multilineTextAlignment(.leading)
                            .frame(
                                height: getRelativeHeight(22.0), alignment: .topLeading
                            )
                    }
                    .animation(.easeInOut, value: invertColor)
                    
                }
            } else {
                
              
                    if  checkCurrentRouteEmpty(appState: appState, router: router) {
                        let routeType = mapRouterWithTab(appState: appState)
                        ToolbarItem(placement: .topBarLeading) {
                            Button {
                                appState.wrappedValue.showSideMenu.toggle()
                            } label: {
                                Image(.imgVector)
                                    .renderingMode(.template)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(18.0),
                                           height: getRelativeHeight(18.0), alignment: .center)

                                   
                            }

                            .transition(.slide)
                        }

                        ToolbarItem(placement: .principal) {
                           
                            Image(.imgLogocolor1)
                                .renderingMode(.template)
                                .resizable()
                                .scaledToFit()
                                .frame(width: getRelativeWidth(23.0),
                                       height: getRelativeHeight(23.0), alignment: .center)
                                
                        }

                        ToolbarItem(placement: .topBarTrailing) {
                            Button {
                                router.wrappedValue.push(to: .notification, where: routeType)
                            } label: {
                                //                            Image(.imgSearch)

                                Image(.imgNotification)
                                    .renderingMode(.template)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(20.0),
                                           height: getRelativeHeight(20.0), alignment: .center)

                                    .clipped()
                                    
                            }
                        }

                    } else {
                        ToolbarItem(placement: .topBarLeading) {
                            Button {
                                withAnimation {
                                    onDismiss?()
                                }

                            } label: {
                                
                                Image(.imgArrowleftWhiteA700)
                                    .renderingMode(.template)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(18.0),
                                           height: getRelativeHeight(18.0), alignment: .center)
                                  
                                //                            .rotateOnLanguage()
                            }

                            .transition(.slide)
                        }
                    }
                
                
               
            }

            ToolbarItem(placement: .topBarTrailing) {
                trailingContent
                    
            }
        }
        
        .tint(invertColor ? ColorConstants.Cyan900 : ColorConstants.WhiteA700)
    }

  
}




 func checkCurrentRouteEmpty(appState: EnvironmentObject<AppState>, router: Environment<RouterManager>) -> Bool {
    switch appState.wrappedValue.selectedTab {
    case .home:
        return router.wrappedValue.homeRouteList.isEmpty
    case .explore:
        return router.wrappedValue.exploreRouteList.isEmpty
    case .appointment:
        return router.wrappedValue.appointmentRouteList.isEmpty
    case .myAccount:
        return router.wrappedValue.myAccountRouteList.isEmpty
    }
}

 func mapRouterWithTab(appState: EnvironmentObject<AppState>) -> RoutesType {
    switch appState.wrappedValue.selectedTab {
    case .home:
        return .homeRoute
    case .explore:
        return .exploreRoute
    case .appointment:
        return .appointmentRoute
    case .myAccount:
        return .myAccountRoute
    }
}

enum ScrollOffsetNamespace {
    static let namespace = "scrollView"
}

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGPoint = .zero
    static func reduce(value: inout CGPoint, nextValue: () -> CGPoint) {}
}

struct ScrollViewOffsetTracker: View {
    var body: some View {
        GeometryReader { geo in
            Color.clear
                .preference(
                    key: ScrollOffsetPreferenceKey.self,
                    value: geo.frame(in: .named(ScrollOffsetNamespace.namespace)).origin
                )
        }
        .frame(height: 0)
    }
}

private extension ScrollView {
    func withOffsetTracking(action: @escaping (_ offset: CGPoint) -> Void) -> some View {
        self.coordinateSpace(name: ScrollOffsetNamespace.namespace)
            .onPreferenceChange(ScrollOffsetPreferenceKey.self, perform: action)
    }
}

public struct ScrollViewWithOffset<Content: View>: View {
    public init(
        _ axes: Axis.Set = .vertical,
        showsIndicators: Bool = true,
        onScroll: ScrollAction? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.axes = axes
        self.showsIndicators = showsIndicators
        self.onScroll = onScroll ?? { _ in }
        self.content = content
    }

    private let axes: Axis.Set
    private let showsIndicators: Bool
    private let onScroll: ScrollAction
    private let content: () -> Content

    public typealias ScrollAction = (_ offset: CGPoint) -> Void

    public var body: some View {
        ScrollView(self.axes, showsIndicators: self.showsIndicators) {
            ZStack(alignment: .top) {
                ScrollViewOffsetTracker()
                self.content()
            }
        }
        .withOffsetTracking(action: self.onScroll)
    }
}




/// A custom view modifier to rotate a view based on the app's language.
struct LanguageBasedRotation: ViewModifier {
    let inverse: Bool
    @EnvironmentObject private var appState: AppState
    func body(content: Content) -> some View {
        content.rotationEffect(inverse ? appState.appLanguage == .en ? .degrees(-180) : .degrees(0)   : appState.appLanguage == .ar ? .degrees(-180) : .degrees(0))
    }
}



extension View {
    /// Rotates the view based on the app's language direction.
    /// - Returns: A rotated view based on the app's language.
    func rotateBasedOnLanguage(inverse: Bool = false) -> some View {
        self.modifier(LanguageBasedRotation(inverse: inverse))
    }
}
