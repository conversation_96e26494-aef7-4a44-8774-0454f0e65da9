import SwiftUI

struct WelcomeView: View {
    @StateObject var welcomeViewModel = WelcomeViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        NavigationView {
            ZStack(alignment: .bottomTrailing) {
                VStack {
                    ZStack(alignment: .center) {
                        Image("img_76925131")
                            .resizable()
                            .frame(width: getRelativeWidth(359.0), height: getRelativeWidth(359.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                        ZStack {}
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(2.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 174.5, topRight: 174.5,
                                                       bottomLeft: 174.5, bottomRight: 174.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.top, getRelativeHeight(313.22))
                            .padding(.trailing, getRelativeWidth(9.0))
                    }
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(359.0), height: getRelativeWidth(359.0),
                           alignment: .center)
                    .padding(.leading, getRelativeWidth(19.0))
                    .padding(.trailing, getRelativeWidth(12.0))
                    Text(StringConstants.kMsgDiscoverNearby)
                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(28.0)))
                        .fontWeight(.heavy)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(311.0), height: getRelativeHeight(71.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(19.0))
                        .padding(.horizontal, getRelativeWidth(29.0))
                    Text(StringConstants.kMsgUnlockTheBeau)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(16.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(279.0), height: getRelativeHeight(49.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(35.0))
                        .padding(.horizontal, getRelativeWidth(42.0))
                    PageIndicator(numPages: 3, currentPage: .constant(1),
                                  selectedColor: ColorConstants.Cyan800,
                                  unSelectedColor: ColorConstants.Cyan8003f, spacing: 4.0)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(ColorConstants.bgGradient)
               
                FabButton(action: {
                   
                }, backgroundColor: ColorConstants.Cyan800, image: "img_arrowright",
                frameInfo: (Double(getRelativeWidth(45.0)),
                            Double(getRelativeHeight(45.0))))
                    .frame(width: getRelativeWidth(45.0), height: getRelativeWidth(45.0),
                           alignment: .bottomTrailing)
                    .background(Circle()
                            .fill(ColorConstants.Cyan800))
                    .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                    .padding()
                
                
            }
          
           
        }
       
    }
}

struct WelcomeView_Previews: PreviewProvider {
    static var previews: some View {
        WelcomeView()
    }
}
