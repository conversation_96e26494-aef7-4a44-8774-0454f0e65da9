import SwiftUI

struct Appoinmentitem2Cell: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(StringConstants.kMsgNov222023)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(121.0), height: getRelativeHeight(17.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(15.0))
                .padding(.horizontal, getRelativeWidth(17.0))
            Divider()
                .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(1.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan8004c)
                .padding(.top, getRelativeHeight(10.0))
                .padding(.horizontal, getRelativeWidth(14.0))
            ZStack(alignment: .topTrailing) {
                Image("img_rectangle10")
                    .resizable()
                    .frame(width: getRelativeWidth(72.0), height: getRelativeWidth(74.0),
                           alignment: .leading)
                    .scaledToFit()
                    .clipShape(Circle())
                    .clipShape(Circle())
                    .padding(.bottom, getRelativeHeight(14.35))
                    .padding(.trailing, getRelativeWidth(256.0))
                VStack(alignment: .leading, spacing: 0) {
                    Text(StringConstants.kMsgBroadwayBarber)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(174.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                        .padding(.trailing)
                    Text(StringConstants.kMsg4thFloorAlZ)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900B2)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(33.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(225.0), height: getRelativeHeight(56.0),
                       alignment: .leading)
                .padding(.bottom, getRelativeHeight(37.0))
                .padding(.leading, getRelativeWidth(89.87))
                Text(StringConstants.kLbl6Km)
                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                    .fontWeight(.semibold)
                    .foregroundColor(ColorConstants.Black900B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(25.0), height: getRelativeHeight(17.0),
                           alignment: .leading)
                    .padding(.top, getRelativeHeight(61.92))
                    .padding(.trailing, getRelativeWidth(195.5))
                Image("img_map_cyan_800")
                    .resizable()
                    .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(30.0),
                           alignment: .leading)
                    .scaledToFit()
                    .padding(.top, getRelativeHeight(64.04))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(328.0), height: getRelativeHeight(93.0),
                   alignment: .leading)
            .padding(.top, getRelativeHeight(13.0))
            .padding(.horizontal, getRelativeWidth(14.0))
            Button(action: {}, label: {
                HStack(spacing: 0) {
                    Text(StringConstants.kLblReBook)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .padding(.horizontal, getRelativeWidth(30.0))
                        .padding(.vertical, getRelativeHeight(7.0))
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(200.0), height: getRelativeHeight(35.0),
                               alignment: .center)
                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                   bottomRight: 17.5)
                                .fill(ColorConstants.Cyan800))
                        .padding(.vertical, getRelativeHeight(19.0))
                        .padding(.horizontal, getRelativeWidth(14.0))
                }
            })
            .frame(width: getRelativeWidth(200.0), height: getRelativeHeight(35.0),
                   alignment: .center)
            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                       bottomRight: 17.5)
                    .fill(ColorConstants.Cyan800))
            .padding(.vertical, getRelativeHeight(19.0))
            .padding(.horizontal, getRelativeWidth(14.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0, bottomRight: 20.0)
            .stroke(ColorConstants.Cyan800,
                    lineWidth: 1))
        .background(RoundedCorners(topLeft: 20.0, topRight: 20.0, bottomLeft: 20.0,
                                   bottomRight: 20.0)
                .fill(ColorConstants.WhiteA700))
        .hideNavigationBar()
    }
}

struct Appoinmentitem2Cell_Previews: PreviewProvider {
    static var previews: some View {
        Appoinmentitem2Cell()
    }
}
