import SwiftUI

struct AppoinmentcancelledView: View {
    @StateObject var appoinmentcancelledViewModel = AppoinmentcancelledViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            VStack {
                HStack {
                    HStack {
                        HStack {
                            Image("img_arrowleft_white_a700")
                                .resizable()
                                .frame(width: getRelativeWidth(16.0),
                                       height: getRelativeHeight(12.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                                .onTapGesture {
                                    self.presentationMode.wrappedValue.dismiss()
                                }
                            Text("Appointments")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(103.0),
                                       height: getRelativeHeight(22.0), alignment: .topLeading)
                                .padding(.leading, getRelativeWidth(20.0))
                        }
                        .frame(width: getRelativeWidth(139.0), height: getRelativeHeight(22.0),
                               alignment: .center)
                        Spacer()
                        Image("img_notification")
                            .resizable()
                            .frame(width: getRelativeWidth(20.0), height: getRelativeHeight(19.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                    }
                    .frame(width: getRelativeWidth(346.0), height: getRelativeHeight(22.0),
                           alignment: .leading)
                }
                .frame(width: getRelativeWidth(346.0), height: getRelativeHeight(22.0),
                       alignment: .leading)
                .padding(.horizontal, getRelativeWidth(22.0))
            }
            .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(22.0),
                   alignment: .leading)
            VStack(alignment: .leading, spacing: 0) {
                ScrollView(.vertical, showsIndicators: false) {
                    ZStack(alignment: .leading) {
                        VStack {
                            VStack {
                                Text(StringConstants.kMsgUpcoming)
                                    .font(FontScheme
                                        .kNeusaNextStdMedium(size: getRelativeHeight(14.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(351.0),
                                           height: getRelativeHeight(18.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(19.0))
                            }
                            .frame(width: UIScreen.main.bounds.width,
                                   height: getRelativeHeight(28.0), alignment: .leading)
                            .overlay(RoundedCorners().stroke(ColorConstants.Cyan800, lineWidth: 1))
                            .background(RoundedCorners().fill(Color.clear.opacity(0.7)))
                            Divider()
                                .frame(width: getRelativeWidth(62.0),
                                       height: getRelativeHeight(4.0), alignment: .trailing)
                                .background(ColorConstants.Cyan800)
                                .padding(.horizontal, getRelativeWidth(20.0))
                        }
                        .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(32.0),
                               alignment: .topLeading)
                        .padding(.bottom, getRelativeHeight(826.0))
                        VStack(spacing: 0) {
                            ScrollView(.vertical, showsIndicators: false) {
                                LazyVStack {
                                    ForEach(0 ... 2, id: \.self) { index in
                                        Appoinmentitem2Cell()
                                    }
                                }
                            }
                        }
                        .frame(width: UIScreen.main.bounds.width, alignment: .leading)
                        .background(ColorConstants.WhiteA700)
                    }
                    .hideNavigationBar()
                    .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                           alignment: .topLeading)
                }
                Text("tabbar")
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(60.0),
                           alignment: .leading)
                    .background(ColorConstants.Cyan800)
                    .shadow(color: ColorConstants.Black90019, radius: 5, x: 0, y: -4)
            }
            .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                   alignment: .topLeading)
        }
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(LinearGradient(gradient: Gradient(colors: [ColorConstants.Cyan900,
                                                               ColorConstants.Black901]),
            startPoint: .topLeading, endPoint: .bottomTrailing))
        .hideNavigationBar()
    }
}

struct AppoinmentcancelledView_Previews: PreviewProvider {
    static var previews: some View {
        AppoinmentcancelledView()
    }
}
