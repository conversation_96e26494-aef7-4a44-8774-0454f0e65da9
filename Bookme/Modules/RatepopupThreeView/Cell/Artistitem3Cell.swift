import SwiftUI

struct Artistitem3Cell: View {
    var body: some View {
        VStack {
            Image("img_rectangle81")
                .resizable()
                .frame(width: getRelativeWidth(104.0), height: getRelativeWidth(106.0),
                       alignment: .leading)
                .scaledToFit()
                .clipShape(Circle())
                .clipShape(Circle())
            Text(StringConstants.kLblLily)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(21.0), height: getRelativeHeight(18.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(11.0))
                .padding(.horizontal, getRelativeWidth(37.0))
            Text(StringConstants.kLblHairStylist)
                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(11.0)))
                .fontWeight(.medium)
                .foregroundColor(ColorConstants.Cyan800B2)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(60.0), height: getRelativeHeight(16.0),
                       alignment: .leading)
                .padding(.horizontal, getRelativeWidth(17.0))
        }
        .frame(width: getRelativeWidth(104.0), alignment: .leading)
        .background(RoundedCorners(topLeft: 53.0, topRight: 53.0, bottomLeft: 53.0,
                                   bottomRight: 53.0))
        .hideNavigationBar()
    }
}

/* struct Artistitem3Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Artistitem3Cell()
 }
 } */
