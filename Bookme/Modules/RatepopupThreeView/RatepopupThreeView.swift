import SwiftUI

struct RatepopupThreeView: View {
    @StateObject var ratepopupThreeViewModel = RatepopupThreeViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            ZStack(alignment: .center) {
                VStack(alignment: .leading, spacing: 0) {
                    VStack(alignment: .leading, spacing: 0) {
                        HStack {
                            Text(StringConstants.kLblPopularArtist)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(124.0),
                                       height: getRelativeHeight(25.0), alignment: .topLeading)
                            Spacer()
                            Text(StringConstants.kLblSeeAll)
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Cyan800B2)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(41.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                                .padding(.bottom, getRelativeHeight(7.0))
                                .padding(.leading, getRelativeWidth(186.0))
                        }
                        .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(25.0),
                               alignment: .leading)
                        .padding(.trailing)
                        HStack(spacing: 0) {
                            ScrollView(.horizontal, showsIndicators: false) {
                                LazyHStack {
                                    ForEach(0 ... 3, id: \.self) { index in
                                        Artistitem3Cell()
                                    }
                                }
                            }
                        }
                        .frame(width: getRelativeWidth(377.0), alignment: .leading)
                        .padding(.top, getRelativeHeight(20.0))
                        .padding(.trailing, getRelativeWidth(10.0))
                    }
                    .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(203.0),
                           alignment: .trailing)
                    .background(RoundedCorners(topLeft: 57.0, topRight: 57.0, bottomLeft: 57.0,
                                               bottomRight: 57.0))
                    .padding(.top, getRelativeHeight(25.0))
                    ZStack(alignment: .bottomLeading) {
                        FSPagerViewSUI($ratepopupThreeViewModel.sliderfortyCurrentPage,
                                       ratepopupThreeViewModel.sliderData) { item in
                            VStack(alignment: .leading, spacing: 0) {
                                Text(StringConstants.kLbl40)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(20.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(51.0),
                                           height: getRelativeHeight(28.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(17.0))
                                    .padding(.horizontal, getRelativeWidth(16.0))
                                Text(StringConstants.kMsgVoucherForYou)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(16.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(146.0),
                                           height: getRelativeHeight(43.0), alignment: .topLeading)
                                    .padding(.vertical, getRelativeHeight(10.0))
                                    .padding(.horizontal, getRelativeWidth(16.0))
                            }
                            .frame(width: getRelativeWidth(241.0), height: getRelativeHeight(158.0))
                            .background(RoundedCorners(topLeft: 23.0, topRight: 6.0,
                                                       bottomLeft: 23.0, bottomRight: 6.0)
                                    .fill(ColorConstants.Cyan80019))
                            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 1, y: 1)
                            .padding(.trailing, getRelativeWidth(119.0))
                        }
                        VStack {
                            VStack {
                                Text(StringConstants.kLblBookNow)
                                    .font(FontScheme
                                        .kNunitoExtraBold(size: getRelativeHeight(13.0)))
                                    .fontWeight(.heavy)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(62.0),
                                           height: getRelativeHeight(18.0), alignment: .topLeading)
                                    .padding(.vertical, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(12.0))
                            }
                            .frame(width: getRelativeWidth(86.0), height: getRelativeHeight(31.0),
                                   alignment: .leading)
                            .background(RoundedCorners(topLeft: 15.5, topRight: 15.5,
                                                       bottomLeft: 15.5, bottomRight: 15.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.trailing, getRelativeWidth(10.0))
                            PageIndicator(numPages: ratepopupThreeViewModel.sliderData.count,
                                          currentPage: $ratepopupThreeViewModel
                                              .sliderfortyCurrentPage,
                                          selectedColor: ColorConstants.Cyan800,
                                          unSelectedColor: ColorConstants.Cyan8003f,
                                          spacing: 8.0)
                        }
                        .frame(width: getRelativeWidth(186.0), height: getRelativeHeight(63.0),
                               alignment: .bottomLeading)
                        .padding(.top, getRelativeHeight(112.0))
                        .padding(.trailing, getRelativeWidth(157.81))
                    }
                    .hideNavigationBar()
                    .frame(width: getRelativeWidth(361.0), height: getRelativeHeight(175.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(26.0))
                    .padding(.horizontal, getRelativeWidth(10.0))
                    HStack {
                        Text(StringConstants.kLblNearestSalon)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(121.0), height: getRelativeHeight(25.0),
                                   alignment: .topLeading)
                        Spacer()
                        Text(StringConstants.kLblSeeAll)
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.Cyan800B2)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(41.0), height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.vertical, getRelativeHeight(1.0))
                    }
                    .frame(width: getRelativeWidth(359.0), height: getRelativeHeight(25.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(30.0))
                    .padding(.horizontal, getRelativeWidth(10.0))
                    VStack(spacing: 0) {
                        ScrollView(.vertical, showsIndicators: false) {
                            LazyVStack {
                                ForEach(0 ... 3, id: \.self) { index in
                                    Rowrectangleten4Cell()
                                }
                            }
                        }
                    }
                    .frame(width: getRelativeWidth(363.0), alignment: .center)
                    .padding(.vertical, getRelativeHeight(18.0))
                    .padding(.horizontal, getRelativeWidth(10.0))
                }
                .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                       alignment: .topLeading)
                .background(ColorConstants.WhiteA700)
                HStack {
                    VStack {
                        HStack {
                            Image("img_vector")
                                .resizable()
                                .frame(width: getRelativeWidth(18.0),
                                       height: getRelativeHeight(17.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                                .padding(.vertical, getRelativeHeight(14.0))
                            Spacer()
                            Image("img_logocolor1")
                                .resizable()
                                .frame(width: getRelativeWidth(23.0),
                                       height: getRelativeHeight(32.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                            Spacer()
                            Image("img_notification")
                                .resizable()
                                .frame(width: getRelativeWidth(20.0),
                                       height: getRelativeHeight(19.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                                .padding(.top, getRelativeHeight(13.0))
                        }
                        .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(32.0),
                               alignment: .center)
                        .padding(.trailing, getRelativeWidth(12.0))
                        HStack {
                            HStack {
                                Image("img_search")
                                    .resizable()
                                    .frame(width: getRelativeWidth(18.0),
                                           height: getRelativeWidth(18.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                    .padding(.leading, getRelativeWidth(9.0))
                                Text(StringConstants.kMsgSearchSalonS)
                                    .font(FontScheme
                                        .kMontserratMedium(size: getRelativeHeight(13.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.Cyan901)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(151.0),
                                           height: getRelativeHeight(16.0), alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(16.0))
                                    .padding(.trailing, getRelativeWidth(85.0))
                            }
                            .frame(width: getRelativeWidth(279.0), height: getRelativeHeight(45.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 10.0, topRight: 10.0, bottomLeft: 10.0,
                                                    bottomRight: 10.0)
                                    .stroke(ColorConstants.Cyan901,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 10.0, topRight: 10.0,
                                                       bottomLeft: 10.0, bottomRight: 10.0)
                                    .fill(ColorConstants.Teal90093))
                            Spacer()
                            ZStack {
                                Image("img_filter")
                                    .resizable()
                                    .frame(width: getRelativeWidth(24.0),
                                           height: getRelativeHeight(17.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                    .padding(.vertical, getRelativeHeight(14.0))
                                    .padding(.horizontal, getRelativeWidth(14.0))
                            }
                            .hideNavigationBar()
                            .frame(width: getRelativeWidth(50.0), height: getRelativeHeight(45.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 10.0, topRight: 10.0,
                                                       bottomLeft: 10.0, bottomRight: 10.0)
                                    .fill(ColorConstants.Cyan800))
                            .shadow(color: ColorConstants.Teal900E8, radius: 3, x: 0, y: 1)
                        }
                        .frame(width: getRelativeWidth(356.0), height: getRelativeHeight(45.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(20.0))
                        .padding(.trailing, getRelativeWidth(7.0))
                    }
                }
                .frame(width: getRelativeWidth(363.0), height: getRelativeHeight(97.0),
                       alignment: .leading)
                .padding(.bottom, getRelativeHeight(1537.78))
                .padding(.leading, getRelativeWidth(17.0))
                .padding(.trailing, getRelativeWidth(10.0))
                VStack(alignment: .leading, spacing: 0) {
                    HStack {
                        Text(StringConstants.kLblCategories)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(87.0), height: getRelativeHeight(25.0),
                                   alignment: .topLeading)
                        Spacer()
                        Text(StringConstants.kLblSeeAll)
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.CyanA400)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(41.0), height: getRelativeHeight(18.0),
                                   alignment: .topLeading)
                            .padding(.vertical, getRelativeHeight(1.0))
                            .padding(.leading, getRelativeWidth(227.0))
                    }
                    .frame(width: getRelativeWidth(355.0), height: getRelativeHeight(25.0),
                           alignment: .leading)
                    .padding(.trailing)
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            VStack {
                                Button(action: {}, label: {
                                    Image("img_group163")
                                })
                                .frame(width: getRelativeWidth(69.0),
                                       height: getRelativeWidth(69.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                        bottomLeft: 34.5, bottomRight: 34.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                           bottomLeft: 34.5, bottomRight: 34.5)
                                        .fill(ColorConstants.Cyan80038))
                                Text(StringConstants.kLblSpa)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(23.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(23.0))
                            }
                            .frame(width: getRelativeWidth(69.0), height: getRelativeHeight(92.0),
                                   alignment: .center)
                            VStack {
                                Button(action: {}, label: {
                                    Image("img_group133")
                                })
                                .frame(width: getRelativeWidth(69.0),
                                       height: getRelativeWidth(69.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                        bottomLeft: 34.5, bottomRight: 34.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                           bottomLeft: 34.5, bottomRight: 34.5)
                                        .fill(ColorConstants.Cyan80038))
                                Text(StringConstants.kLblSalon)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(33.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(5.0))
                                    .padding(.horizontal, getRelativeWidth(18.0))
                            }
                            .frame(width: getRelativeWidth(69.0), height: getRelativeHeight(91.0),
                                   alignment: .center)
                            .padding(.leading, getRelativeWidth(20.0))
                            VStack(alignment: .leading, spacing: 0) {
                                Button(action: {}, label: {
                                    Image("img_group165")
                                })
                                .frame(width: getRelativeWidth(69.0),
                                       height: getRelativeWidth(69.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                        bottomLeft: 34.5, bottomRight: 34.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                           bottomLeft: 34.5, bottomRight: 34.5)
                                        .fill(ColorConstants.Cyan80038))
                                Text(StringConstants.kLblBarbershop)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(68.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(7.0))
                            }
                            .frame(width: getRelativeWidth(69.0), height: getRelativeHeight(93.0),
                                   alignment: .center)
                            .padding(.leading, getRelativeWidth(22.0))
                            VStack(alignment: .leading, spacing: 0) {
                                Button(action: {}, label: {
                                    Image("img_group166")
                                })
                                .frame(width: getRelativeWidth(69.0),
                                       height: getRelativeWidth(69.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                        bottomLeft: 34.5, bottomRight: 34.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                           bottomLeft: 34.5, bottomRight: 34.5)
                                        .fill(ColorConstants.Cyan80038))
                                Text(StringConstants.kLblNailSalon)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(60.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(4.0))
                            }
                            .frame(width: getRelativeWidth(69.0), height: getRelativeHeight(92.0),
                                   alignment: .center)
                            .padding(.leading, getRelativeWidth(14.0))
                            VStack(alignment: .leading, spacing: 0) {
                                Button(action: {}, label: {
                                    Image("img_group167")
                                })
                                .frame(width: getRelativeWidth(69.0),
                                       height: getRelativeWidth(69.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                        bottomLeft: 34.5, bottomRight: 34.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 34.5, topRight: 34.5,
                                                           bottomLeft: 34.5, bottomRight: 34.5)
                                        .fill(ColorConstants.Cyan80038))
                                Text(StringConstants.kLblLifeCoach)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(62.0),
                                           height: getRelativeHeight(17.0), alignment: .center)
                                    .padding(.top, getRelativeHeight(6.0))
                                    .padding(.horizontal, getRelativeWidth(3.0))
                            }
                            .frame(width: getRelativeWidth(69.0), height: getRelativeHeight(92.0),
                                   alignment: .center)
                            .padding(.leading, getRelativeWidth(11.0))
                        }
                        .frame(width: getRelativeWidth(369.0), height: getRelativeHeight(93.0),
                               alignment: .leading)
                    }
                    .padding(.vertical, getRelativeHeight(17.0))
                    .padding(.leading, getRelativeWidth(4.0))
                    .padding(.trailing, getRelativeWidth(10.0))
                }
                .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(140.0),
                       alignment: .topTrailing)
                .padding(.bottom, getRelativeHeight(1369.08))
                ZStack {}
                    .hideNavigationBar()
                    .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(820.0),
                           alignment: .topLeading)
                    .background(ColorConstants.Black90059)
                    .padding(.bottom, getRelativeHeight(825.0))
                VStack(alignment: .trailing, spacing: 0) {
                    HStack {
                        Text(StringConstants.kLblWriteAReview)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(124.0), height: getRelativeHeight(22.0),
                                   alignment: .topLeading)
                        Image("img_closeroundlig")
                            .resizable()
                            .frame(width: getRelativeWidth(12.0), height: getRelativeWidth(12.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                            .padding(.vertical, getRelativeHeight(8.0))
                            .padding(.leading, getRelativeWidth(94.0))
                    }
                    .frame(width: getRelativeWidth(230.0), height: getRelativeHeight(22.0),
                           alignment: .trailing)
                    .padding(.top, getRelativeHeight(17.0))
                    .padding(.horizontal, getRelativeWidth(26.0))
                    Divider()
                        .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(1.0),
                               alignment: .center)
                        .background(ColorConstants.Cyan8003f)
                        .padding(.top, getRelativeHeight(18.0))
                        .padding(.horizontal, getRelativeWidth(21.0))
                    VStack(alignment: .leading, spacing: 0) {
                        Text(StringConstants.kMsgWriteYourRevi)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(125.0), height: getRelativeHeight(20.0),
                                   alignment: .topLeading)
                            .padding(.trailing)
                        VStack {
                            Text(StringConstants.kMsgWhatDidYouLi)
                                .font(FontScheme.kNunitoRegular(size: getRelativeHeight(13.0)))
                                .fontWeight(.regular)
                                .foregroundColor(ColorConstants.Cyan8007f)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(178.0),
                                       height: getRelativeHeight(18.0), alignment: .topLeading)
                                .padding(.vertical, getRelativeHeight(14.0))
                                .padding(.horizontal, getRelativeWidth(13.0))
                        }
                        .frame(width: getRelativeWidth(342.0), height: getRelativeHeight(110.0),
                               alignment: .center)
                        .overlay(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                bottomRight: 13.0)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                                   bottomRight: 13.0)
                                .fill(ColorConstants.WhiteA7003f))
                        .padding(.top, getRelativeHeight(18.0))
                    }
                    .frame(width: getRelativeWidth(343.0), height: getRelativeHeight(148.0),
                           alignment: .center)
                    .background(RoundedCorners(topLeft: 13.0, topRight: 13.0, bottomLeft: 13.0,
                                               bottomRight: 13.0))
                    .padding(.top, getRelativeHeight(18.0))
                    .padding(.horizontal, getRelativeWidth(21.0))
                    HStack {
                        Button(action: {}, label: {
                            HStack(spacing: 0) {
                                Text(StringConstants.kLblBack)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .padding(.horizontal, getRelativeWidth(30.0))
                                    .padding(.vertical, getRelativeHeight(7.0))
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(129.0),
                                           height: getRelativeHeight(35.0), alignment: .center)
                                    .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                            bottomLeft: 17.5, bottomRight: 17.5)
                                            .stroke(ColorConstants.Cyan800,
                                                    lineWidth: 1))
                                    .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                               bottomLeft: 17.5, bottomRight: 17.5)
                                            .fill(Color.clear.opacity(0.7)))
                            }
                        })
                        .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                               alignment: .center)
                        .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                bottomRight: 17.5)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                   bottomRight: 17.5)
                                .fill(Color.clear.opacity(0.7)))
                        Spacer()
                        Button(action: {}, label: {
                            HStack(spacing: 0) {
                                Text(StringConstants.kLblSubmit)
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.bold)
                                    .padding(.horizontal, getRelativeWidth(30.0))
                                    .padding(.vertical, getRelativeHeight(7.0))
                                    .foregroundColor(ColorConstants.WhiteA700)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.center)
                                    .frame(width: getRelativeWidth(129.0),
                                           height: getRelativeHeight(35.0), alignment: .center)
                                    .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                               bottomLeft: 17.5, bottomRight: 17.5)
                                            .fill(ColorConstants.Cyan800))
                            }
                        })
                        .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                               alignment: .center)
                        .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                                   bottomRight: 17.5)
                                .fill(ColorConstants.Cyan800))
                    }
                    .frame(width: getRelativeWidth(341.0), height: getRelativeHeight(35.0),
                           alignment: .center)
                    .padding(.vertical, getRelativeHeight(72.0))
                    .padding(.horizontal, getRelativeWidth(21.0))
                }
                .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(355.0),
                       alignment: .topLeading)
                .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                    .fill(ColorConstants.WhiteA700))
                .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
                .padding(.bottom, getRelativeHeight(822.78))
            }
            .hideNavigationBar()
            .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                   alignment: .topLeading)
            .background(LinearGradient(gradient: Gradient(colors: [ColorConstants.Cyan900,
                                                                   ColorConstants.Black901]),
                startPoint: .topLeading, endPoint: .bottomTrailing))
            Text("tabbar")
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: UIScreen.main.bounds.width - 20, height: getRelativeHeight(60.0),
                       alignment: .leading)
                .background(ColorConstants.Cyan800)
                .shadow(color: ColorConstants.Black90019, radius: 5, x: 0, y: -4)
        }
        .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
        .background(LinearGradient(gradient: Gradient(colors: [ColorConstants.Cyan900,
                                                               ColorConstants.Black901]),
            startPoint: .topLeading, endPoint: .bottomTrailing))
        .hideNavigationBar()
    }
}

struct RatepopupThreeView_Previews: PreviewProvider {
    static var previews: some View {
        RatepopupThreeView()
    }
}
