import SwiftUI

struct Ratepopup1View: View {
    @StateObject var ratepopup1ViewModel = Ratepopup1ViewModel(_isOpen: .constant(false))
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var body: some View {
        VStack {
            VStack {
                HStack {
                    Text(StringConstants.kLblWriteAReview)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(124.0), height: getRelativeHeight(22.0),
                               alignment: .topLeading)
                    Image("img_closeroundlig")
                        .resizable()
                        .frame(width: getRelativeWidth(12.0), height: getRelativeWidth(12.0),
                               alignment: .center)
                        .scaledToFit()
                        .clipped()
                        .padding(.top, getRelativeHeight(6.0))
                        .padding(.leading, getRelativeWidth(94.0))
                }
                .frame(width: getRelativeWidth(230.0), height: getRelativeHeight(22.0),
                       alignment: .trailing)
                .padding(.top, getRelativeHeight(17.0))
                .padding(.horizontal, getRelativeWidth(26.0))
                Divider()
                    .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(1.0),
                           alignment: .center)
                    .background(ColorConstants.Cyan8003f)
                    .padding(.top, getRelativeHeight(22.0))
                    .padding(.horizontal, getRelativeWidth(21.0))
                Image("img_rectangle10")
                    .resizable()
                    .frame(width: getRelativeWidth(58.0), height: getRelativeWidth(58.0),
                           alignment: .center)
                    .scaledToFit()
                    .clipShape(Circle())
                    .clipShape(Circle())
                    .padding(.top, getRelativeHeight(8.0))
                    .padding(.horizontal, getRelativeWidth(21.0))
                Text(StringConstants.kMsgBroadwayBarber)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(176.0), height: getRelativeHeight(20.0),
                           alignment: .topLeading)
                    .padding(.horizontal, getRelativeWidth(21.0))
                HStack {
                    Image("img_vector_yellow_a700")
                        .resizable()
                        .frame(width: getRelativeWidth(9.0), height: getRelativeWidth(9.0),
                               alignment: .center)
                        .scaledToFit()
                        .clipped()
                        .padding(.top, getRelativeHeight(6.0))
                        .padding(.bottom, getRelativeHeight(8.0))
                        .padding(.leading, getRelativeWidth(18.0))
                    Text(StringConstants.kMsg421kReview)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(11.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(90.0), height: getRelativeHeight(16.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(5.0))
                        .padding(.leading, getRelativeWidth(6.0))
                        .padding(.trailing, getRelativeWidth(13.0))
                }
                .frame(width: getRelativeWidth(138.0), height: getRelativeHeight(24.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 12.0, topRight: 12.0, bottomLeft: 12.0,
                                           bottomRight: 12.0)
                        .fill(ColorConstants.Cyan800))
                .padding(.top, getRelativeHeight(6.0))
                .padding(.horizontal, getRelativeWidth(21.0))
                VStack(alignment: .leading, spacing: 0) {
                    Text(StringConstants.kMsgHowWouldYouR)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(162.0), height: getRelativeHeight(20.0),
                               alignment: .topLeading)
                        .padding(.trailing)
                    HStack {
                        Image("img_star1_amber_600")
                            .resizable()
                            .frame(width: getRelativeWidth(23.0), height: getRelativeHeight(22.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                        Spacer()
                        Image("img_star1_amber_600")
                            .resizable()
                            .frame(width: getRelativeWidth(23.0), height: getRelativeHeight(22.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                        Spacer()
                        Image("img_star1_amber_600")
                            .resizable()
                            .frame(width: getRelativeWidth(23.0), height: getRelativeHeight(22.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                        Spacer()
                        Image("img_star1_amber_600")
                            .resizable()
                            .frame(width: getRelativeWidth(23.0), height: getRelativeHeight(22.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                        Spacer()
                        Image("img_star1_amber_600")
                            .resizable()
                            .frame(width: getRelativeWidth(23.0), height: getRelativeHeight(22.0),
                                   alignment: .center)
                            .scaledToFit()
                            .clipped()
                    }
                    .frame(width: getRelativeWidth(167.0), height: getRelativeHeight(22.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(14.0))
                }
                .frame(width: getRelativeWidth(169.0), height: getRelativeHeight(57.0),
                       alignment: .center)
                .padding(.top, getRelativeHeight(29.0))
                .padding(.horizontal, getRelativeWidth(21.0))
                Button(action: {}, label: {
                    HStack(spacing: 0) {
                        Text(StringConstants.kLblNext)
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                            .fontWeight(.bold)
                            .padding(.horizontal, getRelativeWidth(30.0))
                            .padding(.vertical, getRelativeHeight(7.0))
                            .foregroundColor(ColorConstants.WhiteA700)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.center)
                            .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                       bottomLeft: 17.5, bottomRight: 17.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.vertical, getRelativeHeight(28.0))
                            .padding(.horizontal, getRelativeWidth(21.0))
                    }
                })
                .frame(width: getRelativeWidth(129.0), height: getRelativeHeight(35.0),
                       alignment: .center)
                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                           bottomRight: 17.5)
                        .fill(ColorConstants.Cyan800))
                .padding(.vertical, getRelativeHeight(28.0))
                .padding(.horizontal, getRelativeWidth(21.0))
            }
            .frame(width: UIScreen.main.bounds.width, height: getRelativeHeight(355.0),
                   alignment: .leading)
            .background(RoundedCorners(topLeft: 28.0, topRight: 28.0)
                .fill(ColorConstants.WhiteA700))
            .shadow(color: ColorConstants.Black9003f, radius: 7.6, x: 0, y: -7)
        }
        .frame(width: UIScreen.main.bounds.width)
        .hideNavigationBar()
    }
}

struct Ratepopup1View_Previews: PreviewProvider {
    static var previews: some View {
        Ratepopup1View()
    }
}
