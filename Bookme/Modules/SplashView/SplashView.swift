import SwiftUI

struct SplashView: View {
    let isHomeSplash: Bool
    var animation: Namespace.ID?
    @Namespace private var defaultAnimation
    @StateObject var splashViewModel = SplashViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var appState: AppState
    
    // State variable to control the visibility of the language selection view
    @State private var showLanguageSelection = false
        
    var body: some View {
        NavigationView {
            ZStack(alignment: .bottom) {
                VStack(spacing: -28) {
                    Image("logo-spalash")
                    
                        .resizable()
                        .scaledToFit()
                        .matchedGeometryEffect(id: "splash.logo", in: animation ?? defaultAnimation)
                        .frame(width: getRelativeWidth(98.0), height: getRelativeHeight(162.0),
                               alignment: .center)
                        
                        .padding(.horizontal, getRelativeWidth(146.0))
                    
                    Image("text-spalash")
                        .resizable()
                        .scaledToFit()
                        .frame(width: getRelativeWidth(88.0), height: getRelativeHeight(62.0),
                               alignment: .center)
                      
                        .clipped()
                        .padding(.bottom, getRelativeHeight(88.0))
                        .padding(.horizontal, getRelativeWidth(146.0))
                        
                }.frame(maxWidth: .infinity, maxHeight: .infinity)
                
                
                Group{
                    if !isHomeSplash && showLanguageSelection {
                        VStack(spacing: 42.relativeHeight) {
                            Text("Choose your Language")
                                .font(Font.custom("Nunito", size: 28.relativeFontSize).weight(.bold))
                                .multilineTextAlignment(.center)
                                .foregroundColor(.white)
                            
                            VStack(spacing: 16.relativeHeight) {
                                
                                
                                ForEach(AppLanguageType.allCases) { type in
                                    Button(action: {
                                        updateLanguage(type)
                                    }, label: {
                                        HStack(spacing: 0) {
                                            Text(type.title.uppercased())
                                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                                .fontWeight(.heavy)
                                                .padding(.horizontal, getRelativeWidth(30.0))
                                                .padding(.vertical, getRelativeHeight(15.0))
                                                .foregroundColor(ColorConstants.WhiteA700)
                                                .minimumScaleFactor(0.5)
                                                .multilineTextAlignment(.center)
                                                .frame(width: getRelativeWidth(310.0), height: getRelativeHeight(50.0),
                                                       alignment: .center)
                                                .background(Capsule()
                                                    .fill(ColorConstants.Cyan800))
                                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                                .padding(.leading, getRelativeWidth(21.0))
                                                .padding(.trailing, getRelativeWidth(17.0))
                                        }
                                    })
                                }
                                
                              
                                
                              
                            }
                        }
                        .transition(.move(edge: .bottom))
                    }
                }
                .animation(.bouncy, value: showLanguageSelection)
                
              
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
            .background(ColorConstants.bgGradient)
        }
       
        .onAppear {
            if !isHomeSplash {
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
//                    appState.updateInitialScreen(AppState.isOnboardingShown ? .dashboard : .onboarding)
                }
            }
        }
        .onAppear {
            // Show the language selection view after a delay
            if !isHomeSplash {
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    withAnimation {
                        showLanguageSelection = true
                    }
                }
            }
           
        }
    }
    
    func updateLanguage(_ value: AppLanguageType) {
        appState.updateLocale(value)
        appState.updateInitialScreen(AppState.isOnboardingShown ? .dashboard : .onboarding)
    }
}

struct SplashView_Previews: PreviewProvider {
    static var previews: some View {
        SplashView(isHomeSplash: false)
    }
}
