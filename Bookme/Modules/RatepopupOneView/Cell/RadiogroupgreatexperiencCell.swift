import SwiftUI

struct RadiogroupgreatexperiencCell: View {
    var body: some View {
        RadioGroup(items: [StringConstants.kMsgGreatExperienc], selectedId: .constant(""),
                   selectedColor: Color.blue)
            .frame(width: getRelativeWidth(280.0), alignment: .leading)
        RadioGroup(items: [StringConstants.kMsgGreatExperienc], selectedId: .constant(""),
                   selectedColor: ColorConstants.Cyan8007f)
            .frame(width: getRelativeWidth(132.0), height: getRelativeHeight(21.0),
                   alignment: .topLeading)
            .overlay(RoundedCorners(topLeft: 10.5, topRight: 10.5, bottomLeft: 10.5,
                                    bottomRight: 10.5)
                    .stroke(ColorConstants.Cyan8007f,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 10.5, topRight: 10.5, bottomLeft: 10.5,
                                       bottomRight: 10.5)
                    .fill(Color.clear.opacity(0.7)))
        RadioGroup(items: [StringConstants.k], selectedId: .constant(""),
                   selectedColor: ColorConstants.Cyan8007f)
            .frame(width: getRelativeWidth(127.0), height: getRelativeHeight(21.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 10.5, topRight: 10.5, bottomLeft: 10.5,
                                    bottomRight: 10.5)
                    .stroke(ColorConstants.Cyan8007f,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 10.5, topRight: 10.5, bottomLeft: 10.5,
                                       bottomRight: 10.5)
                    .fill(Color.clear.opacity(0.7)))
            .padding(.leading, getRelativeWidth(19.0))
    }
}

/* struct RadiogroupgreatexperiencCell_Previews: PreviewProvider {

 static var previews: some View {
 			RadiogroupgreatexperiencCell()
 }
 } */
