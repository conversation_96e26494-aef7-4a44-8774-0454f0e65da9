import SwiftUI
import WrappingHStack

struct ShopdetailsabouutView: View {
    let routesType: RoutesType
    @StateObject var shopdetailsabouutViewModel = ShopdetailsabouutViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var shopDetailsViewModel: ShopDetailsViewModel
    @Environment(RouterManager.self) var routerManager: RouterManager
    
    var body: some View {
        if let vendorDetailsModel = shopDetailsViewModel.vendorDetailsModel {
            VStack(alignment: .leading, spacing: 0) {
                Group {
                    Text("About Us")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(74.0),
                               height: getRelativeHeight(22.0), alignment: .topLeading)
                        .padding(.trailing)
                    Text(vendorDetailsModel.description)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Black900B2)
//                                    .fixedSize(horizontal: false, vertical: true)
                        .multilineTextAlignment(.leading)
                        .padding(.top, getRelativeHeight(15.0))
                }
                .padding(.horizontal, getRelativeWidth(22.0))
    
                VStack(alignment: .leading) {
                    Text("Staffers")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(204.0), height: getRelativeHeight(22.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(11.0))
                    
                    ScrollView(.horizontal) {
                        HStack {
                            ForEach(vendorDetailsModel.staffs) { staff in
                                let model: PopularArtistModel = .init(vendorID: staff.vendorID ?? 0, vendorName: "", vendorLogo: "", deliveryRange: 0, owner: nil, staffID: staff.staffID ?? 0, staffName: staff.staffName, staffImage: staff.staffImage, designation: staff.designation, distance: nil)
                                Artistitem1Cell(model: model)
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    
                    .scrollIndicators(.hidden)
                    .scrollClipDisabled()
                }
                .padding(.horizontal, getRelativeWidth(22.0))
                .padding(.vertical)
                
                Group {
                    Text("Contact & Business Hours")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(204.0), height: getRelativeHeight(22.0),
                               alignment: .topLeading)
                        .padding(.top, getRelativeHeight(11.0))
                       
                    VStack(spacing: 0) {
                        HStack {
                            HStack {
                                Image("img_phonelight")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: getRelativeWidth(25.0),
                                           height: getRelativeHeight(25.0), alignment: .center)
                                                
                                    .clipped()
                                Text("\(vendorDetailsModel.vendorMobile)")
                                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.Black900B2)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(115.0),
                                           height: getRelativeHeight(20.0), alignment: .topLeading)
                            }
                            .frame(width: getRelativeWidth(143.0), height: getRelativeHeight(25.0),
                                   alignment: .top)
                            .padding(.top, getRelativeHeight(13.0))
                            .padding(.bottom, getRelativeHeight(16.0))
                            .padding(.leading, getRelativeWidth(12.0))
                            Spacer()
                            Button(action: {
                                if let url = URL(string: "tel://\(vendorDetailsModel.vendorMobile)"),
                                   UIApplication.shared.canOpenURL(url)
                                {
                                    UIApplication.shared.open(url)
                                } else {
                                    print("Cannot dial number.")
                                }
                                
                            }, label: {
                                HStack(spacing: 0) {
                                    Text("Call")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.bold)
                                        .padding(.horizontal, getRelativeWidth(23.0))
                                        .padding(.vertical, getRelativeHeight(8.0))
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(71.0),
                                               height: getRelativeHeight(37.0), alignment: .center)
                                        .overlay(RoundedCorners(topLeft: 18.5, topRight: 18.5,
                                                                bottomLeft: 18.5, bottomRight: 18.5)
                                                .stroke(ColorConstants.Cyan800,
                                                        lineWidth: 1))
                                        .background(RoundedCorners(topLeft: 18.5, topRight: 18.5,
                                                                   bottomLeft: 18.5,
                                                                   bottomRight: 18.5)
                                                .fill(ColorConstants.WhiteA700))
                                        .padding(.top, getRelativeHeight(10.0))
                                        .padding(.bottom, getRelativeHeight(8.0))
                                }
                            })
                            .frame(width: getRelativeWidth(71.0), height: getRelativeHeight(37.0),
                                   alignment: .center)
                            .overlay(RoundedCorners(topLeft: 18.5, topRight: 18.5, bottomLeft: 18.5,
                                                    bottomRight: 18.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 18.5, topRight: 18.5,
                                                       bottomLeft: 18.5, bottomRight: 18.5)
                                    .fill(ColorConstants.WhiteA700))
                            .padding(.vertical, getRelativeHeight(10.0))
                            .padding(.horizontal, 8)
                        }
                      
                        .overlay(Divider().frame(height: 1).overlay(ColorConstants.Cyan8007f), alignment: .top)
                        .overlay(Divider().frame(height: 1).overlay(ColorConstants.Cyan8007f), alignment: .bottom)
                        .padding(.top, getRelativeHeight(16.0))
                       
                        ForEach(vendorDetailsModel.weeklyTime) { item in
                            
                            HStack {
                                Text(item.days)
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.Black900B2)
                                    .multilineTextAlignment(.leading)
                                    .frame(
                                        height: getRelativeHeight(20.0), alignment: .topLeading)
                                Spacer()
                                Text("\(item.openHour.to12Hours) - \(item.closeHour.to12Hours)")
                                    .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(14.0)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(ColorConstants.Black900B2)
                                    .multilineTextAlignment(.leading)
                                    .frame(
                                        height: getRelativeHeight(20.0), alignment: .topLeading)
                            }
                            .frame(width: getRelativeWidth(320.0), height: getRelativeHeight(21.0),
                                   alignment: .center)
                            .padding(.top, getRelativeHeight(13.0))
                        }
                    }
                }
                .padding(.horizontal, getRelativeWidth(22.0))
                
                Group {
                    VStack(alignment: .leading, spacing: 0) {
                        Text("Social Media & Share")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(165.0),
                                   height: getRelativeHeight(22.0), alignment: .topLeading)
                        
                        WrappingHStack(vendorDetailsModel.socialIcons, id: \.self, alignment: .leading, spacing: .constant(8.relativeWidth), lineSpacing: 8.relativeHeight) { item in
                            
                            Button(action: {
                                if let url = URL(string: item.link),
                                   UIApplication.shared.canOpenURL(url)
                                {
                                    UIApplication.shared.open(url)
                                } else {
                                    print("Invalid URL or cannot open URL.")
                                }
                            }, label: {
                                NetworkImageView(path: item.iconUrl)
                                    .frame(width: 20, height: 20)
                                    .frame(width: getRelativeWidth(43.0),
                                           height: getRelativeWidth(43.0), alignment: .center)
                                    .overlay(Circle()
                                            .stroke(ColorConstants.Cyan800,
                                                    lineWidth: 1))
                                    .background(Circle()
                                            .fill(Color.clear.opacity(0.7)))
                            }).onAppear {
                                print("social icon:", item.iconUrl)
                            }
                        }
                        .padding(.top, getRelativeHeight(19.0))
                        .padding(.horizontal, getRelativeWidth(10.0))
//                        HStack {
//                            Button(action: {}, label: {
//                                Image("img_insta")
//                            })
//                            .frame(width: getRelativeWidth(53.0),
//                                   height: getRelativeWidth(53.0), alignment: .center)
//                            .overlay(RoundedCorners(topLeft: 26.5, topRight: 26.5,
//                                                    bottomLeft: 26.5, bottomRight: 26.5)
//                                    .stroke(ColorConstants.Cyan800,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 26.5, topRight: 26.5,
//                                                       bottomLeft: 26.5, bottomRight: 26.5)
//                                    .fill(Color.clear.opacity(0.7)))
//                            Spacer()
//                            Button(action: {}, label: {
//                                Image("img_arrowdown")
//                            })
//                            .frame(width: getRelativeWidth(53.0),
//                                   height: getRelativeWidth(53.0), alignment: .center)
//                            .overlay(RoundedCorners(topLeft: 26.5, topRight: 26.5,
//                                                    bottomLeft: 26.5, bottomRight: 26.5)
//                                    .stroke(ColorConstants.Cyan800,
//                                            lineWidth: 1))
//                            .background(RoundedCorners(topLeft: 26.5, topRight: 26.5,
//                                                       bottomLeft: 26.5, bottomRight: 26.5)
//                                    .fill(Color.clear.opacity(0.7)))
//                            .padding(.leading, getRelativeWidth(20.0))
//                        }
//                        .frame(width: getRelativeWidth(126.0), height: getRelativeHeight(53.0),
//                               alignment: .leading)
//                        .padding(.top, getRelativeHeight(19.0))
//                        .padding(.trailing, getRelativeWidth(10.0))
                    }
                
                    .padding(.top, getRelativeHeight(20.0))
                    
                    VStack(alignment: .leading, spacing: 0) {
                        Text("Venue Amenities")
                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                            .fontWeight(.bold)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(133.0),
                                   height: getRelativeHeight(22.0), alignment: .topLeading)
                            .padding(.trailing)
                            .padding(.bottom, 8.0.relativeHeight)
                       
                        ForEach(vendorDetailsModel.ameneties) { item in
                            HStack {
                                NetworkImageView(path: item.amenitiesImageUrl)
                                    .frame(width: getRelativeWidth(24.0),
                                           height: getRelativeWidth(24.0), alignment: .center)
                                    .scaledToFit()
                                    .clipped()
                                    .padding(.bottom, getRelativeHeight(4.0))
                                Text(item.amenitiesName)
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(14.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.Black900B2)
                                    .multilineTextAlignment(.leading)
                                    .frame(
                                        height: getRelativeHeight(20.0), alignment: .topLeading)
                                    .padding(.leading, getRelativeWidth(13.0))
                            }
                            .frame(height: getRelativeHeight(20.0),
                                   alignment: .leading)
                            .padding(.top, getRelativeHeight(9.0))
                            .padding(.trailing, getRelativeWidth(10.0))
                        }
                    }
                    
                    .padding(.top, getRelativeHeight(24.0))
                    
                    Button {
                        if let vendorDetailsModel = shopDetailsViewModel.vendorDetailsModel {
                            routerManager.push(to: .customRichTextPage(type: .paymentAndCancellation(vendorDetailsModel.paymentcancelpolicy ?? "")), where: routesType)
                        }
                        
                    } label: {
                        HStack {
                            Text("Payment & Cancellation policy")
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(208.0),
                                       height: getRelativeHeight(20.0), alignment: .topLeading)
                                .padding(.top, getRelativeHeight(18.0))
                                .padding(.bottom, getRelativeHeight(16.0))
                                .padding(.leading, getRelativeWidth(11.0))
                            Spacer()
                            Image("img_arrowright_cyan_800")
                                .resizable()
                                .frame(width: getRelativeWidth(6.0),
                                       height: getRelativeHeight(12.0), alignment: .center)
                                .scaledToFit()
                                .clipped()
                                .padding(.top, getRelativeHeight(22.0))
                                .padding(.bottom, getRelativeHeight(21.0))
                                .padding(.trailing, getRelativeWidth(17.0))
                        }
                    }
                    .overlay(Divider().frame(height: 1).overlay(ColorConstants.Cyan8007f), alignment: .top)
                    .overlay(Divider().frame(height: 1).overlay(ColorConstants.Cyan8007f), alignment: .bottom)
                    .background(RoundedCorners().fill(ColorConstants.WhiteA700))
                    .padding(.top, getRelativeHeight(30.0))
                }
                .padding(.horizontal, getRelativeWidth(22.0))
            }
            .frame(maxWidth: .infinity, alignment: .top)
        }
    }
}

struct ShopdetailsabouutView_Previews: PreviewProvider {
    static var previews: some View {
        ShopdetailsabouutView(routesType: .exploreRoute)
    }
}
