//
//  VendorDetailsModel.swift
//  Bookme
//
//  Created by Apple on 23/04/2024.

import Foundation
// MARK: - VendorDetailsModel
struct VendorShortDetailsModel: Hashable, Equatable {
    let vendorID ,salonName, address, staffID: String
    let calendarSchedule:Int
}

struct VendorDetailsModel: Codable, Hashable, Equatable {
    let salonName, description, vendorMobile: String
    let vendorLogo, address, vendorID: String
    let socialIcons: [SocialIcon]
    let instagram, facebook: String?
    let distance: Int
    let traveltime, openingDays, openingTime, closingTime: String
    let rating: String
    let paymentcancelpolicy, vendorEmail, latitude, longitude: String?
    let reviews: [Review]
    let reviewcount, ratingonestarcount, ratingtwostarcount, ratingthreestarcount: Int
    let ratingfourstarcount, ratingfivestarcount: Int
    let weeklyTime: [WeeklyTime]
    let staffs: [Staff]
    let services: [Service]?
    let portfolio: [Portfolio]
    let ameneties: [Amenity]
    let wishlist: Int
    let calendarSchedule:Int
    var isLiked: Bool {
        wishlist == 1
    }
    var vendorLogoUrl: String { AppConstants.Server.baseURL + vendorLogo }
    enum CodingKeys: String, CodingKey {
        case salonName = "salon_name"
        case description
        case vendorEmail = "vendor_email"
        case vendorMobile = "vendor_mobile"
        case vendorLogo = "vendor_logo"
        case address
        case vendorID = "vendor_id"
        case socialIcons = "socialicons"
        case instagram, facebook, distance, traveltime
        case openingDays = "opening_days"
        case openingTime = "opening_time"
        case closingTime = "closing_time"
        case paymentcancelpolicy, rating, reviews, reviewcount, ratingonestarcount, ratingtwostarcount, ratingthreestarcount, ratingfourstarcount, ratingfivestarcount, latitude, longitude
        case weeklyTime = "weekly_time"
        case calendarSchedule = "calendarschedule"
        case staffs, services, portfolio, ameneties, wishlist
    }
    
    // MARK: - SocialIcon
    struct SocialIcon: Codable, Identifiable, Hashable, Equatable {
        let id: UUID = .init()
        let icon, link: String
        
        var iconUrl: String { AppConstants.Server.baseURL + icon }
        
        enum CodingKeys: String, CodingKey {
            case icon, link
        }
    }
    // MARK: - Amenity

    struct Amenity: Codable, Identifiable, Hashable, Equatable {
        let id: UUID = .init()
        let amenitiesImage, amenitiesName: String

        enum CodingKeys: String, CodingKey {
            case amenitiesImage = "AmentiesImage"
            case amenitiesName = "AmentiesName"
        }

        var amenitiesImageUrl: String { AppConstants.Server.baseURL + amenitiesImage }
    }
    // MARK: - Portfolio

    struct Portfolio: Codable, Identifiable, Hashable, Equatable {
        let id: UUID = .init()
        let vendorPhotoID: Int
        let photo: String
        let vendorID: Int?
        let likes: Int

        var photoUrl: String { AppConstants.Server.baseURL + photo }

        enum CodingKeys: String, CodingKey {
            case vendorPhotoID = "Vendor_photoID"
            case vendorID = "vendor_id"
            case photo, likes
        }
    }
    // MARK: - Review

    struct Review: Codable, Identifiable, Hashable, Equatable {
        let id: Int
        let rating: Double
        let description: String?
        let userID, vendorID, active: Int
        let comments, quickReview, image: String?
        var likes, dislikes: Int
        let createdAt: String
        let updatedAt: String?
        let name: String
        var isLiked: Bool
        var isReported: Bool
        var reportDescription: String?
        var ratedDate: String? {
            createdAt.dateFormatter(inputFormat: "yyyy-MM-dd HH:mm:ss", outputFormat: "MMM dd, yyyy")
        }

        var imageUrl: String? {
            guard let image = image else { return nil }
            return AppConstants.Server.baseURL + image
        }

        enum CodingKeys: String, CodingKey {
            case id, rating, description
            case userID = "user_id"
            case vendorID = "vendor_id"
            case active, comments, likes, dislikes
            case quickReview = "quickreview"
            case createdAt = "created_at"
            case updatedAt = "updated_at"
            case name, image, isLiked, isReported
        }
    }
    // MARK: - Service

    struct Service: Codable, Identifiable, Equatable, Hashable {
        let servicesID: Int
        let name, servicePrice: String
        let serviceTime: String
        let serviceCategory, vendorID, enabled: Int?
        let staffID: Int?
        let image,price, servicesfor, commision: String?
        let afterdiscountprice: String?
        var selectedStaff: StaffModel.StaffData?

        var id: Int { servicesID }

        enum CodingKeys: String, CodingKey {
            case servicesID = "ServicesID"
            case name = "Name"
            case image = "Image"
            case servicesfor
            case servicePrice = "Service_Price"
            case serviceTime = "Service_Time"
            case serviceCategory = "Service_Category"
            case staffID = "staff_id"
            case vendorID = "vendor_id"
            case enabled
            case afterdiscountprice = "Afterdiscountprice"
            case price, commision
        }
    }
    // MARK: - Staff

    struct Staff: Codable, Hashable, Equatable, Identifiable {
        let id: UUID = .init()
        let staffID: Int?
        let staffName, staffImage: String
        let giveservices: String?
        let staffDescription, email, phone: String?
        let vendorID: Int?
        let createdAt: String?
        let enStaffDescription: String?
        let staffPass: String?
        let enabled: Int?
        let designation: String?

        var staffImageUrl: String { AppConstants.Server.baseURL + staffImage }

        enum CodingKeys: String, CodingKey {
            case staffID = "staff_id"
            case staffName = "staff_name"
            case staffImage = "staff_image"
            case email, phone, giveservices
            case staffDescription = "staff_description"
            case vendorID = "vendor_id"
            case createdAt = "created_at"
            case enStaffDescription = "en_staff_description"
            case staffPass = "staff_pass"
            case enabled, designation
        }
    }
    // MARK: - WeeklyTime

    struct WeeklyTime: Codable, Identifiable, Hashable, Equatable {
        var id: UUID = .init()
        let timeSlotID: Int
        let openHour, closeHour, days: String
        let vendorID, status: Int

        enum CodingKeys: String, CodingKey {
            case timeSlotID = "time_slot_id"
            case openHour = "open_hour"
            case closeHour = "close_hour"
            case days
            case vendorID = "vendor_id"
            case status
        }
    }
}

//extension String {
//    var to12Hours: String {
//        let dateFormatter = DateFormatter()
//        dateFormatter.dateFormat = "HH:mm"
//        let date = dateFormatter.date(from: self)
//        dateFormatter.dateFormat = "h:mm a"
//        let Date12 = dateFormatter.string(from: date!)
//        return Date12
//    }
//}

extension String {
    var to12Hours: String {
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "HH:mm"
        inputFormatter.locale = Locale(identifier: "en_US_POSIX") // Ensures consistency

        guard let date = inputFormatter.date(from: self) else {
            return self // or return "Invalid time" if you want a clearer fallback
        }

        let outputFormatter = DateFormatter()
        outputFormatter.dateFormat = "h:mm a"
        outputFormatter.locale = Locale(identifier: "en_US_POSIX")

        return outputFormatter.string(from: date)
    }
}

extension String {
    func dateFormatter(inputFormat: String, outputFormat: String) -> String? {
        guard !isEmpty, !inputFormat.isEmpty, !outputFormat.isEmpty else { return "-" }

        let dateFormatter = DateFormatter()

        // step 1
        dateFormatter.dateFormat = inputFormat // input format
        let date = dateFormatter.date(from: self)

        // step 2
        dateFormatter.dateFormat = outputFormat // output format

        var string: String?

        if let date = date {
            string = dateFormatter.string(from: date)
        }

        return string
    }
}

extension Int {
    func abbreviateNumber() -> String {
        if self < 1000 {
            return "\(self)"
        }

        if self < 1000000 {
            var n = Double(self)
            n = Double(floor(n/100)/10)
            return "\(n.description)k"
        }

        if self > 1000000 {
            var n = Double(self)
            n = Double(floor(n/100)/10)
            return "\(n.description)k+"
        }

        var n = Double(self)
        n = Double(floor(n/100000)/10)
        return "\(n.description)m"
    }
}

extension String {
    var toDouble: Double { Double(self) ?? 0.0 }

    var toInt: Int { Int(self) ?? 0 }
}

extension Int {
    var toDouble: Double { Double(self) }
}

extension Optional where Wrapped == String {
    var toDouble: Double { Double(self ?? "0") ?? 0.0 }
    var toInt: Int { Int(self ?? "0") ?? 0 }
}

extension BidirectionalCollection where Element: StringProtocol {
    var sentence: String {
        count <= 2 ?
            joined(separator: " & ") :
            dropLast().joined(separator: ", ") + ", & " + last!
    }
}

