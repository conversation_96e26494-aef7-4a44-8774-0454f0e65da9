import MapKit
import SwiftUI
import SwiftyJSON

struct ShopDetailsView: View {
    // MARK: - Properties
    let routesType: RoutesType
    
    @StateObject var viewModel: ShopDetailsViewModel
    @Environment(\.dismiss) var dismiss: DismissAction
    @Environment(RouterManager.self) private var routerManager: RouterManager
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var deepLinkManager: DeepLinkManager
    
    // MARK: - View State
    @State private var viewPagerSize: CGSize = .zero
    @State private var selectedReview: VendorDetailsModel.Review?
    @State private var offset: CGFloat = 0
    @Namespace private var animation
    
    // MARK: - Initialization
    init(routesType: RoutesType, vendorID: Int, selectedType: ShopDetailsHeaderType) {
        self.routesType = routesType
        self._viewModel = StateObject(wrappedValue: ShopDetailsViewModel(vendorID: vendorID, selectedType: selectedType))
    }
    
    // MARK: - Body
    var body: some View {
        
        VStack {
            SuperView(pageState: $viewModel.pageState) {} content: {
                shopDetailsContent
            }
            .overlay(alignment: .bottom) {
                reviewReportOverlay
            }
            .overlay(content: {
                if viewModel.isReportLoading {
                    ActivityLoaderView()
                }
            })
        }
        .logoNavigationBar(
            invertColor: offset < 0,
            trailingContent: trailingContent,
            appState: _appState,
            router: _routerManager,
            onDismiss: dismiss,
            backButtonWithTitle: ""
        )
        .hideNavigationBar(false)
        .toolbarBackground(.thinMaterial, for: .navigationBar)
        .toolbarBackground(.automatic, for: .navigationBar)
        .safeAreaInset(edge: .bottom) {
            bottomNavigationButton
        }
        .ignoresSafeArea(.container, edges: .bottom)
    }
    
    // MARK: - Content Views
    private var shopDetailsContent: some View {
        GeometryReader { proxy in
            if self.viewModel.pageState == .loading(true) {
                ShopDetailsShimmerView(viewModel: viewModel)
            } else if let vendorDetailsModel = viewModel.vendorDetailsModel {
                VStack(spacing: 0) {
                    ScrollViewWithOffset(.vertical, showsIndicators: false) { self.offset = $0.y }
                        content: {
                            LazyVStack(spacing: 0.relativeHeight, pinnedViews: .sectionHeaders) {
                                // Vendor header section
                                Section {
                                    vendorHeaderSection(vendorDetailsModel: vendorDetailsModel)
                                }.padding(.top, -proxy.safeAreaInsets.top)
                                
                                // Content section
                                Section {
                                    selectedContentView
                                        .environmentObject(viewModel)
                                        .frame(minHeight: UIScreen.main.bounds.height * 0.64, alignment: .top)
                                        .padding(.vertical, 16.0.relativeHeight)
                                        .transition(.slide)
                                        .clipped()
                                } header: {
                                    ShopDetailsCategoryHeader(
                                        selectedType: viewModel.selectedType,
                                        animation: animation,
                                        onTypeSelected: viewModel.updateHeaderSelection
                                    )
                                }
                                .clipped()
                            }
                            .safeAreaPadding(.bottom, AppConstants.tabBarHeight.relativeHeight)
                        }
                        .background(ColorConstants.WhiteA700)
                }
            }
        }
    }
    
    @ViewBuilder
    private func vendorHeaderSection(vendorDetailsModel: VendorDetailsModel) -> some View {
        ZStack(alignment: .topLeading) {
            // Header background image
            
            let imagePath = vendorDetailsModel.vendorLogoUrl
            NetworkImageView(path: imagePath, contentMode: .fill)
                .overlay(ColorConstants.Black9003d)
                .frame(width: UIScreen.main.bounds.width,
                       height: getRelativeHeight(303.0), alignment: .center)
                .scaledToFit()
                .clipped()
                .accessibilityLabel("Salon image for \(vendorDetailsModel.salonName)")
            
            ZStack(alignment: .top) {
                // Shop header info
                ShopHeaderView(
                    vendorDetailsModel: vendorDetailsModel,
                    onLocationTap: {
                        if let latitude = vendorDetailsModel.latitude,
                           let longitude = vendorDetailsModel.longitude {
                            openGoogleMapsWithPin(lat: latitude,
                                                  lon: longitude,
                                                  label: vendorDetailsModel.salonName)
                        }
                    }
                )
                
                // Rating button
                RatingButton(
                    rating: vendorDetailsModel.rating,
                    reviewCount: vendorDetailsModel.reviewcount
                ) {
                    viewModel.updateHeaderSelection(.review)
                }
                .padding(.top, -12)
            }
            .frame(maxWidth: .infinity)
            .padding(.top, getRelativeHeight(261.22))
        }
    }
    
    @ViewBuilder
    private var selectedContentView: some View {
        switch viewModel.selectedType {
        case .service:
            let selectedServiceIDs: [Int] = viewModel.selectedServices.map { $0.servicesID }
            ShopDetailsServiceView(
                isFromBooking: false,
                vendorID: Int(viewModel.vendorID),
                selectedServiceIDs: selectedServiceIDs,
                onSelect: viewModel.onSelectService
            )
        case .about:
            ShopdetailsabouutView(routesType: routesType)
        case .review:
            ShopdetailsReviewView(selectedReview: $selectedReview)
        case .portfolio:
            ShopdetailsPortfolioView()
        }
    }
    
    private var reviewReportOverlay: some View {
        Group {
            if let model = selectedReview {
                SlideUpAnimationContainerView {} content: {
                    WriteReportView(reviewModel: model, onSubmit: { value, isComplete, completion in
                        if let value = value {
                            viewModel.onReportReview(model: value) {
                                completion?()
                            } onAuthFail: {
                                handleAuthFailure()
                            }
                        } else {
                            Utilities.dismissKeyboard()
                            selectedReview = nil
                            if isComplete {
                                viewModel.getVendorDetails(id: viewModel.vendorID)
                            }
                        }
                    })
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
                    .transition(.move(edge: .bottom))
                    .ignoresSafeArea(.container, edges: .bottom)
                }
                .transition(.move(edge: .bottom).combined(with: .opacity))
            }
        }
        .animation(.easeOut, value: selectedReview)
    }
    
    private var bottomNavigationButton: some View {
        CustomBottomNavigationButton(
            subTotal: viewModel.subTotal,
            numberOfServices: viewModel.selectedServices.count,
            disable: viewModel.selectedServices.isEmpty
        ) {
            if AppState.isLoggedIn {
                navigateToBookAppointment()
            } else {
                routerManager.push(to: .signIn(type: routesType, getBack: true), where: routesType)
            }
        }
    }
    
    // MARK: - Navigation Actions
    private func navigateToBookAppointment() {
        guard let vendorDetailsModel = self.viewModel.vendorDetailsModel else { return }
        
        routerManager.push(
            to: .bookAppointment(
                vendor: .init(
                    vendorID: vendorDetailsModel.vendorID,
                    salonName: vendorDetailsModel.salonName,
                    address: vendorDetailsModel.address,
                    staffID: "NULL",
                    calendarSchedule: vendorDetailsModel.calendarSchedule
                ),
                selectedService: viewModel.selectedServices,
                bookID: nil
            ),
            where: routesType
        )
    }
    
    private func handleAuthFailure() {
        let routesType = routerManager.mapRouterWithTab(appState: appState)
        routerManager.push(
            to: .signIn(
                type: routesType,
                getBack: true,
                onGetBack: .init(onBack: {
                    viewModel.getVendorDetails(id: viewModel.vendorID)
                })
            ),
            where: routesType
        )
    }
    
    // MARK: - UI Components
    private var trailingContent: some View {
        HStack {
            // Share button
            Button {
                shareVendorLink()
            } label: {
                Image(.imgSearchWhiteA700)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(20.0),
                           height: getRelativeHeight(22.0), alignment: .center)
                    .clipped()
                    .accessibilityLabel("Share salon")
            }

            // Favorite button
            Button {
                viewModel.addToWishlist {
                    routerManager.push(to: .signIn(type: routesType), where: routesType)
                }
            } label: {
                Image((viewModel.isFavorite) ? .favoriteFilled : .favorite)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(23.0),
                           height: getRelativeHeight(20.0), alignment: .center)
                    .clipped()
                    .padding(.leading, getRelativeWidth(34.0))
                    .accessibilityLabel(viewModel.isFavorite ? "Remove from favorites" : "Add to favorites")
            }
        }
    }
    
    // MARK: - Helper Methods
    private func shareVendorLink() {
        guard let vendorDetailsModel = self.viewModel.vendorDetailsModel else { return }
        
        deepLinkManager.generateUniversalLink(with: vendorDetailsModel.vendorID) { url in
            if let url = url {
                print("Generated Dynamic Link: \(url.absoluteString)")
                  
                let activityViewController = UIActivityViewController(activityItems: [url], applicationActivities: nil)
                if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let rootViewController = scene.windows.first?.rootViewController
                {
                    rootViewController.present(activityViewController, animated: true, completion: nil)
                }
            } else {
                print("Failed to create dynamic link")
            }
        }
    }
}

// MARK: - Map Helper Functions
func openGoogleMapsWithPin(lat: String, lon: String, label: String) {
    let googleMapsAppUrl = URL(string: "comgooglemaps://?q=\(label)&center=\(lat),\(lon)&zoom=14")!
    let googleMapsBrowserUrl = URL(string: "https://maps.google.com/?q=\(lat),\(lon)&zoom=14")!
    let appleMapsUrl = URL(string: "http://maps.apple.com/?ll=\(lat),\(lon)&q=\(label)")!

    if UIApplication.shared.canOpenURL(googleMapsAppUrl) {
        UIApplication.shared.open(googleMapsAppUrl, options: [:], completionHandler: nil)
    } else if UIApplication.shared.canOpenURL(googleMapsBrowserUrl) {
        UIApplication.shared.open(googleMapsBrowserUrl, options: [:], completionHandler: nil)
    } else {
        UIApplication.shared.open(appleMapsUrl, options: [:], completionHandler: nil)
    }
}
 
func openAppleMapsWithPin(lat: String, lon: String, label: String) {
    let coordinate = CLLocationCoordinate2D(latitude: Double(lat) ?? 0.0, longitude: Double(lon) ?? 0.0)
    let placemark = MKPlacemark(coordinate: coordinate)
    let mapItem = MKMapItem(placemark: placemark)
    mapItem.name = label
    mapItem.openInMaps(launchOptions: [
        MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving
    ])
}

func performAPICall(address: String) async throws {
    let baseUrl = "https://maps.googleapis.com/maps/api/geocode/json?"
    let apikey = "l9tSWxeB-Glu5orDFikU07bw83E4RSQ"
    let formattedAddress = address.replacingOccurrences(of: " ", with: "+")
    guard let url = URL(string: "\(baseUrl)address=\(formattedAddress)&key=\(apikey)") else { return }
    let (data, _) = try await URLSession.shared.data(from: url)
    do {
        let json = try JSON(data: data)
        print(json)
    } catch {
        print("JSON Error", error.localizedDescription)
    }
}

func getCoordinate(addressString: String,
                   completionHandler: @escaping (CLLocationCoordinate2D, NSError?) -> Void)
{
    let geoCoder = CLGeocoder()
    geoCoder.geocodeAddressString(addressString) { placeMarks, error in
        if error == nil {
            if let placeMarks = placeMarks?[0] {
                let location = placeMarks.location!
                completionHandler(location.coordinate, nil)
                return
            }
        }
              
        completionHandler(kCLLocationCoordinate2DInvalid, error as NSError?)
    }
}

// MARK: - Helper Views
private struct RatingButton: View {
    // MARK: - Properties
    let rating: String
    let reviewCount: Int
    let action: () -> Void
    
    // MARK: - Body
    var body: some View {
        Button(action: action) {
            HStack {
                Image("img_vector_yellow_a700")
                    .resizable()
                    .frame(width: getRelativeWidth(9.0),
                           height: getRelativeWidth(9.0), alignment: .center)
                    .scaledToFit()
                    .clipped()
                    .padding(.leading, getRelativeWidth(18.0))
                    .accessibilityHidden(true)
                
                let ratingValue = formatRating(rating)
                Text("\(ratingValue) (\(reviewCount.abbreviateNumber()) Review) ")
                    .font(FontScheme.kNunitoRegular(size: 11.0.relativeFontSize))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.WhiteA700)
                    .multilineTextAlignment(.leading)
                    .frame(height: getRelativeHeight(16.0), alignment: .topLeading)
                    .padding(.horizontal, getRelativeWidth(6.0))
            }
            .frame(width: getRelativeWidth(138.0),
                   height: getRelativeHeight(24.0), alignment: .center)
            .background(RoundedCorners(topLeft: 12.0, topRight: 12.0,
                                       bottomLeft: 12.0, bottomRight: 12.0)
                    .fill(ColorConstants.Cyan800))
            .accessibilityLabel("Rating \(formatRating(rating)) with \(reviewCount) reviews. Tap to view reviews.")
        }
    }
    
    // MARK: - Helper Methods
    private func formatRating(_ rating: String) -> String {
        let components = rating.components(separatedBy: "/")
        let firstComponent = components.first ?? "0"
        return String(format: "%.1f", Double(firstComponent) ?? 0)
    }
}

private func formatDistance(meters: Int) -> String {
    let measurement = Measurement(value: Double(meters), unit: UnitLength.meters)
    let kilometers = measurement.converted(to: .kilometers)
    return kilometers.description
}

private struct ShopHeaderView: View {
    // MARK: - Properties
    let vendorDetailsModel: VendorDetailsModel
    let onLocationTap: () -> Void
    
    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(vendorDetailsModel.salonName)
                .font(FontScheme.kNunitoBold(size: 18.0.relativeFontSize))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Cyan800)
                .multilineTextAlignment(.leading)
                .padding(.horizontal, getRelativeWidth(30.0))
                .accessibilityAddTraits(.isHeader)
            
            LocationButton(address: vendorDetailsModel.address, action: onLocationTap)
                .padding(.top, getRelativeHeight(11.0))
                .padding(.horizontal, getRelativeWidth(29.0))
            
            TimeDistanceInfoView(
                travelTime: vendorDetailsModel.traveltime,
                distance: vendorDetailsModel.distance,
                openingDays: vendorDetailsModel.openingDays,
                openingTime: vendorDetailsModel.openingTime,
                closingTime: vendorDetailsModel.closingTime
            )
            .padding(.top, getRelativeHeight(13.0))
            .padding(.horizontal, getRelativeWidth(30.0))
            .padding(.bottom)
        }
        .padding(.top, 32)
        .frame(maxWidth: .infinity)
        .background(RoundedCorners(topLeft: 49.0, topRight: 49.0)
            .fill(ColorConstants.WhiteA700))
    }
}

private struct LocationButton: View {
    // MARK: - Properties
    let address: String
    let action: () -> Void
    
    // MARK: - Body
    var body: some View {
        Button(action: action) {
            HStack(alignment: .top, spacing: 8.0.relativeWidth) {
                Image("location_pin")
                    .resizable()
                    .scaledToFit()
                    .frame(width: getRelativeWidth(16.0),
                           height: getRelativeHeight(16.0))
                    .clipped()
                    .padding(.top, getRelativeHeight(2.0))
                    .accessibilityHidden(true)
                
                Text(address)
                    .font(FontScheme.kNunitoRegular(size: 14.0.relativeFontSize))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Gray800)
                    .multilineTextAlignment(.leading)
            }
        }
        .accessibilityLabel("Location: \(address). Tap to open in maps.")
        .accessibilityAddTraits(.isButton)
    }
}

private struct TimeDistanceInfoView: View {
    // MARK: - Properties
    let travelTime: String
    let distance: Int
    let openingDays: String
    let openingTime: String
    let closingTime: String
    
    // MARK: - Body
    var body: some View {
        HStack {
            Image("img_time")
                .frame(width: getRelativeWidth(10.0),
                       height: getRelativeWidth(10.0))
                .overlay(Circle().stroke(ColorConstants.Cyan800, lineWidth: 1))
                .background(Circle().fill(Color.clear.opacity(0.7)))
                .padding(.vertical, getRelativeHeight(5.0))
                .scaleEffect(1.3, anchor: .leading)
                .accessibilityHidden(true)
            
            let formattedDistance = formatDistance(meters: distance)
            Text("\(travelTime) . \(formattedDistance) . \(openingDays) | \(openingTime.to12Hours) - \(closingTime.to12Hours)")
                .font(FontScheme.kNunitoRegular(size: 12.0.relativeFontSize))
                .fontWeight(.regular)
                .foregroundColor(ColorConstants.Black900B2)
                .multilineTextAlignment(.leading)
                .frame(height: getRelativeHeight(20.0))
                .padding(.leading, getRelativeWidth(6.0))
        }
        .frame(height: getRelativeHeight(20.0), alignment: .leading)
        .accessibilityLabel("Travel time: \(travelTime), Distance: \(formatDistance(meters: distance)), Open: \(openingDays) from \(openingTime.to12Hours) to \(closingTime.to12Hours)")
    }
}

// MARK: - Shop Details Category Header
private struct ShopDetailsCategoryHeader: View {
    // MARK: - Properties
    let selectedType: ShopDetailsHeaderType
    var animation: Namespace.ID
    var onTypeSelected: (ShopDetailsHeaderType) -> Void
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            HStack(alignment: .top, spacing: 24.0.relativeWidth) {
                ForEach(ShopDetailsHeaderType.allCases) { item in
                    CategoryTabButton(
                        item: item,
                        isSelected: selectedType == item,
                        animation: animation,
                        onSelect: onTypeSelected
                    )
                    .frame(maxWidth: .infinity)
                }
            }
            .animation(.default, value: selectedType)
            .frame(maxWidth: .infinity)

            Divider()
                .frame(height: getRelativeHeight(2.0), alignment: .leading)
                .background(ColorConstants.Cyan800)
        }
        .background(ColorConstants.WhiteA700)
    }
}

private struct CategoryTabButton: View {
    // MARK: - Properties
    let item: ShopDetailsHeaderType
    let isSelected: Bool
    var animation: Namespace.ID
    var onSelect: (ShopDetailsHeaderType) -> Void
    
    // MARK: - Body
    var body: some View {
        Button(action: {
            onSelect(item)
        }) {
            VStack(spacing: 2) {
                Text(item.title)
                    .font(FontScheme.kNunitoMedium(size: 14.0.relativeFontSize))
                    .fontWeight(.bold)
                    .foregroundColor(isSelected ? ColorConstants.Cyan800 : ColorConstants.Cyan800.opacity(0.51))
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.center)
                
                if isSelected {
                    Divider()
                        .frame(
                            width: CGFloat(item.rawValue.count * 8),
                            height: getRelativeHeight(4.0),
                            alignment: .center
                        )
                        .background(ColorConstants.Cyan800)
                        .clipShape(UnevenRoundedRectangle(
                            topLeadingRadius: 4,
                            bottomLeadingRadius: 0,
                            bottomTrailingRadius: 0,
                            topTrailingRadius: 4
                        ))
                        .matchedGeometryEffect(id: "header.divider", in: animation)
                }
            }
            .padding(.top, 8.0.relativeHeight)
        }
        .accessibilityAddTraits(isSelected ? [.isButton, .isSelected] : .isButton)
        .accessibilityLabel("\(item.title) tab")
        .accessibilityHint(isSelected ? "Selected" : "Tap to view \(item.title)")
    }
}
