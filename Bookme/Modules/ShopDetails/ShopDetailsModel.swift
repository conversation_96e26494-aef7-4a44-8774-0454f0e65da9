//
//  ShopDetailsModel.swift
//  Bookme
//
//  Created by Apple on 07/05/2024.
//

import SwiftUI

enum ShopDetailsHeaderType: String, CaseIterable, Identifiable {
    case service, about, review, portfolio

    var id: UUID {
        switch self {
        case .service,
             .about,
             .review,
             .portfolio:

            UUID()
        }
    }
    
    var title: LocalizedStringKey {
        switch self {
            
            case .service:
            "Services"
        case .about:
         "About"
        case .review:
            "Reviews"
        case .portfolio:
        "Portfolio"
        }
    }

//    @ViewBuilder
//    var view: some View {
//        switch self {
//        case .service:
//            ShopDetailsServiceView(selectedServices: .constant([]))
//        case .about:
//            ShopdetailsabouutView()
//        case .review:
//            ShopdetailsReviewView()
//        case .portfolio:
//            ShopdetailsPortfolioView()
//        }
//    }
}
