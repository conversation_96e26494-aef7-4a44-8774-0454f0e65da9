//
//  TabViewDynamicHeight.swift
//  Bookme
//
//  Created by Apple on 26/04/2024.
//

import SwiftUI
//import Containers

struct TabViewDynamicHeight: View {
    @State private var selection: Int = 0
    @State private var viewPagerSize: CGSize = .zero
    var body: some View {
        
            createViewPager()
                
        
    }
    
    
    @ViewBuilder
    func createViewPager() -> some View {
        
        // Passing `fit` for the contentMode forces the PageView to hug its content. To fill the available space, set this to `fill` (its default value)
        VStack{
            VStack{
                
            }
            .frame(height: 200)
            .background(.green)
            TabView(selection: $selection) {
                Group {
                    ScrollView{
                        Text(String(repeating: "Page 1", count: 1))
                           
                    }
                    ScrollView{
                        Text(String(repeating: "Page 1", count: 10))
                            
                    }
                    ScrollView{
                        Text(String(repeating: "Page 1", count: 1000))
                           
                    }
                }
            }
            
            .tabViewStyle(.page(indexDisplayMode: .never))
            .indexViewStyle(.page(backgroundDisplayMode: .always))
            .background(.orange)
            .onChange(of: viewPagerSize, initial: true) { oldValue, newValue in
                print(newValue)
            }
        }
    }
}


struct ViewRectKey: PreferenceKey {
   static let defaultValue: CGSize = .zero
   static func reduce(value: inout CGSize, nextValue: () -> CGSize) {
      value = nextValue()
   }
}

struct TabViewDynamicHeight_Previews: PreviewProvider {
    static var previews: some View {
        TabViewDynamicHeight()
    }
}
