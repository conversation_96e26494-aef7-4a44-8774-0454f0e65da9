//
//  ShopDetailsShimmerView.swift
//  Bookme
//
//  Created by Apple on 17/03/2025.
//


import SwiftUI

struct ShopDetailsShimmerView: View {
    @StateObject var viewModel: ShopDetailsViewModel
    @State private var offset: CGFloat = 0
    @State var selectedReview: VendorDetailsModel.Review?
    var body: some View {
        GeometryReader { proxy in
            VStack(spacing: 0) {
                ScrollViewWithOffset(.vertical, showsIndicators: false) { self.offset = $0.y }
                    content: {
                        LazyVStack(spacing: 0.relativeHeight, pinnedViews: .sectionHeaders) {
                            Section {
                                ZStack(alignment: .topLeading) {
                                    NetworkImageView(path: nil, contentMode: .fill)
                                        .overlay(ColorConstants.Black9003d)
                                        .frame(width: proxy.size.width,
                                               height: getRelativeHeight(303.0), alignment: .center)
//                                        .background(.black.opacity(0.6))
                                    ZStack(alignment: .top) {
                                        VStack(alignment: .leading, spacing: 0) {
                                            Text("vendorDetailsModel.salonName")
                                                .font(FontScheme
                                                    .kNunitoBold(size: getRelativeHeight(18.0)))
                                                .fontWeight(.bold)
                                                .foregroundColor(ColorConstants.Cyan800)
                                                .minimumScaleFactor(0.5)
                                                .multilineTextAlignment(.leading)
                                                .frame(width: getRelativeWidth(223.0),
                                                       height: getRelativeHeight(25.0),
                                                       alignment: .topLeading)
                                                .padding(.top, getRelativeHeight(0.0))
                                                .padding(.horizontal, getRelativeWidth(30.0))
                                            Button(action: {}, label: {
                                                HStack(alignment: .top, spacing: 8.0.relativeWidth) {
                                                    Image("location_pin")
                                                        .resizable()
                                                        .scaledToFit()
                                                        .frame(width: getRelativeWidth(16.0),
                                                               height: getRelativeHeight(16.0),
                                                               alignment: .center)
                                                       
                                                        .clipped()
                                                        .padding(.top, getRelativeHeight(2.0))
                                                    
                                                    Text("vendorDetailsModel.address vendorDetailsModel.address vendorDetailsModel.address")
                                                        .font(FontScheme
                                                            .kNunitoRegular(size: getRelativeHeight(14.0)))
                                                        .fontWeight(.regular)
                                                        .foregroundColor(ColorConstants.Gray800)
                                                        .multilineTextAlignment(.leading)
                                                }
                                                .padding(.top, getRelativeHeight(11.0))
                                                .padding(.horizontal, getRelativeWidth(29.0))
                                            })
                                            
                                            HStack {
                                                Image("img_time")
                                                    .frame(width: getRelativeWidth(10.0),
                                                           height: getRelativeWidth(10.0),
                                                           alignment: .center)
                                                    .overlay(Circle().stroke(ColorConstants.Cyan800,
                                                                             lineWidth: 1))
                                                    .background(Circle()
                                                        .fill(Color.clear.opacity(0.7)))
                                                    .padding(.top, getRelativeHeight(5.0))
                                                    .padding(.bottom, getRelativeHeight(6.0))
                                                    .scaleEffect(1.3, anchor: .leading)
                                            
                                                // convert meter to kilometers
                                                
                                                Text("\("traveltime") . \("kilometers.description") . \("openingDays") | \("12") - \("12")")
                                                    .font(FontScheme
                                                        .kNunitoRegular(size: getRelativeHeight(14.0)))
                                                    .fontWeight(.regular)
                                                    .foregroundColor(ColorConstants.Black900B2)
                                                    .minimumScaleFactor(0.5)
                                                    .multilineTextAlignment(.leading)
                                                    .frame(width: getRelativeWidth(271.0),
                                                           height: getRelativeHeight(20.0),
                                                           alignment: .topLeading)
                                                    .padding(.leading, getRelativeWidth(6.0))
                                            }
                                            .frame(width: getRelativeWidth(290.0),
                                                   height: getRelativeHeight(20.0),
                                                   alignment: .leading)
                                           
                                            .padding(.top, getRelativeHeight(13.0))
                                            .padding(.horizontal, getRelativeWidth(30.0))
                                            .padding(.bottom)
                                        }
                                        .padding(.top, 32)
                                        .frame(maxWidth: .infinity)
                                        .background(RoundedCorners(topLeft: 49.0, topRight: 49.0)
                                            .fill(ColorConstants.WhiteA700))
                                    
                                        //                                                let ratingComponents: [String] = vendorDetailsModel.rating.components(separatedBy: "/")
                                        //                                                let firstComponent = ratingComponents.first ?? "0"
                                        //                                                let ratingValue = String(format: "%.1f", Double(firstComponent) ?? "0")
                                    
                                        Button(action: {}, label: {
                                            HStack {
                                                Image("img_vector_yellow_a700")
                                                    .resizable()
                                                    .frame(width: getRelativeWidth(9.0),
                                                           height: getRelativeWidth(9.0), alignment: .center)
                                                    .scaledToFit()
                                                    .clipped()
                                                    .padding(.leading, getRelativeWidth(18.0))
                                                Text("\(0) (\(0) Review) ")
                                                    .font(FontScheme
                                                        .kNunitoRegular(size: getRelativeHeight(11.0)))
                                                    .fontWeight(.regular)
                                                    .foregroundColor(ColorConstants.WhiteA700)
                                                    .multilineTextAlignment(.leading)
                                                    .frame(
                                                        height: getRelativeHeight(16.0),
                                                        alignment: .topLeading)
                                                    .padding(.leading, getRelativeWidth(6.0))
                                                    .padding(.trailing, getRelativeWidth(13.0))
                                            }
                                            .frame(width: getRelativeWidth(138.0),
                                                   height: getRelativeHeight(24.0), alignment: .center)
                                            .background(RoundedCorners(topLeft: 12.0, topRight: 12.0,
                                                                       bottomLeft: 12.0, bottomRight: 12.0)
                                                    .fill(ColorConstants.Cyan800))
                                        })
                                        .padding(.top, -12)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.top, getRelativeHeight(261.22))
                                }
                            }.padding(.top, -proxy.safeAreaInsets.top)
                            Section {
                                let type = viewModel.selectedType
                                Group {
                                    switch type {
                                    case .service:
                                        let selectedServiceIDs: [Int] = viewModel.selectedServices.map { $0.servicesID }
                                        ShopDetailsServiceView(isFromBooking: false, vendorID: Int(viewModel.vendorID), selectedServiceIDs: selectedServiceIDs, onSelect: viewModel.onSelectService)
                                    case .about:
                                        ShopdetailsabouutView(routesType: .homeRoute)
                                    case .review:
                                        ShopdetailsReviewView(selectedReview: $selectedReview)
                                    case .portfolio:
                                        ShopdetailsPortfolioView()
                                    }
                                }
                                .environmentObject(viewModel)
                                .frame(minHeight: UIScreen.main.bounds.height * 0.60, alignment: .top)
                                .padding(.vertical, 16.0.relativeHeight)
                                .transition(.slide)
                                .clipped()
                                
                                //                                        switch type {
                                //                                        case .service, .about, .review, .portfolio:
                                //                                            type.view
                                //                                                .frame(minHeight: UIScreen.main.bounds.height * 0.74, alignment: .top)
                                //                                                .environmentObject(viewModel)
                                //                                                .padding(.vertical, 16.0.relativeHeight)
                                //                                                .transition(.slide)
                                //                                                .clipped()
                                //                                        }

                            } header: {
                                VStack(spacing: 0) {
                                    HStack(alignment: .top, spacing: 24.0.relativeWidth) {
                                        ForEach(ShopDetailsHeaderType.allCases) { item in
                                            VStack {
                                                Button(action: {
                                                    viewModel.updateHeaderSelection(item)
                                                  
                                                }, label: {
                                                    HStack(alignment: .top, spacing: 2) {
                                                        //                                                Spacer()
                                                        VStack(spacing: 2) {
                                                            Text(item.title)
                                                                .font(FontScheme
                                                                    .kNunitoMedium(size: 14.0.relativeFontSize))
                                                                .fontWeight(.bold)
                                                                .foregroundColor(viewModel.selectedType == item ? ColorConstants.Cyan800 : ColorConstants.Cyan800.opacity(0.51))
                                                                .minimumScaleFactor(0.5)
                                                                .multilineTextAlignment(.center)
                                                            if viewModel.selectedType == item {
                                                                Divider()
                                                                    .frame(
                                                                        width: CGFloat(item.rawValue.count * 8), height: getRelativeHeight(4.0), alignment: .center)
                                                                    .background(ColorConstants.Cyan800)
                                                                    //                                                            .padding(.horizontal)
                                                                    .clipShape(UnevenRoundedRectangle(topLeadingRadius: 4, bottomLeadingRadius: 0, bottomTrailingRadius: 0, topTrailingRadius: 4))
                                                            }
                                                        }
                                                    }
                                                    .padding(.top, 8.0.relativeHeight)
                                                })
                                            }
                                            
                                            .frame(maxWidth: .infinity)
                                        }
                                    }
                                    .animation(.default, value: viewModel.selectedType)
                                    .frame(maxWidth: .infinity)

                                    Divider()
                                        .frame(
                                            height: getRelativeHeight(2.0), alignment: .leading)
                                        .background(ColorConstants.Cyan800)
                                }
                                .background(ColorConstants.WhiteA700)
                            }
                            
                            .clipped()
                            //                                    .sync(viewModel.selectedServices, with: appState.selectedServices)
                            //                                    .onChange(of: viewModel.selectedService) { oldValue, newValue in
                            //
                            //                                        appState.updateBookingRequestModelListBulk(newValue.map { .init(service: $0) })
                            //
                            //                                        if newValue.count > oldValue.count {
                            //
                            //                                            if let vendorDetailsModel = self.viewModel.vendorDetailsModel {
                            //                                                Utilities.enQueue(after: .now()+0.4) {
                            //
                            //                                                    routerManager.push(to: .bookAppointment(vendor: vendorDetailsModel, serviceIDs: viewModel.selectedServices), where: routesType)
                            //                                                }
                            //                                            }
                            //
                            //                                        }
                            //                                    }
                        }
                        .safeAreaPadding(.bottom, AppConstants.tabBarHeight.relativeHeight)
                    }.background(ColorConstants.WhiteA700)
            }
        }
        .shimmerize()
    }
}
