import Foundation
import SwiftUI

class ShopDetailsViewModel: SuperViewModel {
    let vendorID: Int
    @Published var nextScreen: String? = nil
    @Published var selectedType: ShopDetailsHeaderType = .service
    @Published var vendorDetailsModel: VendorDetailsModel?
    @Published var scrollPosition: Int? = 0
//    @Published var selectedServices: [VendorDetailsModel.Service] = []
    
    @Published var isFavorite: Bool = false
    
//    @Published var selectedService: VendorDetailsModel.Service?
    @Published var selectedServices: [VendorDetailsModel.Service] = []
    
    @Published var showReportPopUp:Bool = false

    init(vendorID: Int, selectedType: ShopDetailsHeaderType) {
        self.vendorID = vendorID
       
        super.init()
        getVendorDetails(id: vendorID)
        updateHeaderSelection(selectedType)
    }
    
    
    func updateReportPopUp() {
        showReportPopUp.toggle()
    }
    
    var subTotal: Double {
        selectedServices.sum(\.price.toDouble)
    }
    
    func onSelectService(_ model: VendorDetailsModel.Service) {
        let copyList = selectedServices
        selectedServices.removeAll()
        
        if !copyList.contains(model) {
            selectedServices.append(model)
        }
    }
    
    @Published var isLikeLoading: Bool = false
    // Convert the `Published` property to a `Binding`
    var isLikeLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isLikeLoading }, // Getter for the binding
            set: { self.isLikeLoading = $0 } // Setter for the binding
        )
    }
    
    func onLikeReview(model:VendorDetailsModel.Review, onSuccess: @escaping () -> Void, onAuthFail: () -> Void){
        guard let userID: Int = AppState.userModel?.user.id else { onAuthFail() ; return }
        let parameters: [String: Any] = ["user_id": userID, "review_id": model.id, "vendor_id": vendorID]
        onApiCall(api.addLikesReview, parameters: parameters,withLoadingIndicator: false, customLoadingBinding: isLikeLoadingBinding) { _ in
//            if $0.success {
//                self.getVendorDetails(id: self.vendorID)
                onSuccess()
//            }
        }
    }
    
    
    @Published var isReportLoading: Bool = false
    // Convert the `Published` property to a `Binding`
    var iisReportLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isReportLoading }, // Getter for the binding
            set: { self.isReportLoading = $0 } // Setter for the binding
        )
    }
    
    func onReportReview(model:VendorDetailsModel.Review, onSuccess:@escaping () -> Void, onAuthFail: () -> Void){
        guard let userID: Int = AppState.userModel?.user.id else { onAuthFail() ; return }
        let parameters: [String: Any] = ["user_id": userID, "review_id": model.id, "vendor_id": vendorID, "notes": model.reportDescription]
        onApiCall(api.addReportReviews, parameters: parameters, withLoadingIndicator: false, customLoadingBinding: iisReportLoadingBinding) { _ in
//            if $0.success {
                onSuccess()
//                self.getVendorDetails(id: self.vendorID)
//            }
        }
    }

    func getVendorDetails(id: Int) {

        if let coordination = AppState.userLocation {
            var request: [String: Any] = ["vendor_id": "\(id)", "lat": "\(coordination.latitude)", "lng": "\(coordination.longitude)"]
            if let userID: Int = AppState.userModel?.user.id { request.updateValue(userID, forKey: "user_id") }
            onApiCall(api.vendorDetail, parameters: request) {
                self.vendorDetailsModel = $0.data
                self.isFavorite = ($0.data?.isLiked) ?? false
            }
        }
    }
    
    func addToWishlist(onAuthFail: () -> Void) {
        guard let userID: Int = AppState.userModel?.user.id else { onAuthFail(); return }
        let parameters: [String: Any] = ["user_id": userID, "Vendor_ID": vendorID]
        
        if AppState.isLoggedIn {
            isFavorite.toggle()
            onApiCall(api.addWishlist, parameters: parameters,withLoadingIndicator: false) { response in
                if response.success {
//                    self.getVendorDetails(id: self.vendorID)
                }
            }
            
        } else {
            onAuthFail()
        }
    }
    
    func checkHeader(_ value: ShopDetailsHeaderType) -> Bool { value == selectedType }

    func updateHeaderSelection(_ value: ShopDetailsHeaderType) {
        selectedType = value
        scrollPosition = value.index
    }
}

extension Array where Element: Equatable {
    // Remove first collection element that is equal to the given `object`:
    mutating func remove(object: Element) {
        guard let index = firstIndex(of: object) else { return }
        remove(at: index)
    }
}

extension Sequence {
    func sum<T: AdditiveArithmetic>(_ predicate: (Element) -> T) -> T { reduce(.zero) { $0 + predicate($1) } }
}
