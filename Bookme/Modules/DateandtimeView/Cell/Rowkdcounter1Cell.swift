import SwiftUI

struct Rowkdcounter1Cell: View {
    var body: some View {
        HStack {
            HStack {
                Button(action: {}, label: {
                    Image("img_vector403")
                })
                .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(18.0),
                       alignment: .center)
                .overlay(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                        bottomRight: 3.0)
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                           bottomRight: 3.0)
                        .fill(Color.clear.opacity(0.7)))
                Text(StringConstants.kMsgHairCutQuiff)
                    .font(FontScheme.kMontserratMedium(size: getRelativeHeight(13.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Black900B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(90.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.leading, getRelativeWidth(7.0))
            }
            .frame(width: getRelativeWidth(115.0), height: getRelativeHeight(18.0),
                   alignment: .leading)
            Text(StringConstants.kLblKd0000)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(13.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Black900)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(57.0), height: getRelativeHeight(18.0),
                       alignment: .leading)
                .padding(.leading, getRelativeWidth(144.0))
        }
        .frame(width: getRelativeWidth(319.0), alignment: .leading)
        .hideNavigationBar()
    }
}

/* struct Rowkdcounter1Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Rowkdcounter1Cell()
 }
 } */
