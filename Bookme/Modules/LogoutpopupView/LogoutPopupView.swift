import SwiftUI

struct LogoutPopupView: View {
    @Binding var showPopUp: Bool
    let onLogout: () -> Void
    var body: some View {
        VStack {
            VStack {
                Text("Logout")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(51.0), height: getRelativeHeight(22.0),
                           alignment: .center)
                    .padding(.top, getRelativeHeight(16.0))
                    .padding(.bottom, getRelativeHeight(8.0))
                    .padding(.horizontal, getRelativeWidth(21.0))
                   
                Divider()
                    .frame(width: getRelativeWidth(349.0), height: getRelativeHeight(1.0),
                           alignment: .center)
                    .background(ColorConstants.Cyan8003f)
                    
                    .padding(.horizontal, getRelativeWidth(21.0))
                   
                Text("Are you sure you want to log out?")
                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(14.0)))
                    .fontWeight(.regular)
                    .foregroundColor(ColorConstants.Black900B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(202.0), height: getRelativeHeight(20.0),
                           alignment: .topLeading)
                    .padding(.top, getRelativeHeight(16.0))
                    .padding(.horizontal, getRelativeWidth(21.0))
                
                
                
                HStack {
                    Button(action: {
                        self.showPopUp.toggle()
                    }, label: {
                        HStack(spacing: 0) {
                            Text("Cancel")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.bold)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(7.0))
                                .foregroundColor(ColorConstants.Cyan800)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(129.0),
                                       height: getRelativeHeight(35.0), alignment: .center)
                                .overlay(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                        bottomLeft: 17.5, bottomRight: 17.5)
                                        .stroke(ColorConstants.Cyan800,
                                                lineWidth: 1))
                                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                           bottomLeft: 17.5, bottomRight: 17.5)
                                        .fill(Color.clear.opacity(0.7)))
                        }
                    })
                   
                    Spacer()
                    Button(action: {
                        self.showPopUp.toggle()
                        self.onLogout()
                    }, label: {
                        HStack(spacing: 0) {
                            Text("Yes, Logout")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.bold)
                                .padding(.horizontal, getRelativeWidth(27.0))
                                .padding(.vertical, getRelativeHeight(7.0))
                                .foregroundColor(ColorConstants.WhiteA700)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(width: getRelativeWidth(129.0),
                                       height: getRelativeHeight(35.0), alignment: .center)
                                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5,
                                                           bottomLeft: 17.5, bottomRight: 17.5)
                                        .fill(ColorConstants.Cyan800))
                        }
                    })
                    
                }
                .frame(width: getRelativeWidth(342.0),
                       alignment: .center)
                .padding(.top, getRelativeHeight(16.0))
                .padding(.bottom, getRelativeHeight(26.0))
                .padding(.horizontal, getRelativeWidth(21.0))
                
            }
            .frame(width: UIScreen.main.bounds.width,
                   alignment: .leading)
            .padding(.bottom, 32.0.relativeHeight)
            .background(RoundedCorners(topLeft: 25.0, topRight: 25.0)
                .fill(ColorConstants.WhiteA700))
            .clipped()
            .shadow(color: ColorConstants.Black90082, radius: 12.1, x: 0, y: -1)
        }
       
        .hideNavigationBar()
    }
}

struct LogoutpopupView_Previews: PreviewProvider {
    static var previews: some View {
        LogoutPopupView(showPopUp: .constant(true)) {}
    }
}
