import CoreLocationUI
import MultiSlider
import SwiftUI
import WrappingHStack

struct FilterView: View {
    let onApplyFilter: (FiltersRequest?) -> Void
    @StateObject var viewModel: FilterViewModel
    @StateObject var locationManager = LocationManager()
    @Environment(\.dismiss) private var dismiss
   
    @Namespace private var animation
    @State private var locationTitle: String?
    
    @State private var isLocationLoading: Bool = false
    
    init(model: FiltersRequest?, onApplyFilter: @escaping (FiltersRequest?) -> Void) {
        self.onApplyFilter = onApplyFilter
        self._viewModel = StateObject(wrappedValue: FilterViewModel(model: model))
    }
    
    var body: some View {
        SuperView(pageState: $viewModel.pageState, loadingView: {
            FilterShimmerView()
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
                   
        }, content: {
            MainScrollBody(invertColor: true, backButtonWithTitle: "Filters") {
                if let filtersModel = viewModel.filtersModel {
                    VStack(alignment: .leading, spacing: 0) {
                        Group {
                            VStack(alignment: .leading, spacing: 0) {
                                Text("Select Gender")
                                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                                    .fontWeight(.bold)
                                    .foregroundColor(ColorConstants.Black900)
                                    .multilineTextAlignment(.leading)
                                    .frame(height: getRelativeHeight(22.0), alignment: .topLeading)
                                    .padding(.horizontal)
                                HStack {
                                    WrappingHStack(filtersModel.genders, id: \.self, alignment: .center, lineSpacing: 8) { type in
                                     
                                        Button {
                                            viewModel.onGenderSelection(type)
                                        } label: {
                                            HStack {
                                                VStack {
                                                    ZStack {}
                                                           
                                                        .frame(width: getRelativeWidth(13.0),
                                                               height: getRelativeWidth(13.0),
                                                               alignment: .center)
                                                        .background(Circle()
                                                            .fill(viewModel.selectedGenderType.contains(type) ? ColorConstants.Cyan800 : ColorConstants.Cyan8003f))
                                                        .padding(.all, getRelativeWidth(4.0))
                                                }
                                                .frame(width: getRelativeWidth(21.0),
                                                       height: getRelativeWidth(21.0), alignment: .center)
                                                .overlay(Circle()
                                                    .stroke(ColorConstants.Cyan800,
                                                            lineWidth: 1))
                                                .background(Circle()
                                                    .fill(Color.clear.opacity(0.7)))
                                                .padding(.leading, getRelativeWidth(9.0))
                                                Text(type.typeName.capitalized)
                                                    .font(FontScheme
                                                        .kNunitoMedium(size: getRelativeHeight(14.0)))
                                                    .fontWeight(.medium)
                                                    .foregroundColor(ColorConstants.Cyan800)
                                                    .fixedSize()
                                                    .multilineTextAlignment(.leading)
                                                    .frame(
                                                        height: getRelativeHeight(20.0),
                                                        alignment: .topLeading)
                                                       
                                                    .padding(.trailing, getRelativeWidth(16.0))
                                            }
                                            .frame(width: getRelativeWidth(115.0),
                                                   height: getRelativeHeight(33.0), alignment: .leading)
                                            .overlay(RoundedCorners(topLeft: 16.5, topRight: 16.5,
                                                                    bottomLeft: 16.5, bottomRight: 16.5)
                                                    .stroke(ColorConstants.Cyan800,
                                                            lineWidth: 1))
                                            .background(RoundedCorners(topLeft: 16.5, topRight: 16.5,
                                                                       bottomLeft: 16.5, bottomRight: 16.5)
                                                    .fill(ColorConstants.WhiteA700))
                                        }
                                    }
                                }
//                                    .animation(.easeInOut, value: viewModel.selectedGenderType)
                                .padding(.top, getRelativeHeight(14.0))
                            }
                            .frame(width: getRelativeWidth(364.0),
                                   alignment: .center)
                            .padding(.leading, getRelativeWidth(11.0))
                            .padding(.trailing, getRelativeWidth(11.0))
                        }
                        Group {
                            Button {
                                isLocationLoading.toggle()
                                locationManager.requestUserCurrentLocation { _, placemark in
                                    // you should always update your UI in the main thread
                                    
                                    
                                    
                                    Utilities.enQueue(after: .now() + 0.5) {
                                        var value: String = .init()
                                        if let administrativeArea = placemark?.administrativeArea {
                                            value = administrativeArea
                                        }
                                        if let locality = placemark?.locality {
                                            value += ", \(locality)"
                                        }
                                        if let name = placemark?.name {
                                            value += ", \(name)"
                                        }
                                            
                                        self.locationTitle = value
                                        self.isLocationLoading.toggle()
                                    }
                                    
                                    
                                }
                            } label: {
                                HStack {
                                    Image("img_gpsfixedfill")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: getRelativeWidth(24.0),
                                               height: getRelativeWidth(24.0), alignment: .center)
                                           
                                        .padding(.leading, getRelativeWidth(19.0))
                                    VStack(alignment: .leading, spacing: 0) {
                                        Text("Your Current Location")
                                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
                                            .fontWeight(.medium)
                                            .foregroundColor(ColorConstants.Cyan8007f)
                                            .multilineTextAlignment(.leading)
                                            .padding(.trailing)
                                        
                                        if isLocationLoading {
                                            ProgressView()
                                            
                                        } else {
                                            if let locationTitle = self.locationTitle {
                                                Text(locationTitle)
                                                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                                    .fontWeight(.medium)
                                                    .foregroundColor(ColorConstants.Cyan800)
                                                    .multilineTextAlignment(.leading)
                                            }
                                        }
                                    }
                                    .frame(height: getRelativeHeight(37.0),
                                           alignment: .leading)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding(.vertical, getRelativeHeight(14.0))
                                    .padding(.leading, getRelativeWidth(8.0))
                                }
                                .frame(width: getRelativeWidth(367.0), height: getRelativeHeight(61.0),
                                       alignment: .leading)
                                .overlay(Capsule()
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                                .background(Capsule()
                                    .fill(ColorConstants.Cyan8003f))
                                .padding(.top, getRelativeHeight(20.0))
                                .padding(.horizontal, getRelativeWidth(11.0))
                                .frame(maxWidth: .infinity)
                            }

                            Divider()
                                .frame(width: getRelativeWidth(370.0), height: getRelativeHeight(1.0),
                                       alignment: .center)
                                .background(ColorConstants.Cyan80035)
                                .padding(.top, getRelativeHeight(20.0))
                                .padding(.horizontal, getRelativeWidth(9.0))
                            
                            VStack(alignment: .leading, spacing: 0) {
                                VStack(alignment: .leading, spacing: 0) {
                                    Text("Select Categories")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(25.0), alignment: .topLeading)
                                        .padding(.trailing)
                                        .padding(.vertical, getRelativeHeight(13.0))
                                    
                                    ScrollView(.horizontal) {
                                        HStack(spacing: 9.relativeWidth) {
                                            ForEach(filtersModel.categories) { item in
                                                Button(action: {
                                                    viewModel.updateCategory(item)
                                                }, label: {
                                                    MainCategoryCellView(item: item, isSelected: viewModel.selectedMainCategory == item)
                                                })
                                            }
                                        }
                                    }
                                    .scrollClipDisabled()
                                    .safeAreaPadding(.horizontal)
                                }
                                    
                                Divider()
                                    .frame(width: getRelativeWidth(370.0),
                                           height: getRelativeHeight(1.0), alignment: .leading)
                                    .background(ColorConstants.Cyan80035)
                                    .padding(.top, getRelativeHeight(20.0))
                            }
                                
                            .background(RoundedCorners(topLeft: 40.0, topRight: 40.0, bottomLeft: 40.0,
                                                       bottomRight: 40.0).fill(.clear))
                            .padding(.top, getRelativeHeight(17.0))
                            .padding(.leading, getRelativeWidth(11.0))
                            .padding(.trailing, getRelativeWidth(7.0))
                        
                            VStack(alignment: .leading, spacing: 0) {
                                VStack(alignment: .leading, spacing: 0) {
                                    Text("Services Categories")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(25.0), alignment: .topLeading)
                                        .padding(.trailing)
                                        .padding(.bottom, 8)
                                        .padding(.vertical, getRelativeHeight(13.0))
                                    if viewModel.isFilterServiceCategoriesLoading {
                                        WrappingHStack(0 ... 3, id: \.self, alignment: .center, spacing: .constant(8), lineSpacing: 8) { _ in
                                            RowserviceiteShimmerCell()
                                        }
                                        .shimmerize()
                                    } else {
                                        WrappingHStack(viewModel.filterServiceCategories, id: \.self, alignment: .center, spacing: .constant(8), lineSpacing: 8) { item in
                                            Button {
                                                viewModel.onServiceSelection(item)
                                            } label: {
                                                let selected = viewModel.selectedFilterServiceCategories.contains(item)
                                                RowserviceiteCell(selected: selected, title: item.catName)
                                            }
                                        }
                                    }
                                }
                                    
                                Divider()
                                    .frame(width: getRelativeWidth(370.0),
                                           height: getRelativeHeight(1.0), alignment: .leading)
                                    .background(ColorConstants.Cyan80035)
                                    .padding(.top, getRelativeHeight(20.0))
                            }
                                
                            .background(RoundedCorners(topLeft: 40.0, topRight: 40.0, bottomLeft: 40.0,
                                                       bottomRight: 40.0).fill(.clear))
                            .padding(.top, getRelativeHeight(17.0))
                            .padding(.leading, getRelativeWidth(11.0))
                            .padding(.trailing, getRelativeWidth(7.0))
                            .animation(.bouncy, value: viewModel.filterServiceCategories)
                            VStack(alignment: .leading, spacing: 0) {
                                VStack(alignment: .leading, spacing: 0) {
                                    Text("Select Time")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(98.0),
                                               height: getRelativeHeight(25.0), alignment: .topLeading)
                                        .padding(.trailing)
                                        .padding(.vertical, getRelativeHeight(13.0))
                                        
//                                    let sortedMap = Array(filtersModel.timeslots).sorted { $0.key.toInt < $1.key.toInt }
//                                    let (_, values) = unzip(sortedMap)
                                        
                                    WrappingHStack(filtersModel.timeslots, id: \.self, alignment: .center, lineSpacing: 8) { item in
                                        Button {
                                            viewModel.onTimeSelection(item)
                                        } label: {
                                            RowtimeCell(selected: viewModel.selectedTime.contains(item), title: item)
                                        }

                                        //
                                    }
                                    .animation(.bouncy, value: viewModel.selectedTime)
                                }
                                   
                                .padding(.leading, getRelativeWidth(4.0))
                                .padding(.trailing, getRelativeWidth(4.0))
                                Divider()
                                    .frame(width: getRelativeWidth(370.0),
                                           height: getRelativeHeight(1.0), alignment: .leading)
                                    .background(ColorConstants.Cyan80035)
                                    .padding(.top, getRelativeHeight(20.0))
                            }
                            .frame(width: getRelativeWidth(370.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 22.0, topRight: 22.0, bottomLeft: 22.0,
                                                       bottomRight: 22.0).fill(.clear))
                            .padding(.top, getRelativeHeight(12.0))
                            .padding(.leading, getRelativeWidth(11.0))
                            .padding(.trailing, getRelativeWidth(7.0))
                            
                            
                            VStack(alignment: .leading, spacing: 0) {
                                VStack(alignment: .leading, spacing: 0) {
                                    Text("Select Rating")
                                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                        .fontWeight(.bold)
                                        .foregroundColor(ColorConstants.Black900)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(width: getRelativeWidth(98.0),
                                               height: getRelativeHeight(25.0), alignment: .topLeading)
                                        .padding(.trailing)
                                        .padding(.vertical, getRelativeHeight(13.0))
                                        
                                    WrappingHStack(filtersModel.ratings, id: \.self, alignment: .leading, lineSpacing: 8) { item in
                                        Button {
                                            viewModel.onRatingSelection(item)
                                        } label: {
                                            CircularRatingCell(selected: viewModel.selectedRating.contains(item), title: item)
                                        }

                                        //
                                    }
                                    .animation(.bouncy, value: viewModel.selectedTime)
                                }
                                   
                                .padding(.leading, getRelativeWidth(4.0))
                                .padding(.trailing, getRelativeWidth(4.0))
                                Divider()
                                    .frame(width: getRelativeWidth(370.0),
                                           height: getRelativeHeight(1.0), alignment: .leading)
                                    .background(ColorConstants.Cyan80035)
                                    .padding(.top, getRelativeHeight(20.0))
                            }
                            .frame(width: getRelativeWidth(370.0),
                                   alignment: .center)
                            .background(RoundedCorners(topLeft: 22.0, topRight: 22.0, bottomLeft: 22.0,
                                                       bottomRight: 22.0).fill(.clear))
                            .padding(.top, getRelativeHeight(12.0))
                            .padding(.leading, getRelativeWidth(11.0))
                            .padding(.trailing, getRelativeWidth(7.0))
                            
                            
                            Text("Distance (KM)")
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Black900)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(117.0), height: getRelativeHeight(25.0),
                                       alignment: .topLeading)
                                .padding(.vertical, getRelativeHeight(14.0))
                                .padding(.bottom)
                                .padding(.horizontal, getRelativeWidth(17.0))
                        }
                        Group {
//                                        SliderView()
//                                        .padding(.top, 8)
//                                            .frame(maxWidth: .infinity)
                            let min = filtersModel.distance.min
                            let max = filtersModel.distance.max
                                
                            CustomSliderView(value: $viewModel.distance, sliderRange: min.toDouble ... max.toDouble, thumbColor: .init(hex: "#008F96"), minTrackColor: .init(hex: "#008F96"), maxTrackColor: Color(red: 0.85, green: 0.95, blue: 0.97), labelView: {
                                VStack(spacing: -2) {
                                    DistanceFormattedTextView(amount: viewModel.distance)
                                        .contentTransition(.numericText(value: viewModel.distance))
//                                                .animation(.bouncy, value: distance)
                                        .font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
                                        .fontWeight(.medium)
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        //                            .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.leading)
                                        .frame(
                                            height: getRelativeHeight(17.0), alignment: .center)
                                        .padding(.horizontal, getRelativeWidth(4.0))
                                        .padding(.vertical, 2.0.relativeHeight)
                                        .background(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                                                   bottomRight: 3.0)
                                                .fill(ColorConstants.Cyan800))
                                        
                                    Image("img_vector401")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: getRelativeWidth(8.0), height: getRelativeHeight(8.0),
                                               alignment: .center)
                                          
                                    //                        .background(.orange)
                                }
                            })
                            .frame(height: 30)
                            .frame(maxWidth: .infinity)
                            .padding()
                               
                            HStack {
                                Text("\(min) KM")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(29.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                                Spacer()
                                Text("\(max) KM")
                                    .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                                    .fontWeight(.regular)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .minimumScaleFactor(0.5)
                                    .multilineTextAlignment(.leading)
                                    .frame(width: getRelativeWidth(45.0),
                                           height: getRelativeHeight(17.0), alignment: .topLeading)
                            }
                            .frame(width: getRelativeWidth(363.0), height: getRelativeHeight(18.0),
                                   alignment: .center)
                            .padding(.horizontal, getRelativeWidth(11.0))
                        }
                    }
                   
                    .padding(.top, getRelativeHeight(16.0))
                    .background(.white)
                    .clipShape(UnevenRoundedRectangle(topLeadingRadius: 38, bottomLeadingRadius: 0, bottomTrailingRadius: 0, topTrailingRadius: 38))
                    .shadow(color: .black.opacity(0.13), radius: 3.95, x: 3, y: -10)
                    .overlay(RoundedCorners(topLeft: 38.0, topRight: 38.0)
                        .stroke(ColorConstants.Cyan800, lineWidth: 1))
                    .padding(.top, getRelativeHeight(24.0))
                }
            }
//
            .if(viewModel.pageState != .loading(true), transform: {
                $0.safeAreaInset(edge: .bottom) {
                    HStack(spacing: 8) {
                        Button(action: {
                            dismiss()
                            onApplyFilter(viewModel.getFiltersRequest())
                        }, label: {
                            Text("Apply Filter")
                                .font(FontScheme
                                    .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.heavy)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(13.0))
                                .foregroundColor(ColorConstants.WhiteA700)
//                                    .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(
                                    height: getRelativeHeight(47.0), alignment: .center)
                                .frame(maxWidth: .infinity)
                                .background(Capsule()
                                    .fill(ColorConstants.Cyan800))
                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0,
                                        y: 1)
                                    
                        })
                        .disableWithOpacity(viewModel.disableApplyButtons)
                        .background(ColorConstants.Cyan800.clipShape(.capsule))

                        Button(action: {
                            dismiss()
                            onApplyFilter(nil)
                        }, label: {
                            Text("Clear")
                                .font(FontScheme
                                    .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                .fontWeight(.heavy)
                                .padding(.horizontal, getRelativeWidth(30.0))
                                .padding(.vertical, getRelativeHeight(13.0))
                                .foregroundColor(ColorConstants.Cyan800)
//                                    .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.center)
                                .frame(
                                    height: getRelativeHeight(47.0), alignment: .center)
                                   
                                .background(ColorConstants.WhiteA700.clipShape(.capsule))
                                .clipped()
                                .overlay(Capsule()
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0,
                                        y: 1)
                                    
                        })
                        .disableWithOpacity(viewModel.model == nil)
                        .background(ColorConstants.WhiteA700.clipShape(.capsule))
                            
                    }.padding(.horizontal, getRelativeWidth(12.0))
                }
            })
               
        })
        .onChange(of: viewModel.selectedMainCategory) { oldValue, newValue in
          
                if let newValue = newValue, oldValue != newValue {
                    Utilities.enQueue(after: .now()+0.5) {
                        viewModel.getFilterServiceCategories(categoryID: newValue.categoryID)
                    }
                  
                }
            
        }
        .onLoad {
            locationManager.requestUserCurrentLocation { coordinate, placemark in
                // you should always update your UI in the main thread
                DispatchQueue.main.async {
                    var value: String = .init()
                    if let administrativeArea = placemark?.administrativeArea {
                        value = administrativeArea
                    }
                    if let locality = placemark?.locality {
                        value += ", \(locality)"
                    }
                    if let name = placemark?.name {
                        value += ", \(name)"
                    }
                    self.viewModel.coordinates = coordinate
                    self.locationTitle = value
                }
            }
        }
    }
}

// struct FilterView_Previews: PreviewProvider {
//    static var previews: some View {
//        NavigationStack{
//            FilterView(onApplyFilter: {}).attachAllEnvironmentObjects()
//        }
//    }
// }

struct MainCategoryCellView: View {
    let item: FiltersModel.Category
    let isSelected: Bool
    var body: some View {
        VStack(spacing: 8.0.relativeHeight) {
            NetworkImageView(path: item.iconUrl, originalColor: false)
                .foregroundStyle(!isSelected ? ColorConstants.Cyan800 : Color.white)
                .frame(width: 36.relativeFontSize, height: 36.relativeFontSize)
                .clipped()
                .frame(width: 53.relativeFontSize, height: 53.relativeFontSize)
                .background(isSelected ? ColorConstants.Cyan800.clipShape(.circle) : Color.white.clipShape(.circle))
                .overlay(
                    Circle()
                        .stroke(Color(red: 0, green: 0.56, blue: 0.59), lineWidth: 1)
                )
//                .overlay(alignment: .topTrailing) {
//                    // Checkmark icon if selected
//                    if isSelected {
//                        Image(.staffCheckmark)
//                            .resizable()
//                            .padding(4)
//                            .frame(width: 20.relativeFontSize, height: 20.relativeFontSize)
//                            .offset(x: 3, y: -1)
//                    }
//                }
            
            Text(item.name)
                .font(Font.custom("Nunito", size: 12).weight(.bold))
                .kerning(0.52)
                .multilineTextAlignment(.center)
                .foregroundColor(Color(red: 0, green: 0.56, blue: 0.59))
        }
    }
}

#Preview {
    NavigationStack {
        FilterView(model: nil, onApplyFilter: { _ in
            
        }).attachAllEnvironmentObjects()
    }
}
