//
//  FiltersModel.swift
//  Bookme
//
//  Created by Apple on 23/04/2024.
//

import Foundation
import SwiftUICore

struct FiltersRequest: Encodable, Equatable, Hashable {
    var searchString: String?
    var gender, category, service, times, rating: String?
    var latitude, longitude: Double?
    var distance: Int?
    var results: Int = 1
}

enum GenderType: String, CaseIterable, Identifiable {
    var id: UUID {
        switch self {
        case .male,
             .female
             :
            UUID()
        }
    }

    case male, female
    
    
    var title: LocalizedStringKey {
        switch self {
        case .male:
            return "Male"
        case .female:
            return "Female"
        }
    }
}

// MARK: - DataClass

struct FiltersModel: Codable {
    let categories: [Category]
    let timeslots: [String]
    let ratings: [Int]
    let distance: Distance
    let genders: [Gender]

    // MARK: - Distance

    struct Distance: Codable {
        let min, max: Int
    }

    // MARK: - Gender

    struct Gender: Codable, Identifiable, Equatable, Hashable {
        let id: UUID = .init()
        let vendorType: Int
        let typeName: String

        enum CodingKeys: String, CodingKey {
            case vendorType = "Vendor_Type"
            case typeName = "Type_Name"
        }
    }

    // MARK: - Service

    struct Category: Codable, Identifiable, Equatable, Hashable {
        let categoryID: Int
        let name, icon: String

        enum CodingKeys: String, CodingKey {
            case categoryID = "Cat_ID"
            case name = "Cat_Name"
            case icon = "Icon"
        }

        var iconUrl: String { AppConstants.Server.baseURL + icon }

        var id: Int {
            categoryID
        }
    }

//    struct Service: Codable, Identifiable, Equatable, Hashable {
//        let id: UUID = .init()
//        let servicesID: ServicesID
//        let name, image: String
//
//        enum CodingKeys: String, CodingKey {
//            case servicesID = "ServicesID"
//            case name = "Name"
//            case image = "Image"
//        }
//
//        var serviceIDInt: Int {
//            switch servicesID {
//            case .integer(let int):
//                return int
//            case .string(_):
//                return 0
//            }
//        }
//    }

    enum ServicesID: Codable, Equatable, Hashable {
        case integer(Int)
        case string(String)

        init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            if let x = try? container.decode(Int.self) {
                self = .integer(x)
                return
            }
            if let x = try? container.decode(String.self) {
                self = .string(x)
                return
            }
            throw DecodingError.typeMismatch(ServicesID.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Wrong type for ServicesID"))
        }

        func encode(to encoder: Encoder) throws {
            var container = encoder.singleValueContainer()
            switch self {
            case .integer(let x):
                try container.encode(x)
            case .string(let x):
                try container.encode(x)
            }
        }
    }
}

// MARK: - FilterServiceCategoriesModel

struct FilterServiceCategoriesModel: Codable {
    let serviceCategories: [ServiceCategory]
    
    
    enum CodingKeys: String, CodingKey {
        case serviceCategories = "servicecategories"
    }

    // MARK: - ServiceCategory

    struct ServiceCategory: Codable, Identifiable, Equatable, Hashable {
        let catName: String
        let catID: Int
        
        
        var id: Int { catID }

        enum CodingKeys: String, CodingKey {
            case catName = "Cat_Name"
            case catID = "Cat_ID"
        }
    }
}
