import Foundation
import MapKit
import SwiftUI

class FilterViewModel: SuperViewModel {
    @Published var filtersModel: FiltersModel?
    @Published var selectedGenderType: [FiltersModel.Gender] = []
    
    @Published var filterServiceCategories: [FilterServiceCategoriesModel.ServiceCategory] = []
    @Published var selectedFilterServiceCategories: [FilterServiceCategoriesModel.ServiceCategory] = []
    
    @Published private(set) var selectedMainCategory: FiltersModel.Category?
    
    @Published var selectedTime: [String] = []
    
    @Published var selectedRating: [Int] = []
    
    private(set) var isInitialized: Bool = false
    
    @Published var coordinates: CLLocationCoordinate2D?
    @Published var distance: Double = 1
    let model: FiltersRequest?
    
    init(model: FiltersRequest?) {
        self.model = model
        super.init()
      
        getFilterDetails(model: model)
    }
    
    func updateCategory(_ value: FiltersModel.Category?) {
        selectedMainCategory = value
    }
    
    func getFiltersRequest() -> FiltersRequest {
        var value: FiltersRequest = .init()
        if !selectedGenderType.isEmpty {
            value.gender = selectedGenderType.map { String($0.typeName) }.joined(separator: ",")
        }
        
        if let selectedMainCategory = selectedMainCategory {
            value.category = String(selectedMainCategory.categoryID)
        }
        
        if !selectedFilterServiceCategories.isEmpty {
            value.service = selectedFilterServiceCategories.map { String($0.catID) }.joined(separator: ",")
        }
        if !selectedTime.isEmpty {
            value.times = selectedTime.map { String($0) }.joined(separator: ",")
        }
        
        if !selectedRating.isEmpty {
            value.rating = selectedRating.map { String($0) }.joined(separator: ",")
        }
        
        if let coordinates = coordinates {
            value.latitude = coordinates.latitude
            value.longitude = coordinates.longitude
        }
        
        value.distance = Int(distance)
        
        return value
    }
    
    func assignInitialValues(model: FiltersRequest?) {
        if let model = model {
            if let gender = model.gender,
               let genders = filtersModel?.genders
            {
                let mainArray = genders
                let genderIDList: [String] = gender.components(separatedBy: ",")
                let matchingItems = mainArray.filter { item1 in genderIDList.contains { $0 == item1.typeName } }
                selectedGenderType = matchingItems
            }
            
            if let service = model.service {
                let mainArray = filterServiceCategories
                let serviceIDList: [String] = service.components(separatedBy: ",")
                let matchingItems = mainArray.filter { item in serviceIDList.contains { Int($0) == item.catID } }
                selectedFilterServiceCategories = matchingItems
            }
            
            if let times = model.times,
               let timeslots = filtersModel?.timeslots
            {
                let mainArray = timeslots
                let timeslotsIDList: [String] = times.components(separatedBy: ",")
                let matchingItems = mainArray.filter { item in timeslotsIDList.contains { $0 == item } }
                selectedTime = matchingItems
            }
            
            if let rating = model.rating,
               let ratingsSlot = filtersModel?.ratings
            {
                let mainArray = ratingsSlot
                let timeslotsIDList: [String] = rating.components(separatedBy: ",")
                let matchingItems = mainArray.filter { item in timeslotsIDList.contains { $0 == String(item )} }
                selectedRating = matchingItems
            }
            
            if let distance = model.distance {
                self.distance = Double(distance)
            }
            
            selectedMainCategory = filtersModel?.categories.first(where: { String($0.categoryID) == model.category })
           
        } else {
//            if let firstGender = filtersModel?.genders.first {
//                selectedGenderType.append(firstGender)
//            }
//
//            if let firstService = filtersModel?.services.first {
//                selectedService.append(firstService)
//            }
            
            selectedMainCategory = filtersModel?.categories.first
            
            if let distance = filtersModel?.distance.max {
                self.distance = Double(distance)
            }
        }
    }
    
    var disableApplyButtons: Bool { selectedGenderType.isEmpty && selectedFilterServiceCategories.isEmpty && selectedTime.isEmpty && selectedRating.isEmpty && (distance == 1) && selectedMainCategory == nil }
    
    func onServiceSelection(_ value: FilterServiceCategoriesModel.ServiceCategory) {
        if selectedFilterServiceCategories.contains(value) {
            selectedFilterServiceCategories.removeAll(where: { $0 == value })
        } else {
            selectedFilterServiceCategories.append(value)
        }
    }
    
    func onTimeSelection(_ value: String) {
//        if let key = filtersModel?.timeslots.key(from: value) {
        if selectedTime.contains(value) {
            selectedTime.removeAll(where: { $0 == value })
        } else {
            selectedTime.append(value)
        }
//        }
    }
    
    func onRatingSelection(_ value: Int) {
        if selectedRating.contains(value) {
            // Deselect this and all ratings above it
            selectedRating.removeAll(where: { $0 >= value })
        } else {
            // Select this and all ratings below it
            selectedRating = Array(1...value)
        }
    }
    
    
    
    func onGenderSelection(_ type: FiltersModel.Gender) {
        if selectedGenderType.contains(type) {
            selectedGenderType.removeAll(where: { $0 == type })
        } else {
            selectedGenderType.append(type)
        }
    }
    
    @Published var isFilterServiceCategoriesLoading: Bool = false
    // Convert the `Published` property to a `Binding`
    var isFilterServiceCategoriesLoadingBinding: Binding<Bool> {
        Binding(
            get: { self.isFilterServiceCategoriesLoading }, // Getter for the binding
            set: { self.isFilterServiceCategoriesLoading = $0 } // Setter for the binding
        )
    }

    func getFilterServiceCategories(categoryID: Int) {
        let parameters: [String: Any] = ["Cat_ID": categoryID]
        onApiCall(api.filterServiceCategories, parameters: parameters, withLoadingIndicator: false, customLoadingBinding: isFilterServiceCategoriesLoadingBinding) {
            self.filterServiceCategories = $0.data?.serviceCategories ?? []
            
            if !self.isInitialized {
                self.assignInitialValues(model: self.model)
                self.isInitialized = true
            }
        }
    }
    
    func getFilterDetails(model: FiltersRequest?) {
        onApiCall(api.filters, parameters: emptyDictionary) {
            self.filtersModel = $0.data
            
            self.assignInitialValues(model: model)
        }
    }
}

extension Dictionary where Value: Equatable {
    func key(from value: Value) -> Key? {
        return first(where: { $0.value == value })?.key
    }
}
