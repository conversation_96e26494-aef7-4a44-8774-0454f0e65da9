//
//  SliderView.swift
//  Bookme
//
//  Created by Apple on 23/02/2024.
//

import SwiftUI
import MultiSlider

//struct SliderView: View {
//    @State private var valueArray: [CGFloat] = [20]
//    
//    
//    var offsetValue:CGFloat {
//        (valueArray.first ?? 0) * 1.6
//    }
//
//    var body: some View {
//        VStack(alignment: .leading, spacing: 8) {
////            MultiValueSlider(
////                value: $valueArray,
////                maximumValue: 10,
////                valueLabelPosition: .top,
////                orientation: .horizontal
////            )
////            .valueLabelTextForThumb { thumbIndex, thumbValue in
////                ["Parasol", "Umbrella"][thumbIndex] + " \(thumbValue)"
////            }
////            .accentColor(.purple)
//            
//        
////                VStack(spacing:-2){
////                   
////                        DistanceFormattedTextView(amount: Double(valueArray.first ?? 0.0) )
////                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
////                            .fontWeight(.medium)
////                            .foregroundColor(ColorConstants.WhiteA700)
//////                            .minimumScaleFactor(0.5)
////                            .multilineTextAlignment(.leading)
////                            .frame(
////                                   height: getRelativeHeight(17.0), alignment: .center)
////                            .padding(.horizontal, getRelativeWidth(4.0))
////                            
////                            .background(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
////                                                       bottomRight: 3.0)
////                                    .fill(ColorConstants.Cyan800))
////                    
////                   
////                   
////                   
////                    Image("img_vector401")
////                        .resizable()
////                        .scaledToFit()
////                        .frame(width: getRelativeWidth(8.0), height: getRelativeHeight(8.0),
////                               alignment: .center)
////                      
////                        
//////                        .background(.orange)
////                } 
////                .offset(x: offsetValue, y: 0)
//                   
//
//            MultiValueSlider(
//                value: $valueArray,
////                            minimumValue: productListModel.minPrice.CGFloatValue(),
//                minimumValue: 1.0,
//                maximumValue: 200,
//                isContinuous: true,
////                valueLabelAlternatePosition: false,
//                
//                orientation: .horizontal,
//                outerTrackColor: UIColor(Color(red: 0.85, green: 0.95, blue: 0.97)),
//                thumbImage: UIImage(resource: .thumb),
//                trackWidth: 6.0.relativeHeight
//            )
//            
//            .isHapticSnap(true)
//            .keepsDistanceBetweenThumbs(false)
////            .snapValues((Int(0) ... Int(10)).map { CGFloat($0) })
////            .snapStepSize(1)
//            .distanceBetweenThumbs(0.00)
//            .showsThumbImageShadow(true)
//            
//            .thumbTintColor(UIColor(ColorConstants.Cyan800))
//            .accentColor(ColorConstants.Cyan800)
//            .frame(width: getRelativeWidth(362.0), height: getRelativeHeight(21.0),
//                   alignment: .center)
//
//           
//        }
//    }
//    
//    let thumbImageSystem = UIImage(
//        systemName: "thumb_image",
//        withConfiguration: UIImage.SymbolConfiguration(pointSize: 30)
//            .applying(UIImage.SymbolConfiguration(weight: .black))
//    )?
//        .withTintColor(.darkGray, renderingMode: .alwaysOriginal)
//}


struct DistanceFormattedTextView: View {
    var amount: Double?
    var body: some View {
        Text( String(format: "%.0f KM", amount ?? 0.0))
            .bold()
    }
}

struct PreviewContainer: View {
    @State private var value:Double = 1
    var body: some View {
        VStack{
            Text("value: \(value, specifier: "%.0F")")
            CustomSliderView(value: $value,sliderRange: 1...200,thumbColor: .init(hex: "#008F96"),minTrackColor: .init(hex: "#008F96"), maxTrackColor: Color(red: 0.85, green: 0.95, blue: 0.97), labelView: {
                
                VStack(spacing:-2){
                   
                        DistanceFormattedTextView(amount: value )
                        .contentTransition(.numericText(value: value))
                        .animation(.bouncy, value: value)
                            .font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
                            .fontWeight(.medium)
                            .foregroundColor(ColorConstants.WhiteA700)
//                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(
                                   height: getRelativeHeight(17.0), alignment: .center)
                            .padding(.horizontal, getRelativeWidth(4.0))
                            
                            .background(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                                       bottomRight: 3.0)
                                    .fill(ColorConstants.Cyan800))
                    
                   
                   
                   
                    Image("img_vector401")
                        .resizable()
                        .scaledToFit()
                        .frame(width: getRelativeWidth(8.0), height: getRelativeHeight(8.0),
                               alignment: .center)
                      
                        
//                        .background(.orange)
                }
            })
            
            
                .frame(height:30)
                .frame(maxWidth: .infinity)
                .padding()
        }
    }
}

#Preview {

        PreviewContainer()
    
}




struct CustomSliderView<Content:View>: View {
    @Binding var value: Double
    
    @State var lastCoordinateValue: CGFloat = 0.0
    var sliderRange: ClosedRange<Double> = 1...100
    var thumbColor: Color = .yellow
    var minTrackColor: Color = .blue
    var maxTrackColor: Color = .gray
    @ViewBuilder let labelView: Content
    
    var body: some View {
        GeometryReader { gr in
            let thumbSize = gr.size.height * 0.8
            let radius = gr.size.height * 0.5
            let minValue = gr.size.width * 0.0
            let maxValue = (gr.size.width) - thumbSize
            
            let scaleFactor = (maxValue - minValue) / (sliderRange.upperBound - sliderRange.lowerBound)
            let lower = sliderRange.lowerBound
            let sliderVal = (self.value - lower) * scaleFactor + minValue
            
            ZStack {
                Rectangle()
                    .foregroundColor(maxTrackColor)
                    .frame(width: gr.size.width, height: gr.size.height * 0.2)
                    .clipShape(RoundedRectangle(cornerRadius: radius))
                HStack {
                    Rectangle()
                        .foregroundColor(minTrackColor)
                    .frame(width: sliderVal, height: gr.size.height * 0.2)
                    Spacer()
                }
                .clipShape(RoundedRectangle(cornerRadius: radius))
                HStack {
                    Circle()
                        .foregroundColor(thumbColor)
                        .frame(width: thumbSize, height: thumbSize)
                        .shadow(color: .black.opacity(0.49), radius: 1,x: 0, y: 1)
                        .offset(x: sliderVal)
                        .gesture(
                            DragGesture(minimumDistance: 0)
                                .onChanged { v in
                                    if (abs(v.translation.width) < 0.1) {
                                        self.lastCoordinateValue = sliderVal
                                    }
                                    if v.translation.width > 0 {
                                        let nextCoordinateValue = min(maxValue, self.lastCoordinateValue + v.translation.width)
                                        self.value = ((nextCoordinateValue - minValue) / scaleFactor)  + lower
                                    } else {
                                        let nextCoordinateValue = max(minValue, self.lastCoordinateValue + v.translation.width)
                                        self.value = ((nextCoordinateValue - minValue) / scaleFactor) + lower
                                    }
                               }
                        )
                        .overlay(alignment: .center) {
                            labelView
//                                .scaleEffect(1)
                                .frame(width: 100,height: 100)
                                .offset(x: sliderVal, y: -30)
                        }
                        
                    Spacer()
                }
            }
        }
    }
}
