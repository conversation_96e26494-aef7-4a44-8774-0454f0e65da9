//
//  FilterShimmerView.swift
//  Bookme
//
//  Created by Apple on 24/08/2024.
//


import SwiftUI
import WrappingHStack

struct FilterShimmerView: View {
    var body: some View {
        VStack {
            VStack(alignment: .leading, spacing: 0) {
                GenderSection
                Group {
                    Button {} label: {
                        HStack {
                            Image("img_gpsfixedfill")
                                .resizable()
                                .scaledToFit()
                                .frame(width: getRelativeWidth(24.0),
                                       height: getRelativeWidth(24.0), alignment: .center)
                                   
                                .padding(.leading, getRelativeWidth(19.0))
                            VStack(alignment: .leading, spacing: 0) {
                                Text(StringConstants.kMsgYourCurrentLo)
                                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.Cyan8007f)
                                    .multilineTextAlignment(.leading)
                                    .padding(.trailing)
                                Text("locationTitle")
                                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(13.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .multilineTextAlignment(.leading)
                            }
                            .frame(height: getRelativeHeight(37.0),
                                   alignment: .leading)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.vertical, getRelativeHeight(14.0))
                            .padding(.leading, getRelativeWidth(8.0))
                        }
                        .frame(width: getRelativeWidth(367.0), height: getRelativeHeight(61.0),
                               alignment: .leading)
                        .overlay(RoundedCorners(topLeft: 30.5, topRight: 30.5, bottomLeft: 30.5,
                                                bottomRight: 30.5)
                                .stroke(ColorConstants.Cyan800,
                                        lineWidth: 1))
                        .background(RoundedCorners(topLeft: 30.5, topRight: 30.5, bottomLeft: 30.5,
                                                   bottomRight: 30.5)
                                .fill(ColorConstants.Cyan8003f))
                        
                        .padding(.top, getRelativeHeight(20.0))
                        .padding(.horizontal, getRelativeWidth(11.0))
                    }

                    Divider()
                        .frame(width: getRelativeWidth(370.0), height: getRelativeHeight(1.0),
                               alignment: .center)
                        .background(ColorConstants.Cyan80035)
                        .padding(.top, getRelativeHeight(20.0))
                        .padding(.horizontal, getRelativeWidth(9.0))
                    VStack(alignment: .leading, spacing: 0) {
                        VStack(alignment: .leading, spacing: 0) {
                            Text(StringConstants.kLblServices)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Black900)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(74.0),
                                       height: getRelativeHeight(25.0), alignment: .topLeading)
                                .padding(.trailing)
                                .padding(.vertical, getRelativeHeight(13.0))
                            WrappingHStack(0...5, id: \.self, alignment: .leading, spacing: .constant(8), lineSpacing: 8) { _ in
                                Button {} label: {
                                    RowserviceiteCell(selected: false, title: "item")
                                }
                            }
                        }
                        Divider()
                            .frame(width: getRelativeWidth(370.0),
                                   height: getRelativeHeight(1.0), alignment: .leading)
                            .background(ColorConstants.Cyan80035)
                            .padding(.top, getRelativeHeight(20.0))
                    }
                        
                    .background(RoundedCorners(topLeft: 40.0, topRight: 40.0, bottomLeft: 40.0,
                                               bottomRight: 40.0).fill(.clear))
                    .padding(.top, getRelativeHeight(17.0))
                    .padding(.leading, getRelativeWidth(11.0))
                    .padding(.trailing, getRelativeWidth(7.0))
                    VStack(alignment: .leading, spacing: 0) {
                        VStack(alignment: .leading, spacing: 0) {
                            Text(StringConstants.kLblSelectTime)
                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                                .fontWeight(.bold)
                                .foregroundColor(ColorConstants.Black900)
                                .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(width: getRelativeWidth(98.0),
                                       height: getRelativeHeight(25.0), alignment: .topLeading)
                                .padding(.trailing)
                                .padding(.vertical, getRelativeHeight(13.0))
                                
                            WrappingHStack(0...7, id: \.self, alignment: .leading, lineSpacing: 8) { _ in
                                Button {} label: {
                                    RowtimeCell(selected: false, title: "11:00")
                                }

//
                            }
                        }
                           
                        .padding(.leading, getRelativeWidth(4.0))
                        .padding(.trailing, getRelativeWidth(4.0))
                        Divider()
                            .frame(width: getRelativeWidth(370.0),
                                   height: getRelativeHeight(1.0), alignment: .leading)
                            .background(ColorConstants.Cyan80035)
                            .padding(.top, getRelativeHeight(20.0))
                    }
                    .frame(width: getRelativeWidth(370.0),
                           alignment: .center)
                    .background(RoundedCorners(topLeft: 22.0, topRight: 22.0, bottomLeft: 22.0,
                                               bottomRight: 22.0).fill(.clear))
                    .padding(.top, getRelativeHeight(12.0))
                    .padding(.leading, getRelativeWidth(11.0))
                    .padding(.trailing, getRelativeWidth(7.0))
                    Text(StringConstants.kLblDistanceKm)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(18.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Black900)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(117.0), height: getRelativeHeight(25.0),
                               alignment: .topLeading)
                        .padding(.vertical, getRelativeHeight(14.0))
                        .padding(.bottom)
                        .padding(.horizontal, getRelativeWidth(17.0))
                }
                Group {
                    CustomSliderView(value: .constant(1.0), sliderRange: 1...200, thumbColor: .init(hex: "#008F96"), minTrackColor: .init(hex: "#008F96"), maxTrackColor: Color(red: 0.85, green: 0.95, blue: 0.97), labelView: {
                        VStack(spacing: -2) {
                            DistanceFormattedTextView(amount: 1.0)
                                .contentTransition(.numericText(value: 1.0))
                                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(12.0)))
                                .fontWeight(.medium)
                                .foregroundColor(ColorConstants.WhiteA700)
                                //                            .minimumScaleFactor(0.5)
                                .multilineTextAlignment(.leading)
                                .frame(
                                    height: getRelativeHeight(17.0), alignment: .center)
                                .padding(.horizontal, getRelativeWidth(4.0))
                                .padding(.vertical, 2.0.relativeHeight)
                                .background(RoundedCorners(topLeft: 3.0, topRight: 3.0, bottomLeft: 3.0,
                                                           bottomRight: 3.0)
                                        .fill(ColorConstants.Cyan800))
                                
                            Image("img_vector401")
                                .resizable()
                                .scaledToFit()
                                .frame(width: getRelativeWidth(8.0), height: getRelativeHeight(8.0),
                                       alignment: .center)
                                  
                            //                        .background(.orange)
                        }
                    })
                    .frame(height: 30)
                    .frame(maxWidth: .infinity)
                    .padding()
                       
                    HStack {
                        Text("\(1) KM")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(29.0),
                                   height: getRelativeHeight(17.0), alignment: .topLeading)
                        Spacer()
                        Text("\(200) KM")
                            .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                            .fontWeight(.regular)
                            .foregroundColor(ColorConstants.Cyan800)
                            .minimumScaleFactor(0.5)
                            .multilineTextAlignment(.leading)
                            .frame(width: getRelativeWidth(45.0),
                                   height: getRelativeHeight(17.0), alignment: .topLeading)
                    }
                    .frame(width: getRelativeWidth(363.0), height: getRelativeHeight(18.0),
                           alignment: .center)
                    .padding(.horizontal, getRelativeWidth(11.0))
                }
            }
                
            .padding(.top, getRelativeHeight(16.0))
            .overlay(RoundedCorners(topLeft: 38.0, topRight: 38.0)
                .stroke(ColorConstants.Cyan800, lineWidth: 1))
            .background(RoundedCorners(topLeft: 38.0, topRight: 38.0)
                .fill(ColorConstants.WhiteA7003f))
            .padding(.top, getRelativeHeight(24.0))
        }
        .shimmerize()
    }
    
    var GenderSection: some View {
        Group {
            VStack(alignment: .leading, spacing: 0) {
                Text(StringConstants.kLblSelectGender)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Black900)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(110.0),
                           height: getRelativeHeight(22.0), alignment: .topLeading)
                    .padding(.horizontal)
                HStack {
                    ForEach(0...2, id: \.self) { _ in
                        
                        Button {} label: {
                            HStack {
                                VStack {
                                    ZStack {}
                                       
                                        .frame(width: getRelativeWidth(13.0),
                                               height: getRelativeWidth(13.0),
                                               alignment: .center)
                                        .background(Circle()
                                            .fill(ColorConstants.Cyan8003f))
                                        .padding(.all, getRelativeWidth(4.0))
                                }
                                .frame(width: getRelativeWidth(21.0),
                                       height: getRelativeWidth(21.0), alignment: .center)
                                .overlay(Circle()
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                                .background(Circle()
                                    .fill(Color.clear.opacity(0.7)))
                                .padding(.leading, getRelativeWidth(9.0))
                                Text("typeName")
                                    .font(FontScheme
                                        .kNunitoMedium(size: getRelativeHeight(14.0)))
                                    .fontWeight(.medium)
                                    .foregroundColor(ColorConstants.Cyan800)
                                    .fixedSize()
                                    .multilineTextAlignment(.leading)
                                    .frame(
                                        height: getRelativeHeight(20.0),
                                        alignment: .topLeading)
                                   
                                    .padding(.trailing, getRelativeWidth(32.0))
                            }
                            .frame(
                                height: getRelativeHeight(33.0), alignment: .center)
                            .overlay(RoundedCorners(topLeft: 16.5, topRight: 16.5,
                                                    bottomLeft: 16.5, bottomRight: 16.5)
                                    .stroke(ColorConstants.Cyan800,
                                            lineWidth: 1))
                            .background(RoundedCorners(topLeft: 16.5, topRight: 16.5,
                                                       bottomLeft: 16.5, bottomRight: 16.5)
                                    .fill(ColorConstants.WhiteA700))
                        }
                    }
                }
//                                    .animation(.easeInOut, value: viewModel.selectedGenderType)
                .padding(.top, getRelativeHeight(14.0))
            }
            .frame(width: getRelativeWidth(364.0), height: getRelativeHeight(69.0),
                   alignment: .center)
            .padding(.leading, getRelativeWidth(11.0))
            .padding(.trailing, getRelativeWidth(11.0))
        }
    }
}

#Preview {
    FilterShimmerView()
}



