import SwiftUI

struct RowserviceiteCell: View {
    let selected:Bool
    let title:String
    var body: some View {
        HStack {
            Text(title.capitalized)
                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                .fontWeight(.medium)
                .padding(.horizontal, getRelativeWidth(26.0))
                .padding(.bottom, getRelativeHeight(7.0))
                .padding(.top, getRelativeHeight(9.0))
                .foregroundColor(selected ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                
                .multilineTextAlignment(.center)
                .frame(height: getRelativeHeight(37.0),
                       alignment: .center)
                .background(selected ?  ColorConstants.Cyan800.clipShape(.capsule) : ColorConstants.WhiteA700.clipShape(.capsule))
                .overlay(Capsule()
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
//            Text(StringConstants.kLblHairSalon)
//                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
//                .fontWeight(.medium)
//                .padding(.leading, getRelativeWidth(30.0))
//                .padding(.bottom, getRelativeHeight(7.0))
//                .padding(.top, getRelativeHeight(9.0))
//                .foregroundColor(ColorConstants.Cyan8007f)
//                .minimumScaleFactor(0.5)
//                .multilineTextAlignment(.leading)
//                .frame(width: getRelativeWidth(130.0), height: getRelativeHeight(37.0),
//                       alignment: .leading)
//                .overlay(RoundedCorners(topLeft: 18.5, topRight: 18.5, bottomLeft: 18.5,
//                                        bottomRight: 18.5)
//                        .stroke(ColorConstants.Cyan800,
//                                lineWidth: 1))
//                .background(ColorConstants.Cyan8003f)
//                .padding(.leading, getRelativeWidth(5.0))
//            Text(StringConstants.kLblBarbershop2)
//                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
//                .fontWeight(.medium)
//                .padding(.trailing, getRelativeWidth(30.0))
//                .padding(.leading, getRelativeWidth(27.0))
//                .padding(.vertical, getRelativeHeight(10.0))
//                .foregroundColor(ColorConstants.Cyan8007f)
//                .minimumScaleFactor(0.5)
//                .multilineTextAlignment(.center)
//                .frame(width: getRelativeWidth(130.0), height: getRelativeHeight(37.0),
//                       alignment: .center)
//                .overlay(RoundedCorners(topLeft: 18.5, topRight: 18.5, bottomLeft: 18.5,
//                                        bottomRight: 18.5)
//                        .stroke(ColorConstants.Cyan800,
//                                lineWidth: 1))
//                .background(ColorConstants.Cyan8003f)
//                .padding(.leading, getRelativeWidth(9.0))
        }
        
    }
}

struct RowserviceiteShimmerCell: View {
    
    var body: some View {
        HStack {
            Text("title")
                .font(FontScheme.kNunitoMedium(size: getRelativeHeight(14.0)))
                .fontWeight(.medium)
                .padding(.horizontal, getRelativeWidth(26.0))
                .padding(.bottom, getRelativeHeight(7.0))
                .padding(.top, getRelativeHeight(9.0))
                .foregroundColor(ColorConstants.Cyan8007f)
                
                .multilineTextAlignment(.center)
                .frame(height: getRelativeHeight(37.0),
                       alignment: .center)
                .background(ColorConstants.WhiteA700.clipShape(.capsule))
                .overlay(Capsule()
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
        }
        
    }
}

struct RowserviceiteCell_Previews: PreviewProvider {

 static var previews: some View {
     RowserviceiteCell(selected: false, title: "All")
 }
 }
