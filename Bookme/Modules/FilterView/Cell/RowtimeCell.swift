import SwiftUI

struct RowtimeCell: View {
    let selected:Bool
    let title:String
    var body: some View {
        HStack {
            Text(title)
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(11.0)))
                .fontWeight(.semibold)
                .padding(.horizontal, getRelativeWidth(14.0))
                .padding(.bottom, getRelativeHeight(5.0))
                .padding(.top, getRelativeHeight(7.0))
                .foregroundColor(selected ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.center)
                .frame(width: getRelativeWidth(78.0), height: getRelativeHeight(29.0),
                       alignment: .center)
                .overlay(Capsule()
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(selected ? ColorConstants.Cyan800.clipShape(.capsule) : ColorConstants.WhiteA700.clipShape(.capsule))
                
                
          
        }

    }
}

struct CircularRatingCell: View {
    let selected:Bool
    let title:Int
    var body: some View {
        HStack {
            Text("\(title)")
                .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                .fontWeight(.semibold)
                .padding(.vertical, getRelativeHeight(5.0))
                .foregroundColor(selected ? ColorConstants.WhiteA700 : ColorConstants.Cyan8007f)
                .multilineTextAlignment(.center)
                .frame(width: getRelativeWidth(40.0), height: getRelativeHeight(40.0),
                       alignment: .center)
                .overlay(Circle()
                        .stroke(ColorConstants.Cyan800,
                                lineWidth: 1))
                .background(selected ? ColorConstants.Cyan800.clipShape(.circle) : ColorConstants.WhiteA700.clipShape(.circle))
                
                
          
        }

    }
}

struct RowtimeCell_Previews: PreviewProvider {
    static var previews: some View {
        RowtimeCell(selected: false, title: "10:00 AM")
    }
}



extension String {
    // Converts 24-hour format to 12-hour format
    // Input: 22:15
    // Output: 10:15 PM
    var time12String: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        guard let date = dateFormatter.date(from: self) else {
            return "Invalid Time String"
        }
        dateFormatter.dateFormat = "hh:mm a"
        return dateFormatter.string(from: date)
    }
    
    // Converts 12-hour format to 24-hour format
    // Input: 10:15 PM
    // Output: 22:15
    var time24String: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "hh:mm a"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        guard let date = dateFormatter.date(from: self) else {
            return "Invalid Time String"
        }
        dateFormatter.dateFormat = "HH:mm"
        return dateFormatter.string(from: date)
    }
}


func unzip<K, V>(_ array: [(key: K, value: V)]) -> ([K], [V]) {
    var keys = [K]()
    var values = [V]()

    keys.reserveCapacity(array.count)
    values.reserveCapacity(array.count)

    array.forEach { key, value in
        keys.append(key)
        values.append(value)
    }

    return (keys, values)
}
