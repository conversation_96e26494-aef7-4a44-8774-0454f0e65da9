import SwiftUI

struct OnboardingView: View {
    @StateObject var viewModel = OnboardingViewModel()
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject private var appState: AppState
   
    var isFirstIndex: Bool { viewModel.currentIndex == viewModel.onBoardingList.startIndex }
    var isSecondIndex: Bool { viewModel.currentIndex == (viewModel.onBoardingList.startIndex + 1) }
    var isSecondFromLastIndex: Bool { viewModel.currentIndex == (viewModel.onBoardingList.endIndex - 1) }
    var isThirdFromLastIndex: Bool { viewModel.currentIndex == (viewModel.onBoardingList.endIndex - 2) }
    var isLastIndex: Bool { viewModel.currentIndex == (viewModel.onBoardingList.endIndex) }
    
    var isNotFirstIndex: Bool { viewModel.currentIndex > viewModel.onBoardingList.startIndex }
    var isNotLastIndex: Bool { viewModel.currentIndex < (viewModel.onBoardingList.endIndex - 1) }

    @Namespace private var animation
    
    var body: some View {
        NavigationView {
            SuperView(pageState: $viewModel.pageState) {
                OnboardingShimmerView()
                    .shimmerize()
                    .background(ColorConstants.bgGradient)
            } content: {
                ZStack(alignment: .center) {
                    VStack(alignment: .center, spacing: 0) {
                        TabView(selection: $viewModel.currentIndex.animation(.bouncy)) {
                            ForEach(Array(viewModel.onBoardingList.enumerated()), id: \.element.id) { index, item in
                                VStack {
                                    ZStack(alignment: .top) {
                                        NetworkImageView(path: item.splashImageUrl)
                                            .frame(width: getRelativeWidth(301.0),
                                                   height: getRelativeHeight(323.0), alignment: .center)
                                            .clipped()
                                            .padding(.leading, getRelativeWidth(28.0))
                                            .padding(.trailing, getRelativeWidth(20.0))
//                                        ZStack {}
//                                            .frame(width: getRelativeWidth(349.0),
//                                                   height: getRelativeHeight(2.0), alignment: .bottomLeading)
//                                            .background(RoundedCorners(topLeft: 174.5, topRight: 174.5,
//                                                                       bottomLeft: 174.5, bottomRight: 174.5)
//                                                    .fill(ColorConstants.Cyan800))
//                                            .padding(.top, getRelativeHeight(330.22))
                                    }
                                    .frame(maxWidth: .infinity)
                                    
                                    Text(item.splashTitle)
                                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(28.0)))
                                        .fontWeight(.heavy)
                                        .foregroundColor(ColorConstants.Cyan800)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .padding(.top, getRelativeHeight(16.0))
                                        .padding(.leading, getRelativeWidth(17.0))
                                        .padding(.trailing, getRelativeWidth(14.0))
                                    Text(item.splashDescription)
                                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(16.0)))
                                        .fontWeight(.regular)
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .padding(.top, getRelativeHeight(16.0))
                                        .padding(.horizontal, getRelativeWidth(17.0))
                                }
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                                .tag(index)
                            }
                        }
//                        .if(true, transform: {
//                            $0.gesture(DragGesture().onChanged { _ in })  // Disable swipe gesture
//                        })
                        
                        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                        .disableScrolling(disabled: isSecondFromLastIndex)
                        .multilineTextAlignment(.center)
                        
                        HStack(spacing: 0) {
                            Button(action: {
                                viewModel.updateCurrentIndex(.decrement)
                            }, label: {
                                Image("img_arrowright")
                                    .resizable()
                                    .colorMultiply(.white)
                                    .padding()
                                    .frame(width: getRelativeWidth(45.0), height: getRelativeHeight(45.0),
                                           alignment: .center)
                                    .clipShape(Circle())
                                    .rotateBasedOnLanguage(inverse: true)
                                
                            })
                            .disabled(!isNotFirstIndex)
                            .frame(width: getRelativeWidth(45.0), height: getRelativeWidth(45.0),
                                   alignment: .center)
                            .background(Circle()
                                .fill(ColorConstants.Cyan8007f))
                            .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 2)
                            .visibility(isNotFirstIndex ? .visible : .invisible)
                            .visibility(viewModel.currentIndex < viewModel.onBoardingList.endIndex - 1 ? .visible : .gone)
                                    
                            Button(action: { viewModel.updateCurrentIndex(.increment) }, label: {
                                HStack(spacing: 0) {
                                    Text("Let’s Get Started")
                                        .font(FontScheme
                                            .kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                        .fontWeight(.heavy)
                                        .padding(.horizontal, getRelativeWidth(30.0))
                                        .padding(.vertical, getRelativeHeight(12.0))
                                        .foregroundColor(ColorConstants.WhiteA700)
                                        .minimumScaleFactor(0.5)
                                        .multilineTextAlignment(.center)
                                        .frame(width: getRelativeWidth(240.0),
                                               height: getRelativeHeight(45.0), alignment: .center)
                                        .background(Capsule()
                                            .fill(ColorConstants.Cyan800))
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0,
                                                y: 1)
                                           
                                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                }
                            })
//                            .visibility(isSecondFromLastIndex ? .visible : .gone)
                            .visibility(isThirdFromLastIndex ? .visible : .gone)
                            .matchedGeometryEffect(id: "trailing.button", in: animation)
                            .padding(.leading, getRelativeWidth(32.0))
                               
                            PageIndicator(numPages: viewModel.onBoardingList.count, currentPage: $viewModel.currentIndex,
                                          selectedColor: ColorConstants.Cyan800,
                                          unSelectedColor: ColorConstants.Cyan8003f, spacing: 6.0)
                                .transition(.backslide)
                                .frame(maxWidth: .infinity)
                                .visibility(viewModel.currentIndex < viewModel.onBoardingList.endIndex - 2 ? .visible : .gone)
                            
                            FabButton(action: {
                                viewModel.updateCurrentIndex(.increment)
                            }, backgroundColor: ColorConstants.Cyan800, image: "img_arrowright",
                            frameInfo: (Double(getRelativeWidth(45.0)),
                                        Double(getRelativeHeight(45.0))))
                                    
                                .frame(width: getRelativeWidth(45.0), height: getRelativeWidth(45.0))
                                .background(Circle()
                                    .fill(ColorConstants.Cyan800))
                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                .matchedGeometryEffect(id: "trailing.button", in: animation)
                                .visibility(viewModel.currentIndex < viewModel.onBoardingList.endIndex - 2 ? .visible : .gone)
                            
                            VStack(spacing: getRelativeHeight(10)) {
                                
                                
                                ForEach(ServiceType.allCases) { type in
                                    Button(action: {
                                        updateSelection(type)
                                    }, label: {
                                        HStack(spacing: 0) {
                                            Text(LocalizedStringKey(type.title))
                                                .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(14.0)))
                                                .fontWeight(.heavy)
                                                .padding(.horizontal, getRelativeWidth(30.0))
                                                .padding(.vertical, getRelativeHeight(15.0))
                                                .foregroundColor(ColorConstants.WhiteA700)
                                                .minimumScaleFactor(0.5)
                                                .multilineTextAlignment(.center)
                                                .frame(width: getRelativeWidth(310.0), height: getRelativeHeight(50.0),
                                                       alignment: .center)
                                                .background(Capsule()
                                                    .fill(ColorConstants.Cyan800))
                                                .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                                                
                                                .padding(.leading, getRelativeWidth(21.0))
                                                .padding(.trailing, getRelativeWidth(17.0))
                                        }
                                    })
                                }
                            }
                            .padding(.bottom)
                            
                            .frame(maxWidth: .infinity)
                            .visibility(isSecondFromLastIndex ? .visible : .gone)
                            .transition(.scale.combined(with: .opacity))
                            .clipped()
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, getRelativeWidth(26.0))
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(ColorConstants.bgGradient)
                    
                    Group {
                        NavigationLink(destination: SelectionView(),
                                       tag: "SelectionView",
                                       selection: $viewModel.nextScreen,
                                       label: {
                                           EmptyView()
                                       })
                    }
                }
            }
        }
    }
    
    func updateSelection(_ type: ServiceType) {
        appState.updateServiceType([type])
        appState.updateInitialScreen(.dashboard)
    }
}

struct WelcomeOneView_Previews: PreviewProvider {
    static var previews: some View {
        OnboardingView()
    }
}

struct OnboardingShimmerView: View {
    var body: some View {
        ZStack {
            VStack(alignment: .leading, spacing: 0) {
                VStack {
                    ZStack(alignment: .top) {
                        NetworkImageView(path: nil)
                                  
                            .frame(width: getRelativeWidth(301.0),
                                   height: getRelativeHeight(343.0), alignment: .center)
                            .clipped()
                            .padding(.leading, getRelativeWidth(28.0))
                            .padding(.trailing, getRelativeWidth(20.0))
                        ZStack {}
                            .frame(width: getRelativeWidth(349.0),
                                   height: getRelativeHeight(2.0), alignment: .bottomLeading)
                            .background(RoundedCorners(topLeft: 174.5, topRight: 174.5,
                                                       bottomLeft: 174.5, bottomRight: 174.5)
                                    .fill(ColorConstants.Cyan800))
                            .padding(.top, getRelativeHeight(330.22))
                    }
                    .frame(maxWidth: .infinity)
                            
                    Text(String(repeating: "item.splashTitle", count: 2))
                        .font(FontScheme.kNunitoExtraBold(size: getRelativeHeight(28.0)))
                        .fontWeight(.heavy)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(317.0), height: getRelativeHeight(76.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(45.0))
                        .padding(.leading, getRelativeWidth(17.0))
                        .padding(.trailing, getRelativeWidth(14.0))
                    Text(String(repeating: "item.splashDescription", count: 14))
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(16.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.center)
                        .frame(width: getRelativeWidth(330.0), height: getRelativeHeight(69.0),
                               alignment: .center)
                        .padding(.top, getRelativeHeight(30.0))
                        .padding(.horizontal, getRelativeWidth(17.0))
                }
                       
                HStack(spacing: 0) {
                    Button(action: {}, label: {
                        Image("img_arrowright")
                            .rotateBasedOnLanguage(inverse: true)
                           
                    })
                   
                    .frame(width: getRelativeWidth(45.0), height: getRelativeWidth(45.0),
                           alignment: .center)
                    .background(RoundedCorners(topLeft: 22.5, topRight: 22.5,
                                               bottomLeft: 22.5, bottomRight: 22.5)
                            .fill(ColorConstants.Cyan8007f))
                    .shadow(color: ColorConstants.Black9003f, radius: 4, x: 0, y: 2)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .visibility(.invisible)
                    
                    PageIndicator(numPages: 4, currentPage: .constant(0),
                                  selectedColor: ColorConstants.Cyan800,
                                  unSelectedColor: ColorConstants.Cyan8003f, spacing: 6.0)
                        .transition(.backslide)
                        .frame(maxWidth: .infinity)
                      
                    FabButton(action: {}, backgroundColor: ColorConstants.Cyan800, image: "img_arrowright",
                              frameInfo: (Double(getRelativeWidth(45.0)),
                                          Double(getRelativeHeight(45.0))))
                            
                        .frame(width: getRelativeWidth(45.0), height: getRelativeWidth(45.0))
                        .background(RoundedCorners(topLeft: 22.5, topRight: 22.5, bottomLeft: 22.5,
                                                   bottomRight: 22.5)
                                .fill(ColorConstants.Cyan800))
                        .shadow(color: ColorConstants.Black90044, radius: 2.5, x: 0, y: 1)
                        .frame(maxWidth: .infinity, alignment: .trailing)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.top, getRelativeHeight(164.0))
                .padding(.horizontal, getRelativeWidth(26.0))
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(ColorConstants.bgGradient)
        }
    }
}

struct DisableScrolling: ViewModifier {
    var disabled: Bool
    
    func body(content: Content) -> some View {
        if disabled {
            content
                .simultaneousGesture(DragGesture(minimumDistance: 0))
        } else {
            content
        }
    }
}

extension View {
    func disableScrolling(disabled: Bool) -> some View {
        modifier(DisableScrolling(disabled: disabled))
    }
}
