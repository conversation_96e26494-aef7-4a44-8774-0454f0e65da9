import Foundation
import SwiftUI

class OnboardingViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    @Published var currentIndex: Int = 0
    @Published var onBoardingList: [OnboardingModel] = []

    
    override init() {
        super.init()
       
        getOnboardingList()
    }
    
    func updateCurrentIndex(_ value: PageIndexUpdateType) { withAnimation(.bouncy) {
        withAnimation(.bouncy) {
            switch value {
            case .increment:
                self.currentIndex += 1
            case .decrement:
                self.currentIndex -= 1
            }
        }

    } }
    
    
    func getOnboardingList() -> Void {
        onApiCall(api.splashScreens, parameters: emptyDictionary) {
            self.onBoardingList = $0.data ?? []
        }
    }
}

enum PageIndexUpdateType { case increment, decrement }
