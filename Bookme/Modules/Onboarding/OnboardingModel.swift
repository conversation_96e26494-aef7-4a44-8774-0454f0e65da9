//
//  OnboardingModel.swift
//  Bookme
//
//  Created by Apple on 31/01/2024.
//

import Foundation

// MARK: - OnboardingModel

struct OnboardingModel: Codable, Identifiable, Equatable {
    let id: UUID = .init()
    let splashID: Int
    let splashImage, splashTitle, splashDescription: String
    let enabled: Int
    let date1: String
    let order1: Int

    var splashImageUrl: String {
        AppConstants.Server.baseURL + splashImage
    }

    enum CodingKeys: String, CodingKey {
        case splashID = "splash_id"
        case splashImage = "splash_image"
        case splashTitle = "splash_title"
        case splashDescription = "splash_description"
        case enabled, date1, order1
    }
}
