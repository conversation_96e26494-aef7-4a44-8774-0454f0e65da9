import SwiftUI

struct Transactionare1Cell: View {
    var body: some View {
        VStack(alignment:.leading) {
            Text(StringConstants.kMsg10December202)
                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.WhiteA700)
                .minimumScaleFactor(0.5)
                .multilineTextAlignment(.leading)
                .frame(width: getRelativeWidth(136.0), height: getRelativeHeight(22.0),
                       alignment: .leading)
                .padding(.leading, getRelativeWidth(7.0))
                .padding(.trailing, getRelativeWidth(10.0))
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text(StringConstants.kLblHairColor)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(61.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                    Spacer()
                    Text(StringConstants.kLblKd0000)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(57.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(299.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(17.0))
                .padding(.horizontal, getRelativeWidth(23.0))
                Text(StringConstants.kMsg10December)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(117.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.vertical, getRelativeHeight(8.0))
                    .padding(.horizontal, getRelativeWidth(23.0))
            }
            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(83.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                    bottomRight: 25.0)
                    .stroke(ColorConstants.Cyan800,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                       bottomRight: 25.0)
                    .fill(ColorConstants.Cyan8003f))
            .padding(.top, getRelativeHeight(12.0))
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text(StringConstants.kLblShaving2)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(49.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                    Spacer()
                    Text(StringConstants.kLblKd0000)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(57.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(299.0), height: getRelativeHeight(21.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(17.0))
                .padding(.horizontal, getRelativeWidth(23.0))
                Text(StringConstants.kMsg10December)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(117.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.vertical, getRelativeHeight(6.0))
                    .padding(.horizontal, getRelativeWidth(23.0))
            }
            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(83.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                    bottomRight: 25.0)
                    .stroke(ColorConstants.Cyan800,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                       bottomRight: 25.0)
                    .fill(ColorConstants.Cyan8003f))
            .padding(.top, getRelativeHeight(10.0))
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text(StringConstants.kLblSkincare2)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(52.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                    Spacer()
                    Text(StringConstants.kLblKd0000)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(57.0), height: getRelativeHeight(20.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(299.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(17.0))
                .padding(.horizontal, getRelativeWidth(23.0))
                Text(StringConstants.kMsg10December)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800B2)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(117.0), height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.vertical, getRelativeHeight(8.0))
                    .padding(.horizontal, getRelativeWidth(23.0))
            }
            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(83.0),
                   alignment: .leading)
            .overlay(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                    bottomRight: 25.0)
                    .stroke(ColorConstants.Cyan800,
                            lineWidth: 1))
            .background(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                       bottomRight: 25.0)
                    .fill(ColorConstants.Cyan8003f))
            .padding(.top, getRelativeHeight(10.0))
        }
        .frame(width: getRelativeWidth(351.0), alignment: .leading)
        .background(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                   bottomRight: 25.0).fill(.clear))
    }
}

struct Transactionare1Cell_Previews: PreviewProvider {
    static var previews: some View {
        Transactionare1Cell()
            .background(ColorConstants.bgGradient)
    }
}
