import SwiftUI

struct TransactionareCell: View {
    let model: TransactionHistoryModel
    var body: some View {
        VStack(alignment: .leading) {
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text(model.serviceName)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                        .frame(height: getRelativeHeight(20.0),
                               alignment: .leading)
                    Spacer()
                    CurrencyText(model.price)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.leading)
                        .frame(height: getRelativeHeight(20.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(299.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(17.0))
                .padding(.horizontal, getRelativeWidth(23.0))
                Text(model.createdAt)
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800B2)
                    .multilineTextAlignment(.leading)
                    .frame(height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.vertical, getRelativeHeight(8.0))
                    .padding(.horizontal, getRelativeWidth(23.0))
            }
            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(83.0),
                   alignment: .leading)
            .clipShape(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                      bottomRight: 25.0))
            .overlay(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                    bottomRight: 25.0)
                    .stroke(ColorConstants.Cyan800,
                            lineWidth: 1))
        }
    }
}

struct TransactionareShimmerCell: View {
  
    var body: some View {
        VStack(alignment: .leading) {
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text("model.serviceName")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.Cyan800)
                        .multilineTextAlignment(.leading)
                        .frame(height: getRelativeHeight(20.0),
                               alignment: .leading)
                    Spacer()
                    CurrencyText("0.00")
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(14.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .multilineTextAlignment(.leading)
                        .frame(height: getRelativeHeight(20.0),
                               alignment: .leading)
                }
                .frame(width: getRelativeWidth(299.0), height: getRelativeHeight(20.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(17.0))
                .padding(.horizontal, getRelativeWidth(23.0))
                Text("model.createdAt")
                    .font(FontScheme.kNunitoBold(size: getRelativeHeight(11.0)))
                    .fontWeight(.bold)
                    .foregroundColor(ColorConstants.Cyan800B2)
                    .multilineTextAlignment(.leading)
                    .frame(height: getRelativeHeight(16.0),
                           alignment: .leading)
                    .padding(.vertical, getRelativeHeight(8.0))
                    .padding(.horizontal, getRelativeWidth(23.0))
            }
            .frame(width: getRelativeWidth(351.0), height: getRelativeHeight(83.0),
                   alignment: .leading)
            .clipShape(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                      bottomRight: 25.0))
            .overlay(RoundedCorners(topLeft: 25.0, topRight: 25.0, bottomLeft: 25.0,
                                    bottomRight: 25.0)
                    .stroke(ColorConstants.Cyan800,
                            lineWidth: 1))
        }
    }
}

// struct TransactionareCell_Previews: PreviewProvider {
//    static var previews: some View {
//        TransactionareCell()
//            .frame(maxWidth: .infinity, maxHeight: .infinity)
//            .background(ColorConstants.bgGradient)
//    }
// }
