import Foundation
import SwiftUI

class TransactionViewModel: SuperViewModel {
    @Published var nextScreen: String? = nil
    
    @Published var transactionHistoryList:[TransactionHistoryModel] = []
    @Published var titleDateList:[String] = []
    
    
    func getSectionedList(_ value:String)-> [TransactionHistoryModel] {
        var array:[TransactionHistoryModel] = []
        
        array = transactionHistoryList.filter({ $0.bookdate == value })
        
        return array
    }
    
    override init() {
        super.init()
        getTransactionData()
    }
    
    func getTransactionData() {
        
        
        
        guard let userID: Int = AppState.userModel?.user.id else { return }
        let parameters: [String: String] = ["user_id":"\(userID)"]
        onApiCall(api.transactionHistory, parameters: parameters) {
            self.transactionHistoryList = $0.data ?? []
            let array = self.transactionHistoryList.map({ $0.bookdate })
            let orderedSetArray = NSOrderedSet(array: array)
            let normalArray = orderedSetArray.array as! [String]
            self.titleDateList = normalArray
            let filteredData = self.getSectionedList(self.titleDateList.last ?? "")
            print(filteredData)
            
        }
    }
}
