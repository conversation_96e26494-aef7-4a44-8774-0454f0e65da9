//
//  TransactionHistoryModel.swift
//  Bookme
//
//  Created by Apple on 07/05/2024.
//

import Foundation


// MARK: - TransactionHistoryModel
struct TransactionHistoryModel: Codable, Identifiable {
    let id: UUID = .init()
    let serviceName, price, createdAt, bookdate: String

    enum CodingKeys: String, CodingKey {
        case serviceName = "service_name"
        case price
        case createdAt = "created_at"
        case bookdate
    }
}


