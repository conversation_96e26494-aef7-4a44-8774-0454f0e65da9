import SwiftUI

struct TransactionView: View {
    @StateObject var viewModel = TransactionViewModel()
    @Environment(\.dismiss) var dismiss: DismissAction
    var body: some View {
        
        SuperView(pageState: $viewModel.pageState) {
            
        } content: {
            MainScrollBody(invertColor: false, backButtonWithTitle: "Transactions") {
                
                if viewModel.pageState == .loading(true){
                    TransactionShimmerView()
                        .shimmerize()
                }else{
                    VStack(alignment: .leading, spacing: 0) {
                        
                        
                        if !viewModel.titleDateList.isEmpty {
                            
                            
                            CustomPlaceholder(placeholderType: .noData,title: "No transaction history", subTitle: "", image: .noTransactions, size: .init(width: 78.relativeFontSize, height: 78.relativeFontSize), titleColor: .white)
                                .padding(.top, 32.relativeHeight)
                            
                        }else{
                            VStack(spacing: 0) {
                                LazyVStack(spacing: 20.0.relativeHeight) {
                                    ForEach(viewModel.titleDateList, id: \.self) { title in
                                               
                                        Section {
                                            VStack(spacing: 10.0.relativeHeight) {
                                                ForEach(viewModel.getSectionedList(title)) { model in
                                                    TransactionareCell(model: model)
                                                }
                                            }
                                                    
                                        } header: {
                                            Text(title)
                                                .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                                                .fontWeight(.bold)
                                                .foregroundColor(ColorConstants.WhiteA700)
                                                .multilineTextAlignment(.leading)
                                               
                                                .padding(.leading, getRelativeWidth(7.0))
                                                .padding(.trailing, getRelativeWidth(10.0))
                                                .frame(maxWidth: /*@START_MENU_TOKEN@*/ .infinity/*@END_MENU_TOKEN@*/, alignment: .leading)
                                            
                                        }
                                    }
                                }
                            }
                               
                            .padding(.top, getRelativeHeight(17.0))
                            .padding(.horizontal, getRelativeHeight(16.0))
                        }
                        
                       
                           
                        
                    }
                    .padding(.top, getRelativeHeight(16.0))
                }
                
                
            }
            .background(ColorConstants.bgGradient)
        }

        
      
    }
}


struct TransactionShimmerView: View {
  
    var body: some View {
        
        MainScrollBody(invertColor: false, backButtonWithTitle:"Transactions") {
                VStack(alignment: .leading, spacing: 0) {
                    VStack(alignment: .leading, spacing: 0) {
                        VStack(spacing: 0) {
                            LazyVStack(spacing: 20.0.relativeHeight) {
                                ForEach(0...16, id: \.self) { index in
                                           
                                    Section {
                                        VStack(spacing: 10.0.relativeHeight) {
                                            ForEach(0...2, id: \.self) { index in
                                                TransactionareShimmerCell()
                                            }
                                        }
                                                
                                    } header: {
                                        Text("titlitledds")
                                            .font(FontScheme.kNunitoBold(size: getRelativeHeight(16.0)))
                                            .fontWeight(.bold)
                                            .foregroundColor(ColorConstants.WhiteA700)
                                            .multilineTextAlignment(.leading)
                                           
                                            .padding(.leading, getRelativeWidth(7.0))
                                            .padding(.trailing, getRelativeWidth(10.0))
                                            .frame(maxWidth: /*@START_MENU_TOKEN@*/ .infinity/*@END_MENU_TOKEN@*/, alignment: .leading)
                                        
                                    }
                                }
                            }
                        }
                           
                        .padding(.top, getRelativeHeight(17.0))
                        .padding(.horizontal, getRelativeHeight(16.0))
                    }
                }
                .padding(.top, getRelativeHeight(16.0))
            }
            .background(ColorConstants.bgGradient)
        
    }
}

struct TransactionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationStack {
            TransactionView().attachAllEnvironmentObjects()
        }
    }
}
