import SwiftUI

struct Rowrectangleten3Cell: View {
    var body: some View {
        HStack {
            ZStack(alignment: .bottomTrailing) {
                Image("img_rectangle10")
                    .resizable()
                    .frame(width: getRelativeWidth(147.0), height: getRelativeWidth(149.0),
                           alignment: .leading)
                    .scaledToFit()
                    .clipShape(Circle())
                    .clipShape(Circle())
                VStack {
                    Image("img_vector_yellow_a700")
                        .resizable()
                        .frame(width: getRelativeWidth(7.0), height: getRelativeWidth(9.0),
                               alignment: .leading)
                        .scaledToFit()
                        .padding(.top, getRelativeHeight(5.0))
                        .padding(.horizontal, getRelativeWidth(13.0))
                    Text(StringConstants.kLbl43)
                        .font(FontScheme.kNunitoBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.bold)
                        .foregroundColor(ColorConstants.WhiteA700)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(16.0), height: getRelativeWidth(18.0),
                               alignment: .leading)
                        .padding(.horizontal, getRelativeWidth(9.0))
                }
                .frame(width: getRelativeWidth(33.0), height: getRelativeWidth(35.0),
                       alignment: .leading)
                .background(RoundedCorners(topLeft: 17.5, topRight: 17.5, bottomLeft: 17.5,
                                           bottomRight: 17.5)
                        .fill(ColorConstants.Cyan800))
                .padding(.top, getRelativeHeight(109.0))
                .padding(.leading, getRelativeWidth(113.0))
            }
            .hideNavigationBar()
            .frame(width: getRelativeWidth(147.0), height: getRelativeWidth(149.0),
                   alignment: .leading)
            VStack(alignment: .leading, spacing: 0) {
                Text(StringConstants.kMsgBroadwayBarber)
                    .font(FontScheme.kNunitoMedium(size: getRelativeHeight(15.0)))
                    .fontWeight(.medium)
                    .foregroundColor(ColorConstants.Cyan800)
                    .minimumScaleFactor(0.5)
                    .multilineTextAlignment(.leading)
                    .frame(width: getRelativeWidth(125.0), height: getRelativeHeight(21.0),
                           alignment: .leading)
                    .padding(.trailing)
                ZStack(alignment: .bottomLeading) {
                    Text(StringConstants.kMsg4thFloorAlZ)
                        .font(FontScheme.kNunitoRegular(size: getRelativeHeight(12.0)))
                        .fontWeight(.regular)
                        .foregroundColor(ColorConstants.Gray900B2)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(182.0), height: getRelativeHeight(55.0),
                               alignment: .leading)
                        .padding(.bottom, getRelativeHeight(14.0))
                    Text(StringConstants.kLbl6Km)
                        .font(FontScheme.kNunitoSemiBold(size: getRelativeHeight(12.0)))
                        .fontWeight(.semibold)
                        .foregroundColor(ColorConstants.Black900B2)
                        .minimumScaleFactor(0.5)
                        .multilineTextAlignment(.leading)
                        .frame(width: getRelativeWidth(27.0), height: getRelativeHeight(14.0),
                               alignment: .leading)
                        .padding(.top, getRelativeHeight(55.0))
                        .padding(.trailing, getRelativeWidth(137.5))
                    Image("img_map")
                        .resizable()
                        .frame(width: getRelativeWidth(7.0), height: getRelativeHeight(11.0),
                               alignment: .leading)
                        .scaledToFit()
                        .padding(.top, getRelativeHeight(53.39))
                        .padding(.trailing, getRelativeWidth(175.0))
                }
                .hideNavigationBar()
                .frame(width: getRelativeWidth(182.0), height: getRelativeHeight(69.0),
                       alignment: .leading)
                .padding(.top, getRelativeHeight(37.0))
            }
            .frame(width: getRelativeWidth(183.0), height: getRelativeHeight(127.0),
                   alignment: .leading)
            .padding(.leading, getRelativeWidth(26.0))
        }
        .frame(width: getRelativeWidth(358.0), alignment: .leading)
        .overlay(RoundedCorners().stroke(ColorConstants.Bluegray50030, lineWidth: 1))
        .background(RoundedCorners().fill(Color.clear.opacity(0.7)))
        .hideNavigationBar()
    }
}

/* struct Rowrectangleten3Cell_Previews: PreviewProvider {

 static var previews: some View {
 			Rowrectangleten3Cell()
 }
 } */
