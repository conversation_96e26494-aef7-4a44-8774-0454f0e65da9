import Foundation
import SwiftUI

class FontScheme: NSObject {
    static func kNunitoExtraBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoExtraBold, size: size)
    }

    static func kNunitoBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoBold, size: size)
    }

    static func kNunitoRegular(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoRegular, size: size)
    }

    static func kNunitoMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoMedium, size: size)
    }

    static func kNunitoSemiBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoSemiBold, size: size)
    }

    static func kMontserratMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kMontserratMedium, size: size)
    }

    static func kMontserratSemiBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kMontserratSemiBold, size: size)
    }

    static func kNeusaNextStdMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNeusaNextStdMedium, size: size)
    }

    static func fontFromConstant(fontName: String, size: CGFloat) -> Font {
        var result = Font.system(size: size)

        switch fontName {
        case "kNunitoExtraBold":
            result = self.kNunitoExtraBold(size: size)
        case "kNunitoBold":
            result = self.kNunitoBold(size: size)
        case "kNunitoRegular":
            result = self.kNunitoRegular(size: size)
        case "kNunitoMedium":
            result = self.kNunitoMedium(size: size)
        case "kNunitoSemiBold":
            result = self.kNunitoSemiBold(size: size)
        case "kMontserratMedium":
            result = self.kMontserratMedium(size: size)
        case "kMontserratSemiBold":
            result = self.kMontserratSemiBold(size: size)
        case "kNeusaNextStdMedium":
            result = self.kNeusaNextStdMedium(size: size)
        default:
            result = self.kNunitoExtraBold(size: size)
        }
        return result
    }

    enum FontConstant {
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoExtraBold: String = "Nunito-ExtraBold"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoBold: String = "Nunito-Bold"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoRegular: String = "Nunito-Regular"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoMedium: String = "Nunito-Medium"
        /**
         * Please Add this fonts Manually
         */
        static let kNunitoSemiBold: String = "Nunito-SemiBold"
        /**
         * Please Add this fonts Manually
         */
        static let kMontserratMedium: String = "Montserrat-Medium"
        /**
         * Please Add this fonts Manually
         */
        static let kMontserratSemiBold: String = "Montserrat-SemiBold"
        /**
         * Please Add this fonts Manually
         */
        static let kNeusaNextStdMedium: String = "NeusaNextStd-Medium"
    }
}
