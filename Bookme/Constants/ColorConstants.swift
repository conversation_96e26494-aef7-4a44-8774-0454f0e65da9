import SwiftUI

enum ColorConstants {
//    LinearGradient(gradient: Gradient(colors: [ColorConstants.Cyan900,ColorConstants.Black901]),startPoint: .topLeading, endPoint: .bottomTrailing)
    
    static let bgGradient: LinearGradient = .init(gradient: Gradient(colors: [ColorConstants.Black901 ,ColorConstants.Cyan900]), startPoint: .top, endPoint: .bottom)
    
    
    
    
//    static let bgGradient: LinearGradient = .init(
//        stops: [
//            Gradient.Stop(color: Color(hex: "#005D62"), location: 0.00),
//            Gradient.Stop(color: Color(hex: "#000000"), location: 1.00),
//        ],
//        startPoint: UnitPoint(x: 0.5, y: 1),
//        endPoint: UnitPoint(x: 0.5, y: 0)
//    )
    
    static let WhiteA7007f: Color = .init("WhiteA7007f")
    static let Black9007f: Color = .init("Black9007f")
    static let Cyan800B2: Color = .init("Cyan800B2")
    static let Black900B3: Color = .init("Black900B3")
    static let Black9003d: Color = .init("Black9003d")
    static let Black900B2: Color = .init("Black900B2")
    static let Cyan8007f: Color = .init("Cyan8007f")
    static let WhiteA7003f: Color = .init("WhiteA7003f")
    static let Cyan8003f: Color = .init("Cyan8003f")
    static let Black9007c: Color = .init("Black9007c")
    static let Black9003f: Color = .init("Black9003f")
    static let Cyan80086: Color = .init("Cyan80086")
    static let Bluegray50030: Color = .init("Bluegray50030")
    static let Black90044: Color = .init("Black90044")
    static let Cyan80087: Color = .init("Cyan80087")
    static let Teal90093: Color = .init("Teal90093")
    static let Black90082: Color = .init("Black90082")
    static let Gray600: Color = .init("Gray600")
    static let Gray400: Color = .init("Gray400")
    static let Gray401: Color = .init("Gray401")
    static let Cyan8004c: Color = .init("Cyan8004c")
    static let Cyan800C4: Color = .init("Cyan800C4")
    static let Gray800: Color = .init("Gray800")
    static let Gray5003f1: Color = .init("Gray5003f1")
    static let Gray200: Color = .init("Gray200")
    static let Cyan80099: Color = .init("Cyan80099")
    static let Bluegray600: Color = .init("Bluegray600")
    static let Cyan90030: Color = .init("Cyan90030")
    static let Cyan800: Color = .init("Cyan800")
    static let Cyan80019: Color = .init("Cyan80019")
    static let Black90019: Color = .init("Black90019")
    static let WhiteA700: Color = .init("WhiteA700")
    static let Black90059: Color = .init("Black90059")
    static let CyanA400: Color = .init("CyanA400")
    static let Gray5003f: Color = .init("Gray5003f")
    static let Teal900E8: Color = .init("Teal900E8")
    static let Cyan80072: Color = .init("Cyan80072")
    static let Black90066: Color = .init("Black90066")
    static let Black900: Color = .init("Black900")
    static let Black50: Color = .init("Black50")
    static let Black902: Color = .init("Black902")
    static let Black901: Color = .init("Black901")
    static let Black900Bf: Color = .init("Black900Bf")
    static let Gray700: Color = .init("Gray700")
    static let Amber600: Color = .init("Amber600")
    static let Bluegray100: Color = .init("Bluegray100")
    static let Cyan80082: Color = .init("Cyan80082")
    static let Gray300: Color = .init("Gray300")
    static let Cyan80084: Color = .init("Cyan80084")
    static let Cyan80033: Color = .init("Cyan80033")
    static let Cyan80077: Color = .init("Cyan80077")
    static let WhiteA70086: Color = .init("WhiteA70086")
    static let Cyan8007f1: Color = .init("Cyan8007f1")
    static let Cyan80035: Color = .init("Cyan80035")
    static let Gray900B2: Color = .init("Gray900B2")
    static let Bluegray102: Color = .init("Bluegray102")
    static let Bluegray101: Color = .init("Bluegray101")
    static let Cyan8003f1: Color = .init("Cyan8003f1")
    static let Cyan901: Color = .init("Cyan901")
    static let Cyan80038: Color = .init("Cyan80038")
    static let Cyan900: Color = .init("Cyan900")
    
    static let Gray500: Color = .init("Gray500")
    
    static let Amber500: Color = .init("Amber500")
   
    static let Cyan5001e: Color = .init("Cyan5001e")
    static let Gray50: Color = .init("Gray50")
    static let Cyan100: Color = .init("Cyan100")
 
    static let Gray50001: Color = .init("Gray50001")

    static let BlueGray900: Color = .init("BlueGray900")
    static let DeepOrangeA400: Color = .init("DeepOrangeA400")
    static let Indigo800: Color = .init("Indigo800")
}
