//
//  AppConstants.swift
//  Bookme

import Foundation

struct AppConstants {
    
    struct Server {
        static let baseURL: String = "https://app.appbookme.com/"
        static let apiPath:String = "api/"
    }
    static let OtpDigits:Int = 4
    static let unAuthenticateErrorString = "Unauthenticated."
    static let unAuthenticateErrorStringSecond = "Invalid credentials1"
    
    
    static let serverURL: String = "@{serverURL}"
    
    static let tabBarHeight:Double = 40
    static let tabBarHeightWithPadding:Double = tabBarHeight + 8
    static let tabBarTopPadding:Double = 35
    
    static let COUNTDOWN_TIMER_LENGTH = 60
}
