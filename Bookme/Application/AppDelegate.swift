import Firebase
import FirebaseCrashlytics
import FirebaseAnalytics
import FirebaseDynamicLinks
import FirebaseMessaging
import SwiftUI
import UserNotifications

class AppDelegate: NSObject, UIApplicationDelegate, UNUserNotificationCenterDelegate, MessagingDelegate {
    // Called when the app finishes launching
    func application(_ application: UIApplication,
                     didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool
    {
        // Firebase configuration
        FirebaseApp.configure()

        // Log app start to Crashlytics and Analytics
        Crashlytics.crashlytics().log("App started")
        Analytics.logEvent("app_start", parameters: nil)

        // Hide Tab Bar
        UITabBar.appearance().isHidden = true

        // Set up push notifications
        setupPushNotifications(application)

        // Check if app was launched from a notification while it was terminated
        if let notificationData = launchOptions?[.remoteNotification] as? [String: AnyObject] {
            // Handle the notification that launched the app
            handleNotification(userInfo: notificationData)
        }
        return true
    }

    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> <PERSON><PERSON> {
        // Handle the deep link URL
        print("Received URL: \(url)")
        // Add your deep linking logic here
        return true
    }

    // MARK: - Setup Push Notifications

    private func setupPushNotifications(_ application: UIApplication) {
        // Request notification permissions
        UNUserNotificationCenter.current().delegate = self
        let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]

        UNUserNotificationCenter.current().requestAuthorization(options: authOptions) { granted, _ in
            print("Permission granted: \(granted)")
        }

        // Register for remote notifications
        application.registerForRemoteNotifications()

        // Set the messaging delegate to handle FCM tokens
        Messaging.messaging().delegate = self
    }

    // MARK: - Handle FCM Token Refresh

    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        guard let fcmToken = fcmToken else { return }
        print("FCM Token: \(fcmToken)")
        // You can send this FCM token to your server for targeting notifications
        // sendTokenToServer(fcmToken)
        AppState.fcmToken = fcmToken
    }

    // MARK: - Remote Notification Registration Callbacks

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        // Pass the device token to Firebase Messaging
        Messaging.messaging().apnsToken = deviceToken
    }

    // MARK: - Handle Incoming Notifications (Foreground)

    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void)
    {
        // This method is called when a notification is delivered to a foreground app.
        completionHandler([.badge, .sound]) // Show alert, play sound, update badge even in foreground
    }

    // MARK: - Handle Notification Tap (Background & Terminated)

    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                didReceive response: UNNotificationResponse,
                                withCompletionHandler completionHandler: @escaping () -> Void)
    {
        // This method is called when the user taps on a notification
        let userInfo = response.notification.request.content.userInfo
        handleNotification(userInfo: userInfo) // Handle the notification payload
        completionHandler()
    }

    // MARK: - Helper Method to Handle Notification Data

    private func handleNotification(userInfo: [AnyHashable: Any]) {
        print("Notification data received: \(userInfo)")

        // Perform actions based on the notification payload.
        // For example, navigate to a specific screen based on notification data.
        // You can use userInfo to extract information from the notification.

        // Example: If your notification contains a screen identifier, you can route to that screen.
        NotificationCenter.default.post(name: .notificationTapped, object: nil, userInfo: userInfo)
    }

    private func handleDynamicLink(_ dynamicLink: DynamicLink?) {
        guard let url = dynamicLink?.url else { return }
        print("Dynamic Link URL: \(url)")
        // Handle navigation or app state updates here
    }
}

extension Notification.Name {
    static let notificationTapped = Notification.Name("NotificationTapped")
    static let didReceiveAPIError = Notification.Name("didReceiveAPIError")
}
