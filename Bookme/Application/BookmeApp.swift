//
//  BookmeApp.swift
//  Bookme

import CoreLocation
import FirebaseCore
import FirebaseDynamicLinks
import SwiftUI

@main
struct BookmeApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var deepLinkManager = DeepLinkManager()
    
    @State private var showAlert = false
       @State private var errorMessage = ""

    var body: some Scene {
        WindowGroup {
            MainView()
                .attachAllEnvironmentObjects()
                .environmentObject(deepLinkManager)
                .scalableFont()
                .onLoad {
//                    if isDebugMode() {
//                      AppState.userLocation = CLLocationCoordinate2D(latitude:29.2817803, longitude: 47.9912226)
//                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: .didReceiveAPIError)) { notification in
                    if let error = notification.object as? String {
                        errorMessage = error
                        showAlert = true
                    }
                }
                .alert("Server Error", isPresented: $showAlert) {
                    But<PERSON>("OK", role: .cancel) {}
                } message: {
                    Text(errorMessage)
                }
                .onOpenURL(perform: deepLinkManager.handleIncomingUniversalLink)
        }
    }

    func isDebugMode() -> Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }
}
