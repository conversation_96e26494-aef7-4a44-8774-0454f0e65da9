{"sourceLanguage": "en", "strings": {"CFBundleName": {"comment": "Bundle name", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "BookMe"}}}}, "NSCalendarsUsageDescription": {"comment": "Privacy - Calendars Usage Description", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "This app requires access to your calendar to schedule appointments."}}}}, "NSCameraUsageDescription": {"comment": "Privacy - Camera Usage Description", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "This app need to access your Camera"}}}}, "NSLocationAlwaysAndWhenInUseUsageDescription": {"comment": "Privacy - Location Always and When In Use Usage Description", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "We will need to make use of your location to present relevant information about offers around you."}}}}, "NSLocationAlwaysUsageDescription": {"comment": "Privacy - Location Always Usage Description", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "We will need to make use of your location to present relevant information about offers around you."}}}}, "NSLocationWhenInUseUsageDescription": {"comment": "Privacy - Location When In Use Usage Description", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "We will need to make use of your location to present relevant information about offers around you."}}}}, "NSPhotoLibraryUsageDescription": {"comment": "Privacy - Photo Library Usage Description", "extractionState": "extracted_with_value", "localizations": {"en": {"stringUnit": {"state": "new", "value": "This app need to access your Photo Library"}}}}}, "version": "1.0"}