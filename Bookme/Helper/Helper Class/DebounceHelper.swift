//
//  DebounceHelper.swift
//  Bookme
//
//  Created by Apple on 23/10/2024.
//

import Foundation

class DebounceHelper {
    private var workItem: DispatchWorkItem?

    func debounce(delay: TimeInterval, action: @escaping () -> Void) {
        // Cancel any previous pending work items
        workItem?.cancel()

        // Create a new DispatchWorkItem with the action
        workItem = DispatchWorkItem { action() }

        // Execute the action after the specified delay
        if let workItem = workItem {
            DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
        }
    }
}
