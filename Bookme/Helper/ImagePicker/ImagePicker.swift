//
//  ImagePicker.swift
//  Bookme
//
//  Created by Apple on 28/04/2024.
//

import AVFoundation
import SwiftUI
import TOCropViewController

struct ImagePicker: UIViewControllerRepresentable {
    @Environment(\.presentationMode) private var presentationMode
    var sourceType: UIImagePickerController.SourceType = .camera
    @Binding var selectedImage: UIImage?
    @Binding var isShowingCropper: Bool

    func makeUIViewController(context: UIViewControllerRepresentableContext<ImagePicker>) -> UIImagePickerController {
        let imagePicker = UIImagePickerController()
        imagePicker.sourceType = sourceType
        imagePicker.allowsEditing = false
        imagePicker.delegate = context.coordinator
        imagePicker.modalPresentationStyle = .fullScreen // Ensures the picker is full screen
        return imagePicker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: UIViewControllerRepresentableContext<ImagePicker>) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    final class Coordinator: NSO<PERSON>, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        var parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
                parent.isShowingCropper = true
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

public func showAlert(title: String, message: String, onOk: (() -> Void)? = nil) {
    let alertVC = UIAlertController(title: title, message: message, preferredStyle: .alert)
    alertVC.addAction(UIAlertAction(title: "Ok", style: .default) { _ in onOk?() })

    if onOk != nil {
        alertVC.addAction(UIAlertAction(title: "Cancel", style: .cancel))
    }

    if let rootVC = UIApplication.shared.activeRootViewController() {
        rootVC.present(alertVC, animated: true, completion: nil)
    }
}

func handleCameraAccess(sourceType: UIImagePickerController.SourceType, onSuccess: @escaping () -> Void) {
    checkCameraAuthorization { status in
        switch status {
        case .justDenied, .alreadyDenied:
            showAlert(title: "Camera Access Required", message: "Enable camera access in Settings.", onOk: {
                if let url = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(url)
                }
            })
        case .restricted:
            showAlert(title: "Access Restricted", message: "Camera access is restricted on this device.")
        case .alreadyAuthorized, .justAuthorized:
            if UIImagePickerController.isSourceTypeAvailable(.camera) || sourceType != .camera {
                onSuccess()
            } else {
                showAlert(title: "Camera Unavailable", message: "The camera is not available on this device.")
            }
        default:
            break
        }
    }
}

func checkCameraAuthorization(completion: @escaping (AVCaptureDevice.AuthorizationStatus) -> Void) {
    AVCaptureDevice.requestAuthorization(for: .video, completion: completion)
}

extension AVCaptureDevice {
    enum AuthorizationStatus {
        case justDenied, alreadyDenied, restricted, justAuthorized, alreadyAuthorized, unknown
    }

    static func requestAuthorization(for mediaType: AVMediaType, completion: @escaping (AuthorizationStatus) -> Void) {
        switch authorizationStatus(for: mediaType) {
        case .authorized:
            completion(.alreadyAuthorized)
        case .denied:
            completion(.alreadyDenied)
        case .restricted:
            completion(.restricted)
        case .notDetermined:
            requestAccess(for: mediaType) { granted in
                DispatchQueue.main.async {
                    completion(granted ? .justAuthorized : .justDenied)
                }
            }
        @unknown default:
            completion(.unknown)
        }
    }
}

func checkForCameraExceptions(sourceType: UIImagePickerController.SourceType, onSuccess: @escaping () -> Void) {
    checkCameraAuthorization { status in
        switch status {
        case .justDenied, .alreadyDenied:
            showAlert(
                title: "Unable to access the Camera",
                message: "To enable access, go to Settings > Privacy > Camera and turn on Camera access for this app.",
                onOk: {
                    if let bundleIdentifier = Bundle.main.bundleIdentifier,
                       let url = URL(string: "\(UIApplication.openSettingsURLString)&path=CAMERA/\(bundleIdentifier)")
                    {
                        UIApplication.shared.open(url, options: [:], completionHandler: nil)
                    }
                }
            )

        case .restricted:
            showAlert(
                title: "Camera Access Restricted",
                message: "Your device's camera access is restricted and cannot be accessed by this app."
            )

        case .justAuthorized, .alreadyAuthorized:
            if !UIImagePickerController.isSourceTypeAvailable(.camera), sourceType == .camera {
                showAlert(
                    title: "Unable to access the Camera",
                    message: "Your device camera is not working at this moment."
                )
            } else {
                onSuccess()
            }

        case .unknown:
            showAlert(
                title: "Camera Access Unknown",
                message: "Unable to determine camera access status."
            )
        }
    }
}

struct ImageCropper: UIViewControllerRepresentable {
    var image: UIImage
    @Binding var croppedImage: UIImage?
    var onDidFinishCancelled: ((Bool) -> Void)?
    @Environment(\.presentationMode) private var presentationMode

    func makeUIViewController(context: Context) -> TOCropViewController {
        let cropViewController = TOCropViewController(croppingStyle: .circular, image: image)
        cropViewController.delegate = context.coordinator
        cropViewController.aspectRatioPreset = .presetSquare
        cropViewController.aspectRatioLockEnabled = true
        cropViewController.resetAspectRatioEnabled = false
        cropViewController.onDidFinishCancelled = onDidFinishCancelled

        // Set the crop shape to circle
//        cropViewController.cropView.cropShapeType = .circle
        cropViewController.cropView.clipsToBounds = true

        return cropViewController
    }

    func updateUIViewController(_ uiViewController: TOCropViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, TOCropViewControllerDelegate {
        var parent: ImageCropper

        init(_ parent: ImageCropper) {
            self.parent = parent
        }

        func cropViewController(_ cropViewController: TOCropViewController, didCropTo image: UIImage, with cropRect: CGRect, angle: Int) {
            parent.croppedImage = image
            parent.presentationMode.wrappedValue.dismiss() // Dismiss crop view after cropping
        }

        func cropViewControllerDidCancel(_ cropViewController: TOCropViewController) {
            parent.presentationMode.wrappedValue.dismiss() // Dismiss if cancel is pressed
        }
    }
}
