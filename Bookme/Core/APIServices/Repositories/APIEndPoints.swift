//
//  APIEndPoints.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//
import Foundation

// Api Endpoints
extension String {
    // HTTP method: GET
    static let splashScreens: String = "splashscreens"
    static let servicesCategory: String = "Services_Category"
    static let googleMap: String = "google_map"
    static let timeSlotFilter: String = "timeslotfilter"
    static let logout: String = "logout"
    static let promotionBanners: String = "promotionbanners"
    static let privacyPolicy: String = "privacypolicy"
    static let termsConditions: String = "termsconditions"
    static let faqs: String = "faqs"
    static let contactUs: String = "contactus"
    static let areas: String = "areas"

    // HTTP method: POST
    static let loginWithEmail: String = "login_with_email"
    static let registerUser: String = "RegisterUser"
    static let nearbySalons: String = "nearbysalons"
    static let nearbyServices: String = "services"
    static let vendorExplore: String = "vendorexplore"
    static let popularArtists: String = "popularartists"
    static let searchVendor: String = "searchvendor"
    static let vendorDetail: String = "vendordetail"
    static let staffSelection: String = "staffSelection"
    static let bookings: String = "bookings"
    
    static let upcomingBookings: String = "upcomingbookings"
    static let completedBookings: String = "completedbookings"
    static let cancelledBookings: String = "cancelledbookings"
    static let cancelBooking: String = "cancellbooking"
    static let modifyBooking: String = "modifybooking"
    
    static let downloadPDF: String = "downloadPDF"
    
    static let updateUserProfile: String = "updateuserprofile"
    static let viewUserProfile: String = "viewuserprofile"
    static let filters: String = "filters"
    static let mapSalons: String = "mapsalons"
   
    static let addAddress: String = "addAddress"
    static let editAddress: String = "editAddress"
    static let viewAddresses: String = "viewAddresses"
    static let defaultAddress: String = "MakeDefaultAddress"
    static let deleteAddress: String = "deleteAddress"
    
    static let addWishlist: String = "addWishlist"
    static let viewWishlists: String = "viewWishlists"
    static let transactionHistory: String = "transactionhistory"
    static let vendorTimeSlot: String = "vendortimeslot"
    static let addSalonRating: String = "add_salon_rating"
    static let bookingDetails: String = "bookingdetails"
    
    static let forgotPassword: String = "forgotPassword"
    static let verifyOTP: String = "verifyotp"
    static let changePassword: String = "changePassword"
    static let resendOTP: String = "resendotp"
    static let socialLogin: String = "sociallogin"
    static let resendOtpRegistration: String = "resendotpregistration"
    
    static let refreshFCMToken: String = "refresh"
    static let allNotifications: String = "allnotifications"
    static let readByUser: String = "read_by_user"
    static let markAllRead: String = "mark_all_read"
    static let deleteNotification: String = "delete_notification"
    static let deleteAllNotifications: String = "delete_all_notifications"
    
    static let addToCart: String = "addToCart"
    static let viewCart: String = "ViewCart"
    static let updateCart: String = "UpdateCart"
    static let deleteItem: String = "DeleteItem"
    static let staffTimeslot: String = "stafftimeslot"
    static let staffAvailableSlots: String = "staffAvailableSlots"
    static let vendorServicesCategory: String = "VendorServicesCategory"
    static let vendorServices: String = "VendorServices"
    
    static let filterServiceCategories: String = "filterservicecategories"
    
    static let verifyOtpRegistration: String = "verifyotpregistration"
    static let notificationSettingsRead: String = "notificationsettingsRead"
    static let notificationSettingsUpdate: String = "notificationsettingsUpdate"
    
    static let deleteAccount: String = "DeleteAccount"
    
    static let couponCodeApply: String = "coupon-code-apply"
    
    static let listAllCards: String = "ListAllCards"
    static let addCard: String = "AddCard"
    
    static let addReportReviews: String = "add_report_reviews"
    static let addLikesReview: String = "add_likes_review"
    
    
    static let reminderNotificationRead: String = "RemindernotificationRead"
    static let reminderNotificationUpdate: String = "RemindernotificationUpdate"
    
    static let updateLanguage: String = "updatelanguage"
}
