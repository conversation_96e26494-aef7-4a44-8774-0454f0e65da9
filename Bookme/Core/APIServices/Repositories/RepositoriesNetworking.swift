//
//  RepositoriesNetworking.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//

import Alamofire

typealias Parameter = [String: Any]

enum RepositoriesNetworking {
    // HTTP method: GET
    case splashScreens
    case servicesCategory
    case googleMap
    case timeSlotFilter

    case promotionBanners
    case privacyPolicy
    case termsConditions
    case faqs
    case contactUs
    case areas

    // HTTP method: POST
    case logout(parameters: Parameter)
    case loginWithEmail(parameters: Parameter)
    case registerUser(parameters: Parameter)
    case nearbySalons(parameters: Parameter)
    case nearbyServices(parameters: Parameter)
    case vendorExplore(parameters: Parameter)
    case popularArtists(parameters: Parameter)
    case searchVendor(parameters: Parameter)
    case vendorDetail(parameters: Parameter)
    case staffSelection(parameters: Parameter)
    case bookings(parameters: Parameter)
    case upcomingBookings(parameters: Parameter)
    case completedBookings(parameters: Parameter)
    case cancelledBookings(parameters: Parameter)
    case cancelBooking(parameters: Parameter)
    case modifyBooking(parameters: Parameter)
    case downloadPDF(parameters: Parameter)
    case updateUserProfile(parameters: Parameter)
    case viewUserProfile(parameters: Parameter)
    case filters(parameters: Parameter)
    case mapSalons(parameters: Parameter)

    case addAddress(parameters: Parameter)
    case editAddress(parameters: Parameter)
    case viewAddresses(parameters: Parameter)
    case defaultAddress(parameters: Parameter)
    case deleteAddress(parameters: Parameter)

    case addWishlist(parameters: Parameter)
    case viewWishlists(parameters: Parameter)
    case transactionHistory(parameters: Parameter)
    case vendorTimeSlot(parameters: Parameter)
    case addSalonRating(parameters: Parameter)
    case bookingDetails(parameters: Parameter)

    case forgotPassword(parameters: Parameter)
    case resendOTP(parameters: Parameter)
    case verifyOTP(parameters: Parameter)
    case resendOtpRegistration(parameters: Parameter)
    case changePassword(parameters: Parameter)
    case socialLogin(parameters: Parameter)

    case refreshFCMToken(parameters: Parameter)
    case allNotifications(parameters: Parameter)
    case readByUser(parameters: Parameter)
    case markAllRead(parameters: Parameter)
    case deleteNotification(parameters: Parameter)
    case deleteAllNotifications(parameters: Parameter)

    case addToCart(parameters: Parameter)
    case viewCart(parameters: Parameter)
    case updateCart(parameters: Parameter)
    case deleteItem(parameters: Parameter)
    case staffTimeslot(parameters: Parameter)
    case staffAvailableSlots(parameters: Parameter)
    case vendorServicesCategory(parameters: Parameter)
    case vendorServices(parameters: Parameter)
    case filterServiceCategories(parameters: Parameter)

    case verifyOtpRegistration(parameters: Parameter)
    case notificationSettingsRead(parameters: Parameter)
    case notificationSettingsUpdate(parameters: Parameter)
    case deleteAccount(parameters: Parameter)
    case couponCodeApply(parameters: Parameter)

    case listAllCards(parameters: Parameter)
    case addCard(parameters: Parameter)
    case addReportReviews(parameters: Parameter)
    case addLikesReview(parameters: Parameter)
    
    case reminderNotificationRead(parameters: Parameter)
    case reminderNotificationUpdate(parameters: Parameter)
    case updateLanguage(parameters: Parameter)
}

extension RepositoriesNetworking: TargetType {
    var path: String {
        switch self {
        // HTTP method: GET
        case .splashScreens: .splashScreens
        case .servicesCategory: .servicesCategory
        case .googleMap: .googleMap
        case .timeSlotFilter: .timeSlotFilter

        case .promotionBanners: .promotionBanners
        case .privacyPolicy: .privacyPolicy
        case .termsConditions: .termsConditions
        case .faqs: .faqs
        case .contactUs: .contactUs
        case .areas: .areas

        // HTTP method: POST
        case .logout: .logout
        case .loginWithEmail: .loginWithEmail
        case .registerUser: .registerUser
        case .nearbySalons: .nearbySalons
        case .nearbyServices: .nearbyServices
        case .vendorExplore: .vendorExplore
        case .popularArtists: .popularArtists
        case .searchVendor: .searchVendor
        case .vendorDetail: .vendorDetail
        case .staffSelection: .staffSelection
        case .bookings: .bookings
        case .upcomingBookings: .upcomingBookings
        case .completedBookings: .completedBookings
        case .cancelledBookings: .cancelledBookings
        case .cancelBooking: .cancelBooking
        case .modifyBooking: .modifyBooking
        case .downloadPDF: .downloadPDF
        case .updateUserProfile: .updateUserProfile
        case .viewUserProfile: .viewUserProfile
        case .filters: .filters
        case .mapSalons: .mapSalons

        case .addAddress: .addAddress
        case .editAddress: .editAddress
        case .viewAddresses: .viewAddresses
        case .defaultAddress: .defaultAddress
        case .deleteAddress: .deleteAddress

        case .addWishlist: .addWishlist
        case .viewWishlists: .viewWishlists
        case .transactionHistory: .transactionHistory
        case .vendorTimeSlot: .vendorTimeSlot
        case .addSalonRating: .addSalonRating
        case .bookingDetails: .bookingDetails

        case .forgotPassword: .forgotPassword
        case .resendOTP: .resendOTP

        case .verifyOTP: .verifyOTP
        case .resendOtpRegistration: .resendOtpRegistration
        case .changePassword: .changePassword
        case .socialLogin: .socialLogin

        case .refreshFCMToken: .refreshFCMToken
        case .allNotifications: .allNotifications
        case .readByUser: .readByUser
        case .markAllRead: .markAllRead
        case .deleteNotification: .deleteNotification
        case .deleteAllNotifications: .deleteAllNotifications

        case .addToCart: .addToCart
        case .viewCart: .viewCart
        case .updateCart: .updateCart
        case .deleteItem: .deleteItem
        case .staffTimeslot: .staffTimeslot
        case .staffAvailableSlots: .staffAvailableSlots
        case .vendorServicesCategory: .vendorServicesCategory
        case .vendorServices: .vendorServices
        case .filterServiceCategories: .filterServiceCategories

        case .verifyOtpRegistration:.verifyOtpRegistration
        case .notificationSettingsRead: .notificationSettingsRead
        case .notificationSettingsUpdate: .notificationSettingsUpdate
        case .deleteAccount:.deleteAccount
        case .couponCodeApply: .couponCodeApply

        case .listAllCards: .listAllCards
        case .addCard: .addCard
        case .addReportReviews: .addReportReviews
        case .addLikesReview: .addLikesReview
            
        case .reminderNotificationRead: .reminderNotificationRead
        case .reminderNotificationUpdate: .reminderNotificationUpdate
            
        case .updateLanguage: .updateLanguage
        }
    }

    var method: HTTPMethod {
        switch self {
        // HTTP method: GET

        case .splashScreens,
             .servicesCategory,
             .googleMap,
             .timeSlotFilter,

             .promotionBanners,
             .privacyPolicy,
             .termsConditions,
             .faqs,
             .contactUs,
             .areas
             : return .get

        // HTTP method: POST
        case .logout,
             .loginWithEmail,
             .registerUser,
             .nearbySalons,
             .nearbyServices,
             .vendorExplore,
             .popularArtists,
             .searchVendor,
             .vendorDetail,
             .staffSelection,
             .bookings,
             .upcomingBookings,
             .completedBookings,
             .cancelledBookings,
             .cancelBooking,
             .modifyBooking,
             .downloadPDF,
             .updateUserProfile,
             .viewUserProfile,
             .filters,
             .mapSalons,

             .addAddress,
             .editAddress,
             .viewAddresses,
             .defaultAddress,
             .deleteAddress,

             .addWishlist,
             .viewWishlists,
             .transactionHistory,
             .vendorTimeSlot,
             .addSalonRating,
             .bookingDetails,
             .forgotPassword,
             .resendOTP,
             .verifyOTP,
             .resendOtpRegistration,
             .changePassword,
             .socialLogin,

             .refreshFCMToken,
             .allNotifications,
             .readByUser,
             .markAllRead,
             .deleteNotification,
             .deleteAllNotifications,

             .addToCart,
             .viewCart,
             .updateCart,
             .deleteItem,
             .staffTimeslot,
             .staffAvailableSlots,
             .vendorServicesCategory,
             .vendorServices,
             .filterServiceCategories,

             .verifyOtpRegistration,
             .notificationSettingsRead,
             .notificationSettingsUpdate,
             .deleteAccount,
             .couponCodeApply,
             .listAllCards,
             .addCard,
             .addReportReviews,
             .addLikesReview,
             .reminderNotificationRead,
             .reminderNotificationUpdate,
             .updateLanguage
            
             : return .post
        }
    }

    var task: NetworkTask {
        switch self {
        // HTTP method: GET

        case .splashScreens,
             .servicesCategory,
             .googleMap,
             .timeSlotFilter,

             .promotionBanners,
             .privacyPolicy,
             .termsConditions,
             .faqs,
             .contactUs,
             .areas
             : return .requestPlain

        // HTTP method: POST
        case
            let .logout(parameters: parameters),
            let .loginWithEmail(parameters: parameters),
            let .registerUser(parameters: parameters),
            let .nearbySalons(parameters: parameters),
            let .nearbyServices(parameters: parameters),
            let .vendorExplore(parameters: parameters),
            let .popularArtists(parameters: parameters),
            let .searchVendor(parameters: parameters),
            let .vendorDetail(parameters: parameters),
            let .staffSelection(parameters: parameters),
            let .bookings(parameters: parameters),
            let .upcomingBookings(parameters: parameters),
            let .completedBookings(parameters: parameters),
            let .cancelledBookings(parameters: parameters),
            let .cancelBooking(parameters: parameters),
            let .modifyBooking(parameters: parameters),
            let .downloadPDF(parameters: parameters),
            let .updateUserProfile(parameters: parameters),
            let .viewUserProfile(parameters: parameters),
            let .filters(parameters: parameters),
            let .mapSalons(parameters: parameters),

            let .addAddress(parameters: parameters),
            let .editAddress(parameters: parameters),
            let .viewAddresses(parameters: parameters),
            let .defaultAddress(parameters: parameters),
            let .deleteAddress(parameters: parameters),

            let .addWishlist(parameters: parameters),
            let .viewWishlists(parameters: parameters),
            let .transactionHistory(parameters: parameters),
            let .vendorTimeSlot(parameters: parameters),
            let .addSalonRating(parameters: parameters),
            let .bookingDetails(parameters: parameters),

            let .forgotPassword(parameters: parameters),
            let .resendOTP(parameters: parameters),
            let .verifyOTP(parameters: parameters),
            let .resendOtpRegistration(parameters: parameters),
            let .changePassword(parameters: parameters),
            let .socialLogin(parameters: parameters),

            let .refreshFCMToken(parameters: parameters),
            let .allNotifications(parameters: parameters),
            let .readByUser(parameters: parameters),
            let .markAllRead(parameters: parameters),
            let .deleteNotification(parameters: parameters),
            let .deleteAllNotifications(parameters: parameters),

            let .addToCart(parameters: parameters),
            let .viewCart(parameters: parameters),
            let .updateCart(parameters: parameters),
            let .deleteItem(parameters: parameters),
            let .staffTimeslot(parameters: parameters),
            let .staffAvailableSlots(parameters: parameters),
            let .vendorServicesCategory(parameters: parameters),
            let .vendorServices(parameters: parameters),
            let .filterServiceCategories(parameters: parameters),

            let .verifyOtpRegistration(parameters: parameters),
            let .notificationSettingsRead(parameters: parameters),
            let .notificationSettingsUpdate(parameters: parameters),
            let .deleteAccount(parameters: parameters),
            let .couponCodeApply(parameters: parameters),
            let .listAllCards(parameters: parameters),
            let .addCard(parameters: parameters),
            let .addReportReviews(parameters: parameters),
            let .addLikesReview(parameters: parameters),
            let .reminderNotificationRead(parameters: parameters),
            let .reminderNotificationUpdate(parameters: parameters),
            let .updateLanguage(parameters: parameters)

            : return .requestParameters(parameters: parameters)
        }
    }
}
