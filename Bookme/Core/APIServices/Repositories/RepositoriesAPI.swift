//
//  RepositoriesAPI.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//

import Alamofire
import Foundation

typealias EmptyStringArray = [String]
typealias CommonApiResponse = ApiBaseModel
typealias CommonResult<T: Codable> = Result<CommonApiResponse<T>, AFError>
typealias ApiResult<T: Codable> = Result<T, AFError>
var emptyDictionary: DictionaryType { [:] }

protocol RepositoriesAPIProtocol {
    // HTTP method: GET
    func splashScreens(parameters: Parameters, completionHandler: @escaping (CommonResult<[OnboardingModel]>) -> Void)
    func servicesCategory(parameters: Parameters, completionHandler: @escaping (CommonResult<[ServicesCategoryModel]>) -> Void)
    func googleMap(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func timeSlotFilter(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func logout(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func promotionBanners(parameters: Parameters, completionHandler: @escaping (CommonResult<[PromotionBannerModel]>) -> Void)
    func privacyPolicy(parameters: Parameters, completionHandler: @escaping (CommonResult<RichTextPageModel>) -> Void)
    func termsConditions(parameters: Parameters, completionHandler: @escaping (CommonResult<RichTextPageModel>) -> Void)
    func faqs(parameters: Parameters, completionHandler: @escaping (CommonResult<[FAQModel]>) -> Void)
    func contactUs(parameters: Parameters, completionHandler: @escaping (CommonResult<ContactUsModel>) -> Void)
    func areas(parameters: Parameters, completionHandler: @escaping (CommonResult<[AreaModel]>) -> Void)

    // HTTP method: POST
    func loginWithEmail(parameters: Parameters, completionHandler: @escaping (CommonResult<UserModel>) -> Void)
    func registerUser(parameters: Parameters, completionHandler: @escaping (CommonResult<User>) -> Void)
    func nearbySalons(parameters: Parameters, completionHandler: @escaping (CommonResult<[NearestVendorModel]>) -> Void)
    func nearbyServices(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func vendorExplore(parameters: Parameters, completionHandler: @escaping (CommonResult<[NearestVendorModel]>) -> Void)
    func popularArtists(parameters: Parameters, completionHandler: @escaping (CommonResult<[PopularArtistModel]>) -> Void)
    func searchVendor(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func vendorDetail(parameters: Parameters, completionHandler: @escaping (CommonResult<VendorDetailsModel>) -> Void)
    func bookings(parameters: Parameters, completionHandler: @escaping (CommonResult<CheckoutSuccessModel>) -> Void)
//    func staffSelection(parameters: Parameters, completionHandler: @escaping (CommonResult<[ServiceStaffModel]>) -> Void)
    func upcomingBookings(parameters: Parameters, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void)
    func completedBookings(parameters: Parameters, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void)
    func cancelledBookings(parameters: Parameters, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void)
    func cancelBooking(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func modifyBooking(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func downloadPDF(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)

    func updateUserProfile(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func viewUserProfile(parameters: Parameters, completionHandler: @escaping (CommonResult<ProfileModel>) -> Void)
    func filters(parameters: Parameters, completionHandler: @escaping (CommonResult<FiltersModel>) -> Void)
    func mapSalons(parameters: Parameters, completionHandler: @escaping (CommonResult<SaloonMapModel>) -> Void)

    func forgotPassword(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func resendOTP(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func verifyOTP(parameters: Parameters, completionHandler: @escaping (CommonResult<VerifyOTPResponse>) -> Void)
    func resendOtpRegistration(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    
    func changePassword(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func socialLogin(parameters: Parameters, completionHandler: @escaping (CommonResult<UserModel>) -> Void)

    func addAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func editAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func viewAddresses(parameters: Parameters, completionHandler: @escaping (CommonResult<[AddressModel]>) -> Void)
    func defaultAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func deleteAddress(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)

    func addWishlist(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func viewWishlists(parameters: Parameters, completionHandler: @escaping (CommonResult<[WishlistModel]>) -> Void)
    func transactionHistory(parameters: Parameters, completionHandler: @escaping (CommonResult<[TransactionHistoryModel]>) -> Void)
    func vendorTimeSlot(parameters: Parameters, completionHandler: @escaping (CommonResult<VendorTimeslotModel>) -> Void)
    func addSalonRating(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func bookingDetails(parameters: Parameters, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void)

    func refreshFCMToken(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func allNotifications(parameters: Parameters, completionHandler: @escaping (CommonResult<[NotificationModel]>) -> Void)
    func readByUser(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func markAllRead(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func deleteNotification(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func deleteAllNotifications(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    
    func addToCart(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func viewCart(parameters: Parameters, completionHandler: @escaping (CommonResult<[CartModel]>) -> Void)
    func updateCart(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func removeFromCart(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func staffTimeslot(parameters: Parameters, completionHandler: @escaping (CommonResult<VendorTimeslotModel>) -> Void)
    func staffAvailableSlots(parameters: Parameters, completionHandler: @escaping (CommonResult<StaffModel>) -> Void)
    func vendorServicesCategory(parameters: Parameters, completionHandler: @escaping (CommonResult<[ServicesCategoryModel]>) -> Void)
    func vendorServices(parameters: Parameters, completionHandler: @escaping (CommonResult<[VendorDetailsModel.Service]>) -> Void)
    
    func filterServiceCategories(parameters: Parameters, completionHandler: @escaping (CommonResult<FilterServiceCategoriesModel>) -> Void)
    
    
    
    func verifyOtpRegistration(parameters: Parameters, completionHandler: @escaping (CommonResult<UserModel>) -> Void)
    func notificationSettingsRead(parameters: Parameters, completionHandler: @escaping (CommonResult<[NotificationSettingsModel]>) -> Void)
    func notificationSettingsUpdate(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func deleteAccount(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    
    
    func couponCodeApply(parameters: Parameters, completionHandler: @escaping (CommonResult<CouponCodeModel>) -> Void)
    
    
    
    func listAllCards(parameters: Parameters, completionHandler: @escaping (CommonResult<PaymentCardModel>) -> Void)
    func addCard(parameters: Parameters, completionHandler: @escaping (CommonResult<String>) -> Void)
    func addReportReviews(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    func addLikesReview(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    
    
    func reminderNotificationRead(parameters: Parameters, completionHandler: @escaping (CommonResult<[ReminderNotificationModel]>) -> Void)
    func reminderNotificationUpdate(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    
    func updateLanguage(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void)
    
}

class RepositoriesAPI: BaseAPI<RepositoriesNetworking>, RepositoriesAPIProtocol {
    public static let shared = RepositoriesAPI()

    override private init() {
        print("RepositoriesAPI class is initialized")
    }

    // HTTP method: GET
    func splashScreens(parameters: Parameters, completionHandler: @escaping (CommonResult<[OnboardingModel]>) -> Void) {
        fetchData(target: .splashScreens, responseClass: CommonApiResponse<[OnboardingModel]>.self, completionHandler: completionHandler)
    }
    
    func servicesCategory(parameters: Parameters, completionHandler: @escaping (CommonResult<[ServicesCategoryModel]>) -> Void) {
        fetchData(target: .servicesCategory, responseClass: CommonApiResponse<[ServicesCategoryModel]>.self, completionHandler: completionHandler)
    }

    func googleMap(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .googleMap, responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func timeSlotFilter(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .timeSlotFilter, responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func promotionBanners(parameters: Parameters, completionHandler: @escaping (CommonResult<[PromotionBannerModel]>) -> Void) {
        fetchData(target: .promotionBanners, responseClass: CommonApiResponse<[PromotionBannerModel]>.self, completionHandler: completionHandler)
    }

    func privacyPolicy(parameters: Parameters, completionHandler: @escaping (CommonResult<RichTextPageModel>) -> Void) {
        fetchData(target: .privacyPolicy, responseClass: CommonApiResponse<RichTextPageModel>.self, completionHandler: completionHandler)
    }

    func termsConditions(parameters: Parameters, completionHandler: @escaping (CommonResult<RichTextPageModel>) -> Void) {
        fetchData(target: .termsConditions, responseClass: CommonApiResponse<RichTextPageModel>.self, completionHandler: completionHandler)
    }

    func faqs(parameters: Parameters, completionHandler: @escaping (CommonResult<[FAQModel]>) -> Void) {
        fetchData(target: .faqs, responseClass: CommonApiResponse<[FAQModel]>.self, completionHandler: completionHandler)
    }

    func contactUs(parameters: Parameters, completionHandler: @escaping (CommonResult<ContactUsModel>) -> Void) {
        fetchData(target: .contactUs, responseClass: CommonApiResponse<ContactUsModel>.self, completionHandler: completionHandler)
    }

    func areas(parameters: Parameters, completionHandler: @escaping (CommonResult<[AreaModel]>) -> Void) {
        fetchData(target: .areas, responseClass: CommonApiResponse<[AreaModel]>.self, completionHandler: completionHandler)
    }

    // HTTP method: POST
    func logout(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .logout(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func loginWithEmail(parameters: Parameters, completionHandler: @escaping (CommonResult<UserModel>) -> Void) {
        fetchData(target: .loginWithEmail(parameters: parameters), responseClass: CommonApiResponse<UserModel>.self, completionHandler: completionHandler)
    }

    func registerUser(parameters: Parameters, completionHandler: @escaping (CommonResult<User>) -> Void) {
        fetchData(target: .registerUser(parameters: parameters), responseClass: CommonApiResponse<User>.self, completionHandler: completionHandler)
    }

    func nearbySalons(parameters: Parameters, completionHandler: @escaping (CommonResult<[NearestVendorModel]>) -> Void) {
        fetchData(target: .nearbySalons(parameters: parameters), responseClass: CommonApiResponse<[NearestVendorModel]>.self, completionHandler: completionHandler)
    }

    func nearbyServices(parameters: Parameters, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .nearbyServices(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func vendorExplore(parameters: Parameters, completionHandler: @escaping (CommonResult<[NearestVendorModel]>) -> Void) {
        fetchData(target: .vendorExplore(parameters: parameters), responseClass: CommonApiResponse<[NearestVendorModel]>.self, completionHandler: completionHandler)
    }

    func popularArtists(parameters: Parameters, completionHandler: @escaping (CommonResult<[PopularArtistModel]>) -> Void) {
        fetchData(target: .popularArtists(parameters: parameters), responseClass: CommonApiResponse<[PopularArtistModel]>.self, completionHandler: completionHandler)
    }

    func searchVendor(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .searchVendor(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func vendorDetail(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VendorDetailsModel>) -> Void) {
        fetchData(target: .vendorDetail(parameters: parameters), responseClass: CommonApiResponse<VendorDetailsModel>.self, completionHandler: completionHandler)
    }

//    func staffSelection(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[ServiceStaffModel]>) -> Void) {
//        fetchData(target: .staffSelection(parameters: parameters), responseClass: CommonApiResponse<[ServiceStaffModel]>.self, completionHandler: completionHandler)
//    }

    func bookings(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<CheckoutSuccessModel>) -> Void) {
        fetchData(target: .bookings(parameters: parameters), responseClass: CommonApiResponse<CheckoutSuccessModel>.self, completionHandler: completionHandler)
    }

    func upcomingBookings(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void) {
        fetchData(target: .upcomingBookings(parameters: parameters), responseClass: CommonApiResponse<[BookingAppointmentModel]>.self, completionHandler: completionHandler)
    }

    func completedBookings(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void) {
        fetchData(target: .completedBookings(parameters: parameters), responseClass: CommonApiResponse<[BookingAppointmentModel]>.self, completionHandler: completionHandler)
    }

    func cancelledBookings(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void) {
        fetchData(target: .cancelledBookings(parameters: parameters), responseClass: CommonApiResponse<[BookingAppointmentModel]>.self, completionHandler: completionHandler)
    }

    func cancelBooking(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .cancelBooking(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func modifyBooking(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .modifyBooking(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func downloadPDF(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .downloadPDF(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func updateUserProfile(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .updateUserProfile(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func viewUserProfile(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<ProfileModel>) -> Void) {
        fetchData(target: .viewUserProfile(parameters: parameters), responseClass: CommonApiResponse<ProfileModel>.self, completionHandler: completionHandler)
    }

    func filters(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<FiltersModel>) -> Void) {
        fetchData(target: .filters(parameters: parameters), responseClass: CommonApiResponse<FiltersModel>.self, completionHandler: completionHandler)
    }

    func mapSalons(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<SaloonMapModel>) -> Void) {
        fetchData(target: .mapSalons(parameters: parameters), responseClass: CommonApiResponse<SaloonMapModel>.self, completionHandler: completionHandler)
    }

    func forgotPassword(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .forgotPassword(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func resendOTP(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .resendOTP(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func verifyOTP(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VerifyOTPResponse>) -> Void) {
        fetchData(target: .verifyOTP(parameters: parameters), responseClass: CommonApiResponse<VerifyOTPResponse>.self, completionHandler: completionHandler)
    }

    func resendOtpRegistration(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .resendOtpRegistration(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func changePassword(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .changePassword(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func socialLogin(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<UserModel>) -> Void) {
        fetchData(target: .socialLogin(parameters: parameters), responseClass: CommonApiResponse<UserModel>.self, completionHandler: completionHandler)
    }

    func addAddress(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addAddress(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func editAddress(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .editAddress(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func viewAddresses(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[AddressModel]>) -> Void) {
        fetchData(target: .viewAddresses(parameters: parameters), responseClass: CommonApiResponse<[AddressModel]>.self, completionHandler: completionHandler)
    }
    
    func defaultAddress(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .defaultAddress(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func deleteAddress(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .deleteAddress(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    

    func addWishlist(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addWishlist(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func viewWishlists(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[WishlistModel]>) -> Void) {
        fetchData(target: .viewWishlists(parameters: parameters), responseClass: CommonApiResponse<[WishlistModel]>.self, completionHandler: completionHandler)
    }

    func transactionHistory(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[TransactionHistoryModel]>) -> Void) {
        fetchData(target: .transactionHistory(parameters: parameters), responseClass: CommonApiResponse<[TransactionHistoryModel]>.self, completionHandler: completionHandler)
    }

    func vendorTimeSlot(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VendorTimeslotModel>) -> Void) {
        fetchData(target: .vendorTimeSlot(parameters: parameters), responseClass: CommonApiResponse<VendorTimeslotModel>.self, completionHandler: completionHandler)
    }

    func addSalonRating(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addSalonRating(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func bookingDetails(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[BookingAppointmentModel]>) -> Void) {
        fetchData(target: .bookingDetails(parameters: parameters), responseClass: CommonApiResponse<[BookingAppointmentModel]>.self, completionHandler: completionHandler)
    }

    func refreshFCMToken(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .refreshFCMToken(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func allNotifications(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[NotificationModel]>) -> Void) {
        fetchData(target: .allNotifications(parameters: parameters), responseClass: CommonApiResponse<[NotificationModel]>.self, completionHandler: completionHandler)
    }

    func readByUser(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .readByUser(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func markAllRead(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .markAllRead(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func deleteNotification(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .deleteNotification(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    func deleteAllNotifications(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .deleteAllNotifications(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func addToCart(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addToCart(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func viewCart(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[CartModel]>) -> Void) {
        fetchData(target: .viewCart(parameters: parameters), responseClass: CommonApiResponse<[CartModel]>.self, completionHandler: completionHandler)
    }
    
    func updateCart(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .updateCart(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func removeFromCart(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .deleteItem(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    func staffTimeslot(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VendorTimeslotModel>) -> Void) {
        fetchData(target: .staffTimeslot(parameters: parameters), responseClass: CommonApiResponse<VendorTimeslotModel>.self, completionHandler: completionHandler)
    }
    
    func staffAvailableSlots(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<StaffModel>) -> Void) {
        fetchData(target: .staffAvailableSlots(parameters: parameters), responseClass: CommonApiResponse<StaffModel>.self, completionHandler: completionHandler)
    }
    
    
    func vendorServicesCategory(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[ServicesCategoryModel]>) -> Void) {
        fetchData(target: .vendorServicesCategory(parameters: parameters), responseClass: CommonApiResponse<[ServicesCategoryModel]>.self, completionHandler: completionHandler)
    }
    func vendorServices(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[VendorDetailsModel.Service]>) -> Void) {
        fetchData(target: .vendorServices(parameters: parameters), responseClass: CommonApiResponse<[VendorDetailsModel.Service]>.self, completionHandler: completionHandler)
    }
    
    func filterServiceCategories(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<FilterServiceCategoriesModel>) -> Void) {
        fetchData(target: .filterServiceCategories(parameters: parameters), responseClass: CommonApiResponse<FilterServiceCategoriesModel>.self, completionHandler: completionHandler)
    }
    
    
    func verifyOtpRegistration(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<UserModel>) -> Void) {
        fetchData(target: .verifyOtpRegistration(parameters: parameters), responseClass: CommonApiResponse<UserModel>.self, completionHandler: completionHandler)
    }
    func notificationSettingsRead(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[NotificationSettingsModel]>) -> Void) {
        fetchData(target: .notificationSettingsRead(parameters: parameters), responseClass: CommonApiResponse<[NotificationSettingsModel]>.self, completionHandler: completionHandler)
    }
    func notificationSettingsUpdate(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .notificationSettingsUpdate(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func deleteAccount(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .deleteAccount(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    func couponCodeApply(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<CouponCodeModel>) -> Void) {
        fetchData(target: .couponCodeApply(parameters: parameters), responseClass: CommonApiResponse<CouponCodeModel>.self, completionHandler: completionHandler)
    }
    
    
    
    func listAllCards(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<PaymentCardModel>) -> Void) {
        fetchData(target: .listAllCards(parameters: parameters), responseClass: CommonApiResponse<PaymentCardModel>.self, completionHandler: completionHandler)
    }
    func addCard(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<String>) -> Void) {
        fetchData(target: .addCard(parameters: parameters), responseClass: CommonApiResponse<String>.self, completionHandler: completionHandler)
    }
    func addReportReviews(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addReportReviews(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    func addLikesReview(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .addLikesReview(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    
    func reminderNotificationRead(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<[ReminderNotificationModel]>) -> Void) {
        fetchData(target: .reminderNotificationRead(parameters: parameters), responseClass: CommonApiResponse<[ReminderNotificationModel]>.self, completionHandler: completionHandler)
    }
    
    func reminderNotificationUpdate(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .reminderNotificationUpdate(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }
    
    
    func updateLanguage(parameters: Parameters = emptyDictionary, completionHandler: @escaping (CommonResult<VoidStruct>) -> Void) {
        fetchData(target: .updateLanguage(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self, completionHandler: completionHandler)
    }

    
}
