//
//  TargetType.swift
//  Bookme
//
//  Created by Apple on 22/04/2024.
//


import Foundation
import Alamofire

protocol TargetType {
    
    var baseURL: String {get}
    
    var path: String {get}
    
    var method: HTTPMethod {get}
    
    var task: NetworkTask {get}
    
    var headers: [String: String]? {get}
}

extension TargetType {
    var headerProgress:[String:String] {
        var data = ["Content-type":"application/json"]
        data["Accept-Language"] = AppState.language
        if let token = UserDefaultsSecure.sharedInstance.getGeneratedToken() {
            data["Authorization"] = "Bearer \(token)"
            print(token)
        }
      
        return data
    }
    var baseURL: String { AppConstants.Server.baseURL + AppConstants.Server.apiPath }
    var headers: [String:String]? { headerProgress }
}
enum NetworkTask {
    case requestPlain
    case requestParameters(parameters: [String: Any], encoding: ParameterEncoding = JSONEncoding.default)
}
