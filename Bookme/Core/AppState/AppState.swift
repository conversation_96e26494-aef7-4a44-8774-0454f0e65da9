//
//  AppState.swift
//  Bookme
//
//  Created by Apple on 30/01/2024.
//

import Alamofire
import SwiftUI

enum InitialScreen {
    case splash, onboarding, dashboard, authentication
}

class AppState: SuperViewModel {
    @Published var appLanguage: AppLanguageType = (AppLanguageType(rawValue: AppState.language) ?? .en)
    @Published var serviceType: [ServiceType] = []

    @Published var initialScreen: InitialScreen = .splash
    @Published var selectedTab: Tab = .home
    @Published var rootViewId = UUID()
    @Published var isTabBarHidden: Bool = true
    @Published var isCustomBottomBarHidden: Bool = false
    @Published var showSideMenu: Bool = false
    @Published var isSearchFocused: Bool = false
    @Published var vendorDetailsModel: VendorDetailsModel?
    @Published var servicesList: [ServicesCategoryModel] = []
    @Published var nearestVendorList: [NearestVendorModel] = []
    @Published var selectedHeader: ServicesCategoryModel?

    @Published var addressListingViewID: UUID = .init()

    @Published var selectedServices: [VendorDetailsModel.Service] = []
    @Published var bookingRequestModelList: [BookingRequestModel] = []
    @Published var selectedBookingRequestModel: BookingRequestModel?

    @Published var showLogoutPopupView: Bool = false

    @Published var event: Event?

    @Published var showSplash: Bool = AppState.showHomeSplash

    @Published var bookingAppointmentViewID: UUID = .init()

    @Published var isConnected = true
     var networkMonitor = NetworkMonitor()

    // MARK: - Reactive User Model
    /// Published user model for reactive UI updates across the app
    @Published var currentUserModel: UserModel? = AppState.userModel {
        didSet {
            // Sync with UserDefaults when the published property changes
            AppState.userModel = currentUserModel
        }
    }


    func checkNetworkConnection() {
        // In init()
        networkMonitor.$isConnected
            .debounce(for: .seconds(0.8), scheduler: RunLoop.main)
            .sink { [weak self] isConnected in
                self?.isConnected = isConnected
            }
            .store(in: &cancellables)
    }

    override init() {
        initialScreen = AppState.isOnboardingShown ? .dashboard : .splash

//            self.serviceType = ServiceType(rawValue: AppState.serviceType) ?? .men)

        serviceType = AppState.serviceType.compactMap(ServiceType.init)

        super.init()

        // Initialize currentUserModel with existing UserDefaults value
        currentUserModel = AppState.userModel

        checkNetworkConnection()
    }

    func onSplashAppear(_ appState: EnvironmentObject<AppState>) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                self.showSplash = false
            }
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.3) {
            withAnimation {
                self.updateTabBarHidden(false)
            }
        }
    }

    func restRootViewID() {
        // reseting rootViewId variable to a new UUID
        rootViewId = .init()
    }

    func updateServiceType(_ type: [ServiceType], reset: Bool = false) {
        let list = type.map(\.rawValue)

        if AppState.serviceType != list {
            AppState.serviceType = list
            serviceType = type
        }
        if reset {
            Utilities.enQueue {
                self.restRootViewID()
            }
        }
    }

    func updateLocale(_ type: AppLanguageType, reset: Bool = false) {
        if AppState.language != type.rawValue {
            AppState.language = type.rawValue
            withAnimation(.bouncy) { self.appLanguage = type }
            if reset { Utilities.enQueue { self.restRootViewID() }
            }
        }
    }

    func onSearch() {
        updateIsSearchFocused(true)
        updateHeaderList(servicesList.first)
        updateSelectedTab(.explore)
    }

    func updateTabBarHidden(_ value: Bool) {
        Utilities.enQueue {
//            withAnimation(.bouncy) {
            self.isTabBarHidden = value
//            }
        }
    }

    func updateCustomBottomBarHidden(_ value: Bool) {
        Utilities.enQueue {
            self.isCustomBottomBarHidden = value
        }
    }

    func updatePopUpView(_ value: Bool) { withAnimation {
        self.showLogoutPopupView = value
    } }

    func onLogout(completion: @escaping () -> Void) {
        guard let token = UserDefaultsSecure.sharedInstance.getGeneratedToken() else { return }

//        let parameters = ["token": token]
//        onApiCall(api.logout, parameters: parameters) { response in
//            if response.success { completion() }
//        }
        let url = AppConstants.Server.baseURL + AppConstants.Server.apiPath + "logout"
        // Your Bearer token
        let bearerToken = token
        // Define headers with the Bearer token
        let headers: HTTPHeaders = [
            "Authorization": "Bearer \(bearerToken)",
            "Accept": "application/json"
        ]
        updatePageState(.loading(true))
        AF.request(url, method: .post, parameters: [:], encoding: JSONEncoding.default, headers: headers)
            .validate(statusCode: 200 ..< 500)
            .responseDecodable(of: CommonApiResponse<VoidStruct>.self) { response in
                switch response.result {
                case let .success(data):
                    if data.success {
                        self.updatePageState(.stable)
                        completion()
                    } else {
                        self.updatePageState(.failure(error: data.message))
                    }
                case let .failure(error):
                    print(error)
                    self.updatePageState(.failure(error: error.localizedDescription))
                }
            }
    }

    func resetBookingAppointmentViewID() { bookingAppointmentViewID = .init() }

    func updateIsSearchFocused(_ value: Bool) {
        isSearchFocused = value
    }

    func onSelectService(_ model: VendorDetailsModel.Service) {
        if selectedServices.contains(model) {
            selectedServices.remove(object: model)
        } else {
            selectedServices.append(model)
        }
    }

    func updateSelectedBookingRequestModel(_ value: BookingRequestModel?) {
        selectedBookingRequestModel = value
    }

    func updateBookingRequestModelList(_ value: BookingRequestModel?) {
        let timeFormat = "h:mm a"
        guard let value = value else { return }
        let servicesID: Int = value.service[0].servicesID
        var i = 0
        if bookingRequestModelList.contains(where: { $0.service[0].servicesID == servicesID }) {
            if var _ = bookingRequestModelList.firstIndex(where: { $0.service[0].servicesID == servicesID }) {
                for model in bookingRequestModelList {
                    var timeDate = value.time?.toDate(inputFormate: timeFormat)

                    var tempValue = model
                    tempValue.date = value.date

                    let minutes = Double(30 * i)
                    timeDate = timeDate?.withAddedMinutes(minutes: minutes)
                    let timeString = timeDate?.toString(outputFormate: timeFormat)
                    tempValue.time = timeString

                    bookingRequestModelList[i] = tempValue
                    i += 1
                }
            }

            updateSelectedBookingRequestModel(bookingRequestModelList.first)
        }
    }

    func updateAddressListingViewID() { addressListingViewID = .init() }

    func checkHeader(_ value: ServicesCategoryModel) -> Bool { value == selectedHeader }

    func updateHeaderList(_ value: ServicesCategoryModel?) { withAnimation(.bouncy) { selectedHeader = value }}

    func updateSelectedTab(_ tab: Tab) { selectedTab = tab }

    func updateInitialScreen(_ type: InitialScreen) {
        initialScreen = type
        if type == .dashboard {
            AppState.isOnboardingShown = true
        }
    }

    func onAuthentication(_ user: UserModel?) {
        AppState.isLoggedIn = user != nil
        AppState.userModel = user
        currentUserModel = user

        if let _ = user {
        } else {
            UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: nil)
        }
    }

    /// Updates the user model and triggers reactive UI updates
    /// - Parameter user: The updated user model
    func updateUserModel(_ user: UserModel?) {
        currentUserModel = user
        AppState.userModel = user
    }
}

import CoreLocation

extension AppState {
    static var userModel: UserModel? {
        get { UserDefaults.userModel }
        set { UserDefaults.userModel = newValue }
    }

    static var isLoggedIn: Bool {
        get { UserDefaults.isLoggedIn }
        set { UserDefaults.isLoggedIn = newValue }
    }

    static var isPasskeyLoggedIn: Bool {
        get { UserDefaults.isPasskeyLoggedIn }
        set { UserDefaults.isPasskeyLoggedIn = newValue }
    }

    static var userLocation: CLLocationCoordinate2D? {
        get {
            if let userLocation = UserDefaults.userLocation {
                return .init(dict: userLocation)
            } else {
                return nil
            }
        }
        set { UserDefaults.userLocation = newValue?.asDictionary }
    }

    static var isOnboardingShown: Bool {
        get { UserDefaults.isOnboardingShown }
        set { UserDefaults.isOnboardingShown = newValue }
    }

    static var showHomeSplash: Bool {
        get { UserDefaults.showHomeSplash }
        set { UserDefaults.showHomeSplash = newValue }
    }

    static var deviceID: String {
        UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
    }

    static var language: String {
        get { UserDefaults.language }
        set { UserDefaults.language = newValue }
    }

    static var serviceType: [String] {
        get { UserDefaults.serviceType }
        set { UserDefaults.serviceType = newValue }
    }

    static var fcmToken: String {
        get { UserDefaults.fcmToken }
        set { UserDefaults.fcmToken = newValue }
    }
}

private extension UserDefaults {
    static var userModel: UserModel? {
        get {
            guard let data = standard.data(forKey: #function) else { return nil }
            return (try? JSONDecoder().decode(UserModel.self, from: data))
        }
        set {
            guard let data = try? JSONEncoder().encode(newValue) else { return }
            standard.set(data, forKey: #function)
        }
    }

    static var isLoggedIn: Bool {
        get { standard.value(forKey: #function) as? Bool ?? false }
        set { standard.set(newValue, forKey: #function) }
    }

    static var isPasskeyLoggedIn: Bool {
        get { standard.value(forKey: #function) as? Bool ?? false }
        set { standard.set(newValue, forKey: #function) }
    }

    static var userLocation: CLLocationCoordinate2D.CLLocationDictionary? {
        get { standard.value(forKey: #function) as? CLLocationCoordinate2D.CLLocationDictionary }
        set { standard.set(newValue, forKey: #function) }
    }

    static var isOnboardingShown: Bool {
        get { standard.value(forKey: #function) as? Bool ?? false }
        set { standard.set(newValue, forKey: #function) }
    }

    static var showHomeSplash: Bool {
        get { standard.value(forKey: #function) as? Bool ?? false }
        set { standard.set(newValue, forKey: #function) }
    }

    static var accessToken: String {
        get { standard.value(forKey: #function) as? String ?? "" }
        set { standard.set(newValue, forKey: #function) }
    }

    var language: String {
        get { value(forKey: #function) as? String ?? Locale.current.language.languageCode?.identifier ?? "" }
        set { set(newValue, forKey: #function) }
    }

    static var language: String {
        get { standard.value(forKey: #function) as? String ?? "en" }
        set { standard.set(newValue, forKey: #function) }
    }

    static var serviceType: [String] {
        get { standard.value(forKey: #function) as? [String] ?? ["men"] }
        set { standard.set(newValue, forKey: #function) }
    }

    static var fcmToken: String {
        get { standard.value(forKey: #function) as? String ?? "" }
        set { standard.set(newValue, forKey: #function) }
    }

    var showDashboard: Bool {
        get { value(forKey: #function) as? Bool ?? false }
        set { set(newValue, forKey: #function) }
    }

    var changeLanguage: Bool {
        get { value(forKey: #function) as? Bool ?? false }
        set { set(newValue, forKey: #function) }
    }
}

enum AppLanguageType: String, CaseIterable, Identifiable {
    case en, ar

    var id: String {
        switch self {
        case .en:
            "en"
        case .ar:
            "ar"
        }
    }

    var title: String {
        switch self {
        case .en:
            "english"
        case .ar:
            "عربي"
        }
    }

    var locale: Locale {
        switch self {
        case .en:
            Locale(identifier: rawValue)
        case .ar:
            Locale(identifier: rawValue)
        }
    }

    var direction: LayoutDirection {
        switch self {
        case .en:
            .leftToRight
        case .ar:
            .rightToLeft
        }
    }
}

enum ServiceType: String, CaseIterable, Identifiable {
    case men, women, kids

    var id: String {
        switch self {
        case .men:
            "men"
        case .women:
            "women"
        case .kids:
            "kids"
        }
    }

    var title: String {
        switch self {
        case .men:
            "Men"
        case .women:
            "Women"
        case .kids:
            "Kids"
        }
    }
}
