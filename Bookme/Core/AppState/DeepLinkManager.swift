//
//  DeepLinkManager.swift
//  Bookme
//
//  Created by Apple on 21/12/2024.
//
import SwiftUI

final class DeepLinkManager: ObservableObject {
    @Published var vendorId: String?

    // Generate a universal link using vendorId
    func generateUniversalLink(with vendorID: String, completion: @escaping (URL?) -> Void) {
        // Universal link format
        let universalLink = URL(string: "\(AppConstants.Server.baseURL)vendor/\(vendorID)")
        completion(universalLink)
    }

    // Handle universal link directly
    func handleIncomingUniversalLink(_ url: URL) {
        print("Incoming Universal Link: \(url.absoluteString)")

        // Parse the path to extract vendor ID
        let pathComponents = url.pathComponents
        if let index = pathComponents.firstIndex(of: "vendor"), pathComponents.count > index + 1 {
            let vendorId = pathComponents[index + 1]
            print("Extracted Vendor ID from path: \(vendorId)")
            self.vendorId = vendorId
        } else {
            print("Vendor ID not found in the URL path")
        }
    }
}

struct DeepLinkNavigationModifier: ViewModifier {
    let routesType: RoutesType
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var deepLinkManager: DeepLinkManager
    @Environment(RouterManager.self) private var routerManager

    func body(content: Content) -> some View {
        content

            .onChange(of: deepLinkManager.vendorId, initial: true) { _, vendorId in
                if let vendorId = vendorId {
                    print("Navigating to Vendor ID: \(vendorId)") // Log navigation action
                    deepLinkManager.vendorId = nil // Reset vendorId after use

                    Utilities.enQueue(after: .now() + 0.5) { appState.updateTabBarHidden(true) }

                    routerManager.push(
                        to: .shopDetails(type: .homeRoute, vendorID: Int(vendorId) ?? 0),
                        where: routesType
                    )
                }
            }
    }
}

extension View {
    func handleDeepLinkNavigation(routesType: RoutesType) -> some View {
        modifier(DeepLinkNavigationModifier(routesType: routesType))
    }
}
