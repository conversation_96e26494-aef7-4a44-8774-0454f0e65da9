//
//  UserModel.swift
//  Bookme
//
//  Created by Apple on 27/04/2024.
//

import Foundation

// MARK: - UserModel

struct UserModel: Codable {
    var user: User
}

// MARK: - User

struct User: Codable {
    let id: Int
    var name: String
    var email, userPhone, image, dob, gender, deviceID: String?
    let firstname, lastname, address, aboutme, facebookID, emailVerifiedAt, referralCode, lat, lon, createdAt, updatedAt, country, vendorid, area, street, house, avenue: String?
    let walletCredits, rewards, phoneVerified: Int?
    let block: Int?
    let otp: Int?
    var imageUrl: String { AppConstants.Server.baseURL + (image ?? "") }

    enum CodingKeys: String, CodingKey {
        case id
        case userPhone = "user_phone"
        case name, firstname, lastname, image, email, address, aboutme, otp
        case facebookID = "facebook_id"
        case emailVerifiedAt = "email_verified_at"
        case deviceID = "device_id"
        case walletCredits = "wallet_credits"
        case rewards
        case phoneVerified = "phone_verified"
        case referralCode = "referral_code"
        case block, lat, lon
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case gender = "Gender"
        case country = "Country"
        case vendorid, dob, area, street, house, avenue
    }
}

struct SocialUserModel: Identifiable, Equatable {
    let id: String?
    let fullName, firstName, lastName, email, photo: String?
    let type: SocialLoginType
}
