//
//  Route.swift
//  Bookme
//
//  Created by Apple on 01/02/2024.
//

import SwiftUI

class CallBack<T: RoutableProtocol>: RoutableProtocol {
    static func == (lhs: CallBack, rhs: CallBack) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }

    let onBack: TypeCallback<T>
    init(onBack: @escaping TypeCallback<T>) {
        self.onBack = onBack
    }
}

class EmptyCallBack: RoutableProtocol {
    static func == (lhs: EmptyCallBack, rhs: EmptyCallBack) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }

    let onBack: VoidCallback
    init(onBack: @escaping VoidCallback) {
        self.onBack = onBack
    }
}

enum Route: Equatable, Identifiable, Hashable {
    case onboarding,
         signIn(type: RoutesType, getBack:Bool = false, onGetBack: EmptyCallBack? = nil),
         signUp,
         signUpOtp(userID: Int, isEmail: Bool, viewModel: AuthViewModel),
         filter(model:FiltersRequest?,onDone: CallBack<FiltersRequest?>),
         profile,
         addressListing,
         addAddress(model: AddressModel?, onDone: EmptyCallBack),
         paymentMethod,
         saloonMap,
         shopDetails(type: RoutesType, vendorID: Int, selectedType: ShopDetailsHeaderType = .service),
         shopDetailsService(vendorID: Int?, selectedServiceIDs: [Int], onSelect: CallBack<VendorDetailsModel.Service>),
         
         
         
         shopDetailsStaffModel(staffModelRequest:StaffModelRequest, selectedStaffID: String?, onSelect: CallBack<StaffModel.StaffData>),
         bookAppointment(vendor: VendorShortDetailsModel, selectedService: [VendorDetailsModel.Service],  bookID: Int?),
         cancelModify(model: BookingAppointmentModel),
         eReceipt(model: BookingAppointmentModel),
         settings,
         notificationSettings,
         saved,
         transaction,
         helpCenter,
         customRichTextPage(type: CustomRichTextPageType),
         passwordManager,
         notification,
         forgotPassword(type: RoutesType),
         forgotPasswordOtp(type: RoutesType, isEmail:Bool, ForgotPasswordViewModel),
         changesPassword(type: RoutesType, ForgotPasswordViewModel),
         changePasswordSuccess(type: RoutesType, fromForgotPassword: Bool),
         checkoutView(viewModel: BookAppointmentViewModel),
         userPaymentCard(type: RoutesType),
         addCard(type: RoutesType),
         paymentGateway(link: String, onRedirect: CallBack<PaymentGatewayModel?>),
         uPaymentCard(link: String, onRedirect: CallBack<Bool>),
         bookingSuccess(type: RoutesType, model:PaymentGatewayModel),
         mainCategory(category:[ServicesCategoryModel]),
         notificationReminder,
         serviceFor

    var id: Self { self } // ← Perfect for SwiftUI

    var rawValue: String {
        switch self {
        case .onboarding: "onboarding"
        case .signIn: "signIn"
        case .signUp: "signUp"
        case .signUpOtp: "signUpOtp"
        case .filter: "filter"
        case .profile: "profile"
        case .addressListing: "addressListing"
        case .addAddress: "addAddress"
        case .paymentMethod: "paymentMethod"
        case .saloonMap: "saloonMap"
        case .shopDetails: "shopDetails"
        case .shopDetailsService: "shopDetailsService"
        case .bookAppointment: "bookAppointment"
        case .cancelModify: "cancelModify"
        case .eReceipt: "eReceipt"
        case .settings: "settings"
        case .notificationSettings: "notificationSettings"
        case .saved: "saved"
        case .transaction: "transaction"
        case .helpCenter: "helpCenter"
        case .customRichTextPage: "customRichTextPage"
        case .passwordManager: "passwordManager"
        case .notification: "notification"
        case .forgotPassword: "forgotPassword"
        case .forgotPasswordOtp: "otp"
        case .changesPassword: "changesPassword"
        case .changePasswordSuccess: "changePasswordSuccess"
        case .checkoutView: "checkoutView"
        case .userPaymentCard: "checkoutPaymentMethod"
        case .addCard: "addCard"
        case .uPaymentCard: "uPaymentCard"
        case .paymentGateway: "paymentGateway"
        case .bookingSuccess: "bookingSuccess"
        case .shopDetailsStaffModel:"shopDetailsStaffModel"
        case .mainCategory:"mainCategory"
        case .notificationReminder:"notificationReminder"
        case .serviceFor:"serviceFor"
        }
    }
}

extension Route: View {
    var body: some View {
        switch self {
        case .onboarding:
            OnboardingView()
        case let .signIn(type, getBack, onGetBack):
            SignInView(routesType: type, getBack: getBack, onGetBack: onGetBack?.onBack)
        case .signUp:
            SignUpView()
        case let .signUpOtp(userID, isEmail, viewModel):
            RegistrationOtpView(userID: userID, isEmail: isEmail, authViewModel: viewModel)
        case let .filter(model,onDone):
            FilterView(model:model,onApplyFilter: onDone.onBack)
                .environment(\.colorScheme, .light)
        case .profile:
            ProfileView()
        case .addressListing:
            AddressListingView()
        case let .addAddress(model, onDone):
            AddAddressView(model: model, onDone: onDone.onBack)
        case .paymentMethod:
            PaymentMethodView()
        case .saloonMap:
            SaloonMapView()
        case let .shopDetails(type, vendorID, selectedType):
            ShopDetailsView(routesType: type, vendorID: vendorID, selectedType: selectedType)
        case let .shopDetailsStaffModel(StaffModelRequest, selectedStaffID, onSelect):
            ShopDetailsStaffView(staffModelRequest: StaffModelRequest, selectedStaffID: selectedStaffID, onSelect: onSelect.onBack)
        case let .bookAppointment(vendor, selectedService, bookID):
            BookAppointmentView(vendor: vendor, selectedService: selectedService, bookID: bookID)
        case let .shopDetailsService(vendorID, selectedServiceIDs, onSelect):
            ShopDetailsServiceView(vendorID: vendorID, selectedServiceIDs: selectedServiceIDs, onSelect: onSelect.onBack)
        case let .cancelModify(model):
            CancelModifyView(model: model)
        case let .eReceipt(model):
            EReceiptView(model: model)
        case .settings:
            SettingsView()
        case .notificationSettings:
            UserNotificationSettingsView()
        case .saved:
            SavedView()
        case .transaction:
            TransactionView()
        case .helpCenter:
            HelpCenterView()
        case let .customRichTextPage(type):
            CustomRichTextPageView(customRichTextPageType: type)
        case .passwordManager:
            PasswordManagerView()
        case .notification:
            NotificationView()
        case let .forgotPassword(type):
            ForgotPasswordView(routesType: type)
        case let .forgotPasswordOtp(type, isEmail, viewModel):
            ForgotPasswordOtpView(routesType: type, isEmail: isEmail, authViewModel: viewModel)
        case let .changesPassword(type, viewModel):
            ChangePasswordView(routesType: type, authViewModel: viewModel)
        case let .changePasswordSuccess(type, fromForgotPassword):
            ChangePasswordSuccessView(routesType: type, fromForgotPassword: fromForgotPassword)
        case let .checkoutView(viewModel):
            CheckoutView(viewModel: viewModel)
        case let .paymentGateway(link, callback):
            UPaymentView(url: link, onPaymentCapture: callback.onBack)
        case let .uPaymentCard(link, onRedirect):
            UPaymentCardView(url: link, onPaymentCapture: onRedirect.onBack)
        case let .userPaymentCard(type: type):
            UserPaymentCardView(routesType: type)
        case let .addCard(type: type):
            AddCardView(routesType: type)
        case let .bookingSuccess(type, model):
            BookingSuccessView(routesType: type, model:model)
        case let .mainCategory(category):
            MainCategoryView(servicesList: category)
        case .notificationReminder:
            NotificationReminderView()
        case .serviceFor:
            ServiceForView()
        }
    }
}

extension Binding: @retroactive Equatable where Value: Equatable {
    public static func == (left: Binding<Value>, right: Binding<Value>) -> Bool {
        left.wrappedValue == right.wrappedValue
    }
}

extension Binding: @retroactive Hashable where Value: Hashable {
    public func hash(into hasher: inout Hasher) {
        hasher.combine(self.wrappedValue.hashValue)
    }
}
