//
//  RouterManager.swift
//  Bookme
//
//  Created by Apple on 30/01/2024.
//

import SwiftUI

// Enum Types for distinguishing general and tab routes
enum RoutesType: String, Hashable {
    case generalRoute, homeRoute, exploreRoute, appointmentRoute, myAccountRoute
}

enum AuthViewType {
    case signIn, signUp, success
}


@Observable
final class RouterManager {
    // For showing onboarding screen or tab decision

     var authViewType: AuthViewType = .signIn

    // For out side Tab routes navigations
     var generalRouteList = [Route]()

    // For inside Tab routes navigations
     var homeRouteList = [Route]()
     var exploreRouteList = [Route]()
     var appointmentRouteList = [Route]()
     var myAccountRouteList = [Route]()

    init() {
    }

    // For pushing new screen to the given routes
    func push(to screen: Route, appState: AppState? = nil, where routesType: RoutesType = .generalRoute, forcefully:Bool = false) {
        
        var routesType: RoutesType = routesType
        if let appState = appState { routesType = mapRouterWithTab(appState: appState) }
        Utilities.enQueue { [self] in
            switch routesType {
            case .generalRoute:
                if !forcefully{
                    guard !generalRouteList.contains(screen) else { return printRouteException() }
                }
                generalRouteList.append(screen)
            case .homeRoute:
                if !forcefully{
                    guard !homeRouteList.contains(screen) else { return printRouteException() }}
                homeRouteList.append(screen)
            case .exploreRoute:
                if !forcefully{
                    guard !exploreRouteList.contains(screen) else { return printRouteException() }}
                exploreRouteList.append(screen)
            case .appointmentRoute:
                if !forcefully{
                    guard !appointmentRouteList.contains(screen) else { return printRouteException() }}
                appointmentRouteList.append(screen)
            case .myAccountRoute:
                if !forcefully{
                    guard !myAccountRouteList.contains(screen) else { return printRouteException() }}
                myAccountRouteList.append(screen)
          
            }

            func printRouteException() { print("Route already exist contain") }
        }
    }

    // For navigate back to previous screen from the given routes
    func goBack(where routesType: RoutesType = .generalRoute) {
        switch routesType {
        case .generalRoute:
            _ = generalRouteList.popLast()
        case .homeRoute:
            _ = homeRouteList.popLast()
        case .exploreRoute:
            _ = exploreRouteList.popLast()
        case .appointmentRoute:
            _ = appointmentRouteList.popLast()
        case .myAccountRoute:
            _ = myAccountRouteList.popLast()
        }
    }

    // For to replace whole screens from given route with new screens array
    func replace(stack: [Route], where routesType: RoutesType = .generalRoute) {
        switch routesType {
        case .generalRoute:
            generalRouteList = stack
        case .homeRoute:
            homeRouteList = stack
        case .exploreRoute:
            exploreRouteList = stack
        case .appointmentRoute:
            appointmentRouteList = stack
        case .myAccountRoute:
            myAccountRouteList = stack
       
        }
    }
    
    
    // For pop back to a specific screen from given route
    func popToRout(to screen: String, where routesType: RoutesType = .generalRoute) {
        switch routesType {
        case .generalRoute:
            guard generalRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = generalRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            generalRouteList.removeLast(generalRouteList.count - (itemIndex))
        case .homeRoute:
            guard homeRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = homeRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            homeRouteList.removeLast(homeRouteList.count - (itemIndex))
        case .exploreRoute:
            guard exploreRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = exploreRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            exploreRouteList.removeLast(exploreRouteList.count - (itemIndex))
        case .appointmentRoute:
            guard appointmentRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = appointmentRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            appointmentRouteList.removeLast(appointmentRouteList.count - (itemIndex))
        case .myAccountRoute:
            guard myAccountRouteList.contains(where: { $0.rawValue == screen }) else { return printRouteException() }
            guard let index = myAccountRouteList.firstIndex(where: { $0.rawValue == screen }) else { return }
            let itemIndex = index + 1
            myAccountRouteList.removeLast(myAccountRouteList.count - (itemIndex))

        }

        func printRouteException() { print("Route not contain") }
    }
    
    
    
//
//    // For pop back to a specific screen from given route
//    func popToRout(to screen: Route, where routesType: RoutesType = .generalRoute) {
//        switch routesType {
//        case .generalRoute:
//            guard generalRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in generalRouteList.reversed().enumerated() {
//                let reverseIndex: Int = generalRouteList.count - index - 1
//                if item == screen { return }
//                generalRouteList.remove(at: reverseIndex)
//            }
//        case .homeRoute:
//            guard homeRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in homeRouteList.reversed().enumerated() {
//                let reverseIndex: Int = homeRouteList.count - index - 1
//                if item == screen { return }
//                homeRouteList.remove(at: reverseIndex)
//            }
//        case .exploreRoute:
//            guard exploreRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in exploreRouteList.reversed().enumerated() {
//                let reverseIndex: Int = exploreRouteList.count - index - 1
//                if item == screen { return }
//                exploreRouteList.remove(at: reverseIndex)
//            }
//        case .appointmentRoute:
//            guard appointmentRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in appointmentRouteList.reversed().enumerated() {
//                let reverseIndex: Int = appointmentRouteList.count - index - 1
//                if item == screen { return }
//                appointmentRouteList.remove(at: reverseIndex)
//            }
//        case .myAccountRoute:
//            guard myAccountRouteList.contains(screen) else { return printRouteException() }
//            for (index, item) in myAccountRouteList.reversed().enumerated() {
//                let reverseIndex: Int = myAccountRouteList.count - index - 1
//                if item == screen { return }
//                myAccountRouteList.remove(at: reverseIndex)
//            }
//       
//        }
//
//        func printRouteException() { print("Route not contain") }
//    }

    func popToAllRoot() {
        generalRouteList.removeAll()
        homeRouteList.removeAll()
        exploreRouteList.removeAll()
        appointmentRouteList.removeAll()
        myAccountRouteList.removeAll()
        
    }

    func popToRootTabs() {
        homeRouteList.removeAll()
        exploreRouteList.removeAll()
        appointmentRouteList.removeAll()
        myAccountRouteList.removeAll()
        
    }

    // For pop all the way back to its root view from given route
    func popToRoot(where routesType: RoutesType = .generalRoute) {
        switch routesType {
        case .generalRoute:
            generalRouteList = []
        case .homeRoute:
            homeRouteList = []
        case .exploreRoute:
            exploreRouteList = []
        case .appointmentRoute:
            appointmentRouteList = []
        case .myAccountRoute:
            myAccountRouteList = []
      
        }
    }
    
    
    func checkIfRouteContain(route:Route)-> Bool {
        homeRouteList.contains(route) ||  exploreRouteList.contains(route) || appointmentRouteList.contains(route)  || myAccountRouteList.contains(route)
    }

    // For mapping two enums: Tab -> RoutesType
    func mapTabTypeToRoutesType(from tab: Tab) -> RoutesType {
        switch tab {
        case .home:
            return RoutesType.homeRoute
        case .explore:
            return RoutesType.exploreRoute
        case .appointment:
            return RoutesType.appointmentRoute
        case .myAccount:
            return RoutesType.myAccountRoute
        }
    }
    
    
     func mapRouterWithTab(appState: AppState) -> RoutesType {
        switch appState.selectedTab {
        case .home:
            .homeRoute
        case .explore:
            .exploreRoute
        case .appointment:
            .appointmentRoute
        case .myAccount:
            .myAccountRoute
        }
    }

}

struct EnvironmentModifier: ViewModifier {
    @StateObject private var appState = AppState()
    @State private var routerManager = RouterManager()
    @StateObject private var locationManager = LocationManager()
   
    func body(content: Content) -> some View {
        content
            .environment(\.colorScheme, .light)
            .environment(routerManager)
            .environmentObject(appState)
            .environmentObject(locationManager)
            .environment(\.locale, appState.appLanguage.locale)
            .environment(\.layoutDirection, appState.appLanguage.direction)
        
    }
}

extension View {
    func attachAllEnvironmentObjects() -> some View {
        modifier(EnvironmentModifier())
    }
}
